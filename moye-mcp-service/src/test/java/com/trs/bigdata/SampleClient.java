/*
 * Copyright 2024 - 2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.trs.bigdata;

import com.trs.bigdata.pojo.FileInfo;
import io.modelcontextprotocol.client.McpClient;
import io.modelcontextprotocol.client.McpSyncClient;
import io.modelcontextprotocol.spec.McpClientTransport;
import io.modelcontextprotocol.spec.McpSchema.CallToolRequest;
import io.modelcontextprotocol.spec.McpSchema.CallToolResult;
import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public class SampleClient {

    private final McpClientTransport transport;

    public SampleClient(McpClientTransport transport) {
        this.transport = transport;
    }

    public void run() {

        var client = McpClient.sync(this.transport)
            .requestTimeout(Duration.ofSeconds(300))
            .loggingConsumer((notification) -> System.out.println(notification.data())).build();
        client.initialize();
        client.ping();

        testPoliceSituationAnalyze(client);
//        testPhoneCall(client);
//        testSearch(client);

        client.closeGracefully();
    }

    private void testPhoneCall(McpSyncClient client) {
        List<FileInfo> fileInfos = List.of(
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1廖华15883887687_238283.xls",
                "1廖华15883887687.xls"),
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1陈志刚15883402111_5a0bb6.xls",
                "1陈志刚15883402111.xls"),
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1白远鑫13689658678_deeb16.xls",
                "1白远鑫13689658678.xls"),
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1李鹏程13890251186_36de69.xls",
                "1李鹏程13890251186.xls"),
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1黄英13981057776_bc9b1c.xls",
                "1黄英13981057776.xls"),
            new FileInfo("http://192.168.210.120/llmops/appendix/upload/2025/8/25/1邓健15928311872_07e668.xls",
                "1邓健15928311872.xls"));

        CallToolResult getDoSearchResult = client.callTool(new CallToolRequest("话单分析工具",
            Map.of("excelInfos", fileInfos, "callId", "123")));
        System.out.println("获取检索: " + getDoSearchResult);
    }

    private void testSearch(McpSyncClient client) {
        CallToolResult getDoSearchResult = client.callTool(new CallToolRequest("查询数据",
            Map.of("query", "使用[数据智能体,分析数据]工具，查询最近两年内涉及洋垃圾进口的企业信息，筛选条件包括进口时间在最近两年内且商品属于洋垃圾类别，提取企业名称等关键信息。",
                "resourceUris", List.of("/moye/mcp/data-models/10600", "/moye/mcp/data-models/10601"),
                "callId", "6bfbfbb073154d33",
                "messageId", "cc4062a8-74df-4a30-ba11-2fa32c1b27a6",
                "appId", 219)));
        System.out.println("获取检索: " + getDoSearchResult);
    }

    private void testPoliceSituationAnalyze(McpSyncClient client) {
        CallToolResult getDoSearchResult = client.callTool(new CallToolRequest("警情分析工具",
            Map.of("callId", "123",
                "startTime", "2025-08-01 00:00:00",
                "endTime", "2025-08-02 23:59:59")));
        System.out.println("获取检索: " + getDoSearchResult);
    }
}
