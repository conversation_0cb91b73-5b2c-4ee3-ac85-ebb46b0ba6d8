package com.trs.bigdata.service;

import com.trs.bigdata.dao.PoliceSituationAnalyseMapper;
import io.modelcontextprotocol.server.McpSyncServerExchange;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Service;

/**
 * 警情分析工具
 */
@Service
public class PoliceSituationAnalyzer {

    @Resource
    private PoliceSituationAnalyseMapper policeSituationAnalyseMapper;

    /**
     * 警情分析工具
     *
     * @param callId    用户请求id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param exchange  用户会话
     * @return 分析结果
     */
    @Tool(name = "警情分析工具", description = "分析指定时间段内的警情数据，生成警情分析报告")
    public String analyze(
        @ToolParam(description = "用户请求id") String callId,
        @ToolParam(description = "开始时间，格式为 yyyy-MM-dd HH:mm:ss", required = false) LocalDateTime startTime,
        @ToolParam(description = "结束时间，格式为 yyyy-MM-dd HH:mm:ss", required = false) LocalDateTime endTime,
        McpSyncServerExchange exchange) {
        String startTimeStr = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String endTimeStr = endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Duration duration = Duration.between(startTime, endTime);
        StringBuilder report = new StringBuilder("\n报告分析开始：\n\n");
        report.append("警情分析报告（高新分局）\n");
        report.append(情况综述(startTime, endTime, duration, startTimeStr, endTimeStr));
        report.append(重点警情及发案(startTime, endTime, duration, startTimeStr, endTimeStr));
        report.append("\n报告分析完毕。\n\n");
        return report.toString();
    }

    private String 情况综述(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr) {
        StringBuilder report = new StringBuilder();
        final Integer 全区违法犯罪警情总数 = policeSituationAnalyseMapper.全区违法犯罪警情总数(startTime, endTime);
        final Integer 上期全区违法犯罪警情总数 = policeSituationAnalyseMapper.全区违法犯罪警情总数(
            startTime.minusSeconds(duration.toSeconds()),
            endTime.minusSeconds(duration.toSeconds()));
        report.append("一、情况综述：\n")
            .append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区接报违法犯罪警情").append(全区违法犯罪警情总数).append("件,")
            .append("环比上期").append(上期全区违法犯罪警情总数).append("件")
            .append(全区违法犯罪警情总数 > 上期全区违法犯罪警情总数 ? "上升" : "下降")
            .append(String.format("%.2f", Math.abs(全区违法犯罪警情总数 - 上期全区违法犯罪警情总数) * 100.0d / 上期全区违法犯罪警情总数))
            .append("%。\n")
            .append("其中盗窃类警情").append(policeSituationAnalyseMapper.全区盗窃类警情数量(startTime, endTime))
            .append("件,")
            .append("诈骗类警情").append(policeSituationAnalyseMapper.全区诈骗类警情数量(startTime, endTime))
            .append("件,")
            .append("非侵财类警情").append(policeSituationAnalyseMapper.全区非侵财类警情数量(startTime, endTime))
            .append("件。\n");
        return report.toString();
    }

    private String 重点警情及发案(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr) {
        return "二、重点警情及发案类型分析：\n"
            + 盗窃类警情分析(startTime, endTime, duration, startTimeStr, endTimeStr)
            + 街面六类侵财警情分析(startTime, endTime, duration, startTimeStr, endTimeStr)
            + 殴打他人警情分析(startTime, endTime, duration, startTimeStr, endTimeStr)
            + 家暴类警情分析(startTime, endTime, duration, startTimeStr, endTimeStr)
            + 热点区域警情通报(startTime, endTime, duration, startTimeStr, endTimeStr);
    }

    private String 盗窃类警情分析(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr,
        String endTimeStr) {
        StringBuilder report = new StringBuilder();
        final Integer 全区盗窃类警情数量 = policeSituationAnalyseMapper.全区盗窃类警情数量(startTime, endTime);
        final Integer 上期全区盗窃类警情数量 = policeSituationAnalyseMapper.全区盗窃类警情数量(
            startTime.minusSeconds(duration.toSeconds()),
            endTime.minusSeconds(duration.toSeconds()));
        report.append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区共接报盗窃类警情").append(全区盗窃类警情数量).append("件");
        if (全区盗窃类警情数量 > 0) {
            report.append("，");
            report.append("环比上期").append(上期全区盗窃类警情数量).append("件")
                .append(全区盗窃类警情数量 > 上期全区盗窃类警情数量 ? "上升" : "下降")
                .append(String.format("%.2f", Math.abs(全区盗窃类警情数量 - 上期全区盗窃类警情数量) * 100.0d / 上期全区盗窃类警情数量))
                .append("%。");
            final Integer 全区盗窃类警情高发时间段 = policeSituationAnalyseMapper.全区盗窃类警情高发时段(startTime,
                endTime);
            if (全区盗窃类警情高发时间段 != null && 全区盗窃类警情高发时间段 > 0) {
                report.append("盗窃类警情高发时间段为").append(全区盗窃类警情高发时间段).append("时。\n");
            }
        } else {
            report.append("。\n");
        }
        return report.toString();
    }

    private String 街面六类侵财警情分析(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr) {
        StringBuilder report = new StringBuilder();
        final Integer 全区街面六类警情数量 = policeSituationAnalyseMapper.全区街面六类警情数量(startTime, endTime);
        report.append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区共接报街面六类警情").append(全区街面六类警情数量).append("件。\n");
        return report.toString();
    }

    private String 殴打他人警情分析(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr) {
        StringBuilder report = new StringBuilder();
        final Integer 全区殴打他人警情数量 = policeSituationAnalyseMapper.全区殴打他人警情数量(startTime, endTime);
        final Integer 上期全区殴打他人警情数量 = policeSituationAnalyseMapper.全区殴打他人警情数量(
            startTime.minusSeconds(duration.toSeconds()),
            endTime.minusSeconds(duration.toSeconds()));
        report.append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区共接报殴打他人警情").append(全区殴打他人警情数量).append("件,");
        if (全区殴打他人警情数量 > 0) {
            report.append("环比上期").append(上期全区殴打他人警情数量).append("件")
                .append(全区殴打他人警情数量 > 上期全区殴打他人警情数量 ? "上升" : "下降")
                .append(String.format("%.2f", Math.abs(全区殴打他人警情数量 - 上期全区殴打他人警情数量) * 100.0d / 上期全区殴打他人警情数量))
                .append("%；\n");
        }
        return report.toString();
    }

    private String 家暴类警情分析(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr) {
        StringBuilder report = new StringBuilder();
        final Integer 全区家暴类警情数量 = policeSituationAnalyseMapper.全区家暴警情数量(startTime, endTime);
        report.append(startTimeStr).append("至").append(endTimeStr).append(" ")
            .append("全区共接报家暴类警情").append(全区家暴类警情数量).append("件");
        if (全区家暴类警情数量 > 0) {
            report.append("，");
            Map<String, Integer> 家暴类警情部门统计 = policeSituationAnalyseMapper.家暴类警情部门统计(startTime,
                endTime);
            if (家暴类警情部门统计 != null && !家暴类警情部门统计.isEmpty()) {
                String deptStats = 家暴类警情部门统计.entrySet().stream()
                    .map(entry -> entry.getKey() + "：" + entry.getValue() + "件")
                    .reduce((a, b) -> a + "，" + b)
                    .orElse("");
                report.append("其中各部门接报情况为：").append(deptStats).append("。\n");
            }
        } else {
            report.append("。\n");
        }
        return report.toString();
    }

    private String 热点区域警情通报(LocalDateTime startTime, LocalDateTime endTime, Duration duration,
        String startTimeStr, String endTimeStr) {
        StringBuilder report = new StringBuilder();
        final Integer 龙湖时代天街 = policeSituationAnalyseMapper.龙湖时代天街(startTime, endTime);
        final Integer 富士康青年公寓 = policeSituationAnalyseMapper.富士康青年公寓(startTime, endTime);
        report.append("三、热点区域警情通报：\n")
            .append("龙湖时代天街接报警情").append(龙湖时代天街).append("件，")
            .append("富士康青年公寓接报警情").append(富士康青年公寓).append("件。\n");
        return report.toString();
    }
}
