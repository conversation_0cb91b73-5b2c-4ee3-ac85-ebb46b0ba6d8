package com.trs.moye.ability.utils;

import static com.trs.moye.ability.utils.TypeConversionUtils.ifNeedJsonParse;
import static com.trs.moye.ability.utils.TypeConversionUtils.isJsonFormat;
import static com.trs.moye.ability.utils.TypeConversionUtils.isSimpleNonJsonString;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.Test;

/**
 * 类型转换测试类
 *
 * <AUTHOR>
 * @since 2025/8/29
 */
public class TypeConversionUtilsTest {


    @Test
    void testNeedJsonParse() {
        // 测试用例：有效的JSON + 需要解析的类型
        assertTrue(ifNeedJsonParse("{\"name\":\"John\"}", Map.class));
        assertTrue(ifNeedJsonParse("[\"apple\",\"banana\"]", List.class));
        assertTrue(ifNeedJsonParse("{\"age\":30}", Object.class));
        assertTrue(ifNeedJsonParse("{\"data\":[1,2,3]}", Object.class));
        assertTrue(ifNeedJsonParse("{\"a\":1}", Object.class));

        // 测试用例：有效的JSON + 不需要解析的基本类型
        assertFalse(ifNeedJsonParse("{\"name\":\"John\"}", Integer.class));
        assertFalse(ifNeedJsonParse("[\"apple\",\"banana\"]", int.class));

        // 测试用例：非JSON字符串 + 各种类型
        assertFalse(ifNeedJsonParse("hello world", String.class));
        assertFalse(ifNeedJsonParse("123", Integer.class));
        assertFalse(ifNeedJsonParse("[1,2,3]", String.class)); // 简单数组被排除

        // 测试用例：边界情况
        assertFalse(ifNeedJsonParse(null, String.class));
        assertFalse(ifNeedJsonParse("", String.class));
        assertFalse(ifNeedJsonParse("{}", Map.class)); // 空对象被排除
        assertFalse(ifNeedJsonParse("[]", List.class)); // 空数组被排除
    }

    @Test
    void testIsJsonFormat() {
        // 有效的JSON格式
        assertTrue(isJsonFormat("{\"name\":\"John\"}"));
        assertTrue(isJsonFormat("{\"name\":\"John\",\"age\":30}"));
        assertTrue(isJsonFormat("[\"apple\",\"banana\"]"));
        assertTrue(isJsonFormat("[{\"id\":1},{\"id\":2}]"));
        assertTrue(isJsonFormat("{\"a\":1}"));

        // 应该被排除的简单格式
        assertFalse(isJsonFormat("[0-9]"));
        assertFalse(isJsonFormat("[1,2,3]"));
        assertFalse(isJsonFormat("[]"));
        assertFalse(isJsonFormat("{}"));
        assertFalse(isJsonFormat("[0]"));

        // 无效的JSON格式
        assertFalse(isJsonFormat(""));
        assertFalse(isJsonFormat(null));
        assertFalse(isJsonFormat("{"));
        assertFalse(isJsonFormat("["));
        assertFalse(isJsonFormat("{\"name\":\"John\""));
    }

    @Test
    void testIsSimpleNonJsonString() {
        // 应该返回true的情况（简单非JSON）
        assertTrue(isSimpleNonJsonString("{}"));
        assertTrue(isSimpleNonJsonString("[]"));
        assertTrue(isSimpleNonJsonString("[1,2,3]"));
        assertTrue(isSimpleNonJsonString("[0]"));
        assertTrue(isSimpleNonJsonString("[123]"));

        // 应该返回false的情况（复杂JSON）
        assertFalse(isSimpleNonJsonString("{\"key\":123}"));
        assertFalse(isSimpleNonJsonString("{\"a\":1}"));
        assertFalse(isSimpleNonJsonString("{\"name\":\"John\"}"));
        assertFalse(isSimpleNonJsonString("[\"apple\",\"banana\"]"));
        assertFalse(isSimpleNonJsonString("{\"data\":[1,2,3]}"));
        assertFalse(isSimpleNonJsonString("{\"person\":{\"name\":\"John\"}}"));
    }

    @Test
    void testEdgeCases() {
        // 测试带空格的JSON
        assertTrue(isJsonFormat(" {\"name\":\"John\"} "));
        assertTrue(isJsonFormat("\n{\"name\":\"John\"}\n"));

        // 测试嵌套结构
        assertTrue(isJsonFormat("{\"data\":{\"nested\":\"value\"}}"));
        assertTrue(isJsonFormat("{\"array\":[{\"obj\":1},{\"obj\":2}]}"));

        // 测试转义字符
        assertTrue(isJsonFormat("{\"escaped\":\"quote\\\"test\"}"));
    }

    @Test
    void testVariousTargetTypes() {
        String json = "{\"name\":\"John\"}";

        // 数组类型
        assertTrue(ifNeedJsonParse(json, String[].class));
        assertTrue(ifNeedJsonParse(json, Object[].class));

        // 集合类型
        assertTrue(ifNeedJsonParse(json, Collection.class));
        assertTrue(ifNeedJsonParse(json, List.class));
        assertTrue(ifNeedJsonParse(json, Set.class));
        assertTrue(ifNeedJsonParse(json, ArrayList.class));

        // Map类型
        assertTrue(ifNeedJsonParse(json, Map.class));
        assertTrue(ifNeedJsonParse(json, HashMap.class));

        // 自定义对象类型
        assertTrue(ifNeedJsonParse(json, Object.class));

        // 基本类型（不应该解析）
        assertFalse(ifNeedJsonParse(json, int.class));
        assertFalse(ifNeedJsonParse(json, Integer.class));
        assertFalse(ifNeedJsonParse(json, boolean.class));
    }
}
