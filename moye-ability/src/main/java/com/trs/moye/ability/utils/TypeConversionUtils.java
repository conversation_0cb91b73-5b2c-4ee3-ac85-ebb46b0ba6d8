package com.trs.moye.ability.utils;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.moye.base.common.utils.JsonUtils;
import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * 类型转换工具类，支持基础类型及包装类的自动转换
 */
@Slf4j
public class TypeConversionUtils {

    /**
     * 将输入值转换为目标类型
     *
     * @param inputValue 输入值（可为String、JsonNode或原始类型）
     * @param targetType 目标类型（如Integer.class, Boolean.class, String.class等）
     * @return 转换后的目标类型对象，转换失败返回null
     */
    public static Object convert(Object inputValue, Type targetType) {
        if (inputValue == null) {
            return null;
        }

        // 若类型已匹配，直接返回
        if (isTypeMatch(inputValue.getClass(), targetType)) {
            return inputValue;
        }

        // 统一将输入值转为字符串处理（JsonNode需先转为文本）
        String valueStr = toString(inputValue);
        if (StringUtils.isBlank(valueStr)) {
            return null;
        }

        // 根据目标类型进行转换
        Class<?> targetClass = (Class<?>) targetType;
        if (targetClass == String.class) {
            return valueStr;
        } else if (targetClass == Integer.class || targetClass == int.class) {
            return NumberUtils.toInt(valueStr, 0);
        } else if (targetClass == Long.class || targetClass == long.class) {
            return NumberUtils.toLong(valueStr, 0L);
        } else if (targetClass == Boolean.class || targetClass == boolean.class) {
            return "true".equalsIgnoreCase(valueStr) || "1".equals(valueStr);
        } else if (targetClass == Double.class || targetClass == double.class) {
            return NumberUtils.toDouble(valueStr, 0.0);
        } else if (targetClass == Float.class || targetClass == float.class) {
            return NumberUtils.toFloat(valueStr, 0.0f);
        } else {
            // 复杂类型（如自定义对象）使用JsonUtils转换
            return JsonUtils.parseObject(valueStr, targetClass);
        }
    }

    /**
     * 判断输入值类型是否与目标类型匹配
     *
     * @param inputClass 输入值类型
     * @param targetType 目标类型
     * @return true:匹配 false:不匹配
     */
    private static boolean isTypeMatch(Class<?> inputClass, Type targetType) {
        if (targetType instanceof Class<?>) {
            Class<?> targetClass = (Class<?>) targetType;
            // 基础类型与包装类匹配（如int和Integer）
            if (targetClass.isPrimitive()) {
                return getWrapperClass(targetClass).isAssignableFrom(inputClass);
            } else {
                return targetClass.isAssignableFrom(inputClass);
            }
        }
        return false;
    }

    /**
     * 将输入值转为字符串（处理JsonNode等特殊类型）
     *
     * @param inputValue 输入值
     * @return 字符串
     */
    private static String toString(Object inputValue) {
        if (inputValue instanceof String) {
            return (String) inputValue;
        } else if (inputValue instanceof JsonNode) {
            return ((JsonNode) inputValue).asText();
        } else {
            return inputValue.toString();
        }
    }

    /**
     * 获取基础类型对应的包装类
     *
     * @param primitiveClass 基础类型
     * @return 包装类
     */
    private static Class<?> getWrapperClass(Class<?> primitiveClass) {
        if (primitiveClass == int.class) return Integer.class;
        if (primitiveClass == long.class) return Long.class;
        if (primitiveClass == boolean.class) return Boolean.class;
        if (primitiveClass == double.class) return Double.class;
        if (primitiveClass == float.class) return Float.class;
        if (primitiveClass == short.class) return Short.class;
        if (primitiveClass == byte.class) return Byte.class;
        if (primitiveClass == char.class) return Character.class;
        return primitiveClass;
    }

    /**
     * 判断是否为基础数据类型
     * <p>基础类型包括：原生类型、包装类、String、LocalDate、LocalDateTime</p>
     *
     * @param clazz 待判断的类
     * @return true:基础类型 false:非基础类型
     */
    private static boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() // 原生类型（int、boolean等）
                || clazz == String.class
                || clazz == Integer.class || clazz == Boolean.class || clazz == Long.class
                || clazz == Float.class || clazz == Double.class || clazz == Byte.class
                || clazz == Short.class || clazz == Character.class
                || clazz == LocalDate.class || clazz == LocalDateTime.class;
    }

    /**
     * 解析paramExample为目标类型示例值
     *
     * @param paramExample 字符串格式的示例值（如"100"、"true"）
     * @param targetType   目标参数类型（如Integer.class）
     * @return 转换后的示例值对象
     */
    public static Object parseParamExample(String paramExample, Class<?> targetType) {
        if (StringUtils.isBlank(paramExample)) {
            return null;
        }
        // 特殊处理JSON格式的示例值（数组/对象）：
        boolean needJsonParse = ifNeedJsonParse(paramExample, targetType);

        if (needJsonParse) {
            try {
                // 尝试JSON解析，失败则降级为普通字符串处理
                Class<?> parseType = (targetType == String.class) ? Object.class : targetType;
                return JsonUtils.parseObject(paramExample, parseType);
            } catch (Exception e) {
                log.error("ParamExample is not valid JSON, treat as regular string: {}", paramExample, e);
            }
        }
        // 基础类型直接转换（包含JSON解析失败的降级处理）
        return convert(paramExample, targetType);
    }

    /**
     * 是否需要json解析
     *
     * @param paramExample 参数示例
     * @param targetType   类型
     * @return 是否需要json解析
     */
    public static boolean ifNeedJsonParse(String paramExample, Class<?> targetType) {
        if (paramExample == null) {
            return false;
        }
        String trimmed = paramExample.trim();
        boolean isJson = isJsonFormat(trimmed);
        // 不需要JSON解析的情况
        if (!isJson) {
            return false;
        }
        // 需要JSON解析的情况
        return targetType.isArray() || Collection.class.isAssignableFrom(targetType) || Map.class.isAssignableFrom(
            targetType) || !isBasicType(targetType);
    }

    /**
     * 是否是json
     *
     * @param jsonString 字符串
     * @return 是否是json
     */
    public static boolean isJsonFormat(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return false;
        }
        String trimmed = jsonString.trim();
        // 基本长度和格式检查
        if (trimmed.length() < 2) {
            return false;
        }
        char firstChar = trimmed.charAt(0);
        char lastChar = trimmed.charAt(trimmed.length() - 1);
        // 必须是 { } 或 [ ] 配对
        boolean hasValidBraces = (firstChar == '{' && lastChar == '}')
            || (firstChar == '[' && lastChar == ']');
        if (!hasValidBraces) {
            return false;
        }
        // 排除明显不是JSON的简单字符串
        return !isSimpleNonJsonString(trimmed);
    }

    /**
     * 排除简单json字符串
     *
     * @param str 字符串
     * @return 是否是json
     */
    public static boolean isSimpleNonJsonString(String str) {
        // 排除空对象和空数组
        if (str.equals("{}") || str.equals("[]")) {
            return true;
        }
        // 排除纯数字数组（如 [1,2,3], [123]）
        if (isPureNumberArray(str)) {
            return true;
        }
        // 排除正则表达式字符集模式
        return isRegexCharacterSet(str);

    }

    /**
     * 判断是否是纯数字数组
     *
     * @param str 字符串
     * @return 是否是纯数字数组
     */
    private static boolean isPureNumberArray(String str) {
        if (!str.startsWith("[") || !str.endsWith("]")) {
            return false;
        }
        String inner = str.substring(1, str.length() - 1).trim();
        // 空数组已经在前面处理了，这里处理有内容的
        if (inner.isEmpty()) {
            return false;
        }
        // 匹配纯数字、逗号、空格的模式
        return inner.matches("[0-9,\\s]+");
    }

    /**
     * 判断是否是正则表达式字符集（如 [0-9], [a-z]）
     *
     * @param str 字符串
     * @return 是否是正则表达式
     */
    private static boolean isRegexCharacterSet(String str) {
        if (!str.startsWith("[") || !str.endsWith("]")) {
            return false;
        }
        String inner = str.substring(1, str.length() - 1).trim();
        // 匹配字符范围模式：0-9, a-z, A-Z 等
        return inner.matches("[a-zA-Z0-9]-[a-zA-Z0-9]");
    }
}