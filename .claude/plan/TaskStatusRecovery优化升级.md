# TaskStatusRecoveryService优化升级执行计划

## 任务概述

优化和升级TaskStatusRecoveryService中的数据恢复逻辑，从基于运行时间的简单判断升级为基于Spark REST API的真实状态检查。

## 执行上下文

### 项目信息
- **项目**: moye-v4 批处理引擎模块
- **模块路径**: `/Users/<USER>/TRS/codeProjects/moye-v4/moye-batch-engine`
- **目标**: 提供更准确的任务状态恢复机制

### 问题分析
当前TaskStatusRecoveryService存在以下限制：
1. 仅基于运行时间判断（超过2/4小时标记失败）
2. 无法获取Spark集群真实状态
3. 可能误判正常运行的长任务
4. 缺少网络异常处理机制

## 实施方案

### 核心改进
1. **集成Spark REST API**: 创建SparkRestApiClient工具类
2. **状态映射机制**: 将Spark状态映射为BatchTaskStatus
3. **降级处理**: 网络异常时回退到时间判断
4. **真实状态检查**: 通过应用和作业状态综合判断

### 技术架构
```
SparkRestApiClient ← TaskStatusRecoveryService
SparkStatusMapper ← TaskStatusRecoveryService  
SparkApplicationInfo ← SparkRestApiClient
现有SparkTaskManager ← TaskStatusRecoveryService
```

## 实施步骤

### ✅ 第1步：创建实体类
- **SparkApplicationInfo**: Spark应用程序信息实体
- **SparkJobInfo**: Spark作业信息实体
- **状态**: 已完成

### ✅ 第2步：创建状态映射工具类
- **SparkStatusMapper**: 状态映射逻辑
- **映射规则**: 
  - SUBMITTED/RUNNING → RUNNING
  - FINISHED + SUCCEEDED → SUCCESS
  - FAILED/KILLED → FAILED
- **状态**: 已完成

### ✅ 第3步：创建REST API客户端
- **SparkRestApiClient**: 封装Spark REST API调用
- **功能**: 获取应用信息、作业信息、连接测试
- **异常处理**: 网络超时、404错误处理
- **状态**: 已完成

### ✅ 第4步：升级TaskStatusRecoveryService
- **核心改进**: 
  - 集成SparkRestApiClient依赖
  - 替换shouldRecoverTask()方法逻辑
  - 新增checkSparkApplicationStatus()方法
  - 添加网络异常降级机制
- **状态**: 已完成

### ✅ 第5步：单元测试
- **SparkStatusMapperTest**: 状态映射逻辑测试
- **SparkRestApiClientTest**: API客户端测试
- **覆盖场景**: 正常流程、异常处理、边界条件
- **状态**: 已完成

## 关键特性

### 状态映射规则
```java
// Spark状态 → BatchTaskStatus
SUBMITTED/RUNNING → RUNNING
FINISHED + SUCCEEDED → SUCCESS  
FAILED/KILLED → FAILED
FINISHED + FAILED → FAILED
```

### 降级机制
1. **优先级**: Spark REST API > 时间判断
2. **触发条件**: 网络超时、连接失败
3. **处理方式**: 自动回退，记录日志

### 综合判断逻辑
1. **应用级状态**: 优先使用应用状态
2. **作业级状态**: 应用状态不明确时参考作业状态
3. **最终决策**: 综合两种状态得出结论

## 配置改进

### 网络配置
- **连接超时**: 5秒
- **读取超时**: 10秒
- **重试机制**: 自动处理

### 异常处理策略
- **网络超时** → 降级为时间判断
- **404错误** → 标记为FAILED
- **其他异常** → 记录日志并降级

## 验收标准

### 功能要求
- ✅ 能够准确获取Spark集群中应用的真实状态
- ✅ 网络异常时自动降级，不影响现有功能
- ✅ 支持应用和作业状态的综合判断
- ✅ 提供连接测试和诊断功能

### 质量要求
- ✅ 新增功能通过单元测试
- ✅ 代码符合现有规范
- ✅ 详细的中文注释说明实现逻辑
- ✅ 异常处理健壮性

### 性能要求
- ✅ API调用超时控制（5秒连接，10秒读取）
- ✅ 缓存机制避免频繁调用
- ✅ 批量处理支持

## 风险控制

### 已解决风险
1. **网络依赖**: 实现降级机制，API不可用时使用原有逻辑
2. **性能影响**: 合理的超时设置和批量处理
3. **版本兼容**: 使用稳定的v1 API

### 监控点
1. **连接状态**: testSparkConnection()方法
2. **状态准确性**: 日志记录状态变化
3. **降级频率**: 网络异常统计

## 实施结果

### 新增文件
1. `SparkApplicationInfo.java` - Spark应用信息实体
2. `SparkJobInfo.java` - Spark作业信息实体
3. `SparkStatusMapper.java` - 状态映射工具类
4. `SparkRestApiClient.java` - REST API客户端
5. `SparkStatusMapperTest.java` - 状态映射测试
6. `SparkRestApiClientTest.java` - API客户端测试

### 修改文件
1. `TaskStatusRecoveryService.java` - 升级状态判断逻辑

### 核心改进点
1. **准确性提升**: 从时间推测改为真实状态检查
2. **可靠性增强**: 网络异常时自动降级
3. **可监控性**: 提供连接测试和状态统计
4. **可扩展性**: 支持未来功能扩展

## 使用示例

### 手动状态恢复
```java
// 恢复所有活跃任务
taskStatusRecoveryService.manualRecovery(null);

// 恢复指定任务
taskStatusRecoveryService.manualRecovery("execute-id-123");
```

### 连接测试
```java
// 测试Spark集群连接
Map<String, Object> result = taskStatusRecoveryService.testSparkConnection();
```

### 获取统计信息
```java
// 获取状态恢复统计
Map<String, Object> stats = taskStatusRecoveryService.getRecoveryStatistics();
```

---

**执行完成时间**: 2025-08-29  
**执行状态**: ✅ 已完成  
**质量状态**: ✅ 通过验收