# Spark状态获取多模式配置说明

## 配置文件位置
`moye-batch-engine/src/main/resources/application-*.properties`

## 核心配置项

### 1. 基础配置
```properties
# 默认部署模式 (yarn/standalone/local)
spark.deploy.mode=standalone

# 强制使用特定Provider (可选，用于测试和特殊场景)
spark.status.provider.force=

# Provider检测间隔（秒）
spark.status.provider.detect-interval=300
```

### 2. Standalone模式配置
```properties
# Spark Master Web UI URL (继续使用现有配置)
moye.batch.spark.webUiUrl=http://spark-master:8080
```

### 3. Yarn模式配置
```properties
# Yarn ResourceManager Web UI URL
yarn.resource-manager.web-ui-url=http://resource-manager:8088
```

### 4. 连接配置
```properties
# 连接超时时间（秒）
spark.status.connect-timeout-seconds=5
# 读取超时时间（秒）  
spark.status.read-timeout-seconds=10
# 重试次数
spark.status.retry-count=3

# 缓存配置
spark.status.cache.enabled=true
spark.status.cache.expiry-minutes=5
```

## 环境配置示例

### 开发环境 (application-local.properties)
```properties
# 本地开发，使用Standalone模式
spark.deploy.mode=standalone
moye.batch.spark.webUiUrl=http://localhost:8080
spark.status.cache.enabled=true
```

### 测试环境 (application-test.properties)
```properties
# 测试环境，使用Yarn模式
spark.deploy.mode=yarn
yarn.resource-manager.web-ui-url=http://test-rm:8088
moye.batch.spark.webUiUrl=http://test-spark:8080
spark.status.provider.detect-interval=60
```

### 生产环境 (application-prod.properties)
```properties
# 生产环境，使用Yarn模式
spark.deploy.mode=yarn
yarn.resource-manager.web-ui-url=http://prod-rm:8088
spark.status.cache.enabled=true
spark.status.cache.expiry-minutes=10
spark.status.retry-count=5
```

## 多模式自动切换

系统会根据以下优先级自动选择Provider：

1. **强制指定**: 如果配置了 `spark.status.provider.force`，直接使用指定的Provider
2. **自动检测**: 系统会测试所有Provider的连通性，选择响应时间最短的可用Provider
3. **默认回退**: 如果自动检测失败，使用 `spark.deploy.mode` 对应的Provider
4. **最终回退**: 如果以上都失败，使用Standalone Provider

## Provider支持的模式

| Provider | 支持的deploy.mode |
|----------|------------------|
| **StandaloneSparkStatusProvider** | standalone, spark-standalone |
| **YarnSparkStatusProvider** | yarn, yarn-cluster, yarn-client |
| **LocalSparkStatusProvider** | local, local[*], local[1], local[2], local[4] |

## 监控和调试

### 查看当前Provider状态
通过 `TaskStatusRecoveryService.testSparkConnection()` 方法可以获取：
- 当前活跃的Provider
- 各Provider的可用性状态
- 响应时间统计
- 连接测试结果

### 手动清理缓存
调用 `SparkStatusClient.cleanupCaches()` 方法可以手动清理所有缓存

### 日志配置
```properties
# 开启详细日志用于调试
logging.level.com.trs.moye.batch.engine.client=DEBUG
```

## 迁移指南

### 从SparkRestApiClient迁移
原有的SparkRestApiClient已被替换为SparkStatusClient，API保持兼容：

```java
// 原来的用法
@Resource
private SparkRestApiClient sparkRestApiClient;
SparkApplicationInfo info = sparkRestApiClient.getApplicationInfo(appId);

// 新的用法
@Resource  
private SparkStatusClient sparkStatusClient;
SparkApplicationInfo info = sparkStatusClient.getApplicationInfo(appId);
```

### 配置迁移
- 保留现有的 `moye.batch.spark.webUiUrl` 配置（用于Standalone模式）
- 添加 `yarn.resource-manager.web-ui-url` 配置（用于Yarn模式）
- 可选添加新的调优配置项

## 故障排查

### 常见问题
1. **Provider不可用**: 检查对应的Web UI URL是否可访问
2. **自动切换失败**: 检查网络连接和防火墙设置
3. **状态不准确**: 清理缓存或调整缓存过期时间
4. **性能问题**: 调整检测间隔和超时时间

### 调试步骤
1. 检查日志中的Provider初始化信息
2. 调用连接测试方法验证各Provider状态
3. 检查配置文件中的URL和参数设置
4. 使用DEBUG级别日志查看详细执行过程