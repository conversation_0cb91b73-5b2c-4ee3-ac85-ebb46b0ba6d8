package com.trs.moye.batch.engine.utils;

import static org.junit.jupiter.api.Assertions.*;

import com.trs.moye.batch.engine.entity.SparkApplicationInfo;
import com.trs.moye.batch.engine.entity.SparkJobInfo;
import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * Spark状态映射工具类测试
 *
 * <AUTHOR>
 * @since 2025/08/29
 */
@DisplayName("Spark状态映射工具类测试")
class SparkStatusMapperTest {

    @Test
    @DisplayName("测试应用状态映射到BatchTaskStatus")
    void testMapApplicationStatusToBatchTaskStatus() {
        // 测试运行状态
        SparkApplicationInfo runningApp = new SparkApplicationInfo();
        runningApp.setState("RUNNING");
        assertEquals(BatchTaskStatus.RUNNING, 
                    SparkStatusMapper.mapApplicationStatusToBatchTaskStatus(runningApp));

        // 测试提交状态
        SparkApplicationInfo submittedApp = new SparkApplicationInfo();
        submittedApp.setState("SUBMITTED");
        assertEquals(BatchTaskStatus.RUNNING, 
                    SparkStatusMapper.mapApplicationStatusToBatchTaskStatus(submittedApp));

        // 测试成功完成状态
        SparkApplicationInfo successApp = new SparkApplicationInfo();
        successApp.setState("FINISHED");
        successApp.setFinalState("SUCCEEDED");
        assertEquals(BatchTaskStatus.SUCCESS, 
                    SparkStatusMapper.mapApplicationStatusToBatchTaskStatus(successApp));

        // 测试失败状态
        SparkApplicationInfo failedApp = new SparkApplicationInfo();
        failedApp.setState("FAILED");
        assertEquals(BatchTaskStatus.FAILED, 
                    SparkStatusMapper.mapApplicationStatusToBatchTaskStatus(failedApp));

        // 测试被杀死状态
        SparkApplicationInfo killedApp = new SparkApplicationInfo();
        killedApp.setState("KILLED");
        assertEquals(BatchTaskStatus.FAILED, 
                    SparkStatusMapper.mapApplicationStatusToBatchTaskStatus(killedApp));

        // 测试空值情况
        assertEquals(BatchTaskStatus.FAILED, 
                    SparkStatusMapper.mapApplicationStatusToBatchTaskStatus(null));

        System.out.println("应用状态映射测试通过");
    }

    @Test
    @DisplayName("测试根据作业状态确定整体状态")
    void testDetermineStatusFromJobs() {
        // 测试空作业列表
        assertEquals(BatchTaskStatus.RUNNING, 
                    SparkStatusMapper.determineStatusFromJobs(null));
        assertEquals(BatchTaskStatus.RUNNING, 
                    SparkStatusMapper.determineStatusFromJobs(Collections.emptyList()));

        // 测试有运行中作业
        SparkJobInfo runningJob = new SparkJobInfo();
        runningJob.setStatus("RUNNING");
        SparkJobInfo successJob = new SparkJobInfo();
        successJob.setStatus("SUCCEEDED");
        
        assertEquals(BatchTaskStatus.RUNNING, 
                    SparkStatusMapper.determineStatusFromJobs(Arrays.asList(runningJob, successJob)));

        // 测试有失败作业
        SparkJobInfo failedJob = new SparkJobInfo();
        failedJob.setStatus("FAILED");
        
        assertEquals(BatchTaskStatus.FAILED, 
                    SparkStatusMapper.determineStatusFromJobs(Arrays.asList(failedJob, successJob)));

        // 测试所有作业成功
        SparkJobInfo successJob2 = new SparkJobInfo();
        successJob2.setStatus("SUCCEEDED");
        
        assertEquals(BatchTaskStatus.SUCCESS, 
                    SparkStatusMapper.determineStatusFromJobs(Arrays.asList(successJob, successJob2)));

        System.out.println("作业状态综合判断测试通过");
    }

    @Test
    @DisplayName("测试综合状态判断")
    void testDetermineOverallStatus() {
        // 测试应用信息为空
        SparkJobInfo successJob = new SparkJobInfo();
        successJob.setStatus("SUCCEEDED");
        
        assertEquals(BatchTaskStatus.SUCCESS, 
                    SparkStatusMapper.determineOverallStatus(null, Arrays.asList(successJob)));

        // 测试应用状态明确为成功
        SparkApplicationInfo successApp = new SparkApplicationInfo();
        successApp.setState("FINISHED");
        successApp.setFinalState("SUCCEEDED");
        
        assertEquals(BatchTaskStatus.SUCCESS, 
                    SparkStatusMapper.determineOverallStatus(successApp, null));

        // 测试应用运行中，但作业已完成
        SparkApplicationInfo runningApp = new SparkApplicationInfo();
        runningApp.setState("RUNNING");
        
        SparkJobInfo failedJob = new SparkJobInfo();
        failedJob.setStatus("FAILED");
        
        assertEquals(BatchTaskStatus.FAILED, 
                    SparkStatusMapper.determineOverallStatus(runningApp, Arrays.asList(failedJob)));

        System.out.println("综合状态判断测试通过");
    }

    @Test
    @DisplayName("测试是否需要状态恢复判断")
    void testNeedsStatusRecovery() {
        // 测试应用不存在
        assertTrue(SparkStatusMapper.needsStatusRecovery(null));

        // 测试应用运行中
        SparkApplicationInfo runningApp = new SparkApplicationInfo();
        runningApp.setState("RUNNING");
        assertFalse(SparkStatusMapper.needsStatusRecovery(runningApp));

        // 测试应用已完成
        SparkApplicationInfo finishedApp = new SparkApplicationInfo();
        finishedApp.setState("FINISHED");
        assertTrue(SparkStatusMapper.needsStatusRecovery(finishedApp));

        // 测试应用失败
        SparkApplicationInfo failedApp = new SparkApplicationInfo();
        failedApp.setState("FAILED");
        assertTrue(SparkStatusMapper.needsStatusRecovery(failedApp));

        System.out.println("状态恢复判断测试通过");
    }

    @Test
    @DisplayName("测试边界条件和异常处理")
    void testEdgeCasesAndExceptionHandling() {
        // 测试未知状态
        SparkApplicationInfo unknownApp = new SparkApplicationInfo();
        unknownApp.setState("UNKNOWN_STATE");
        assertEquals(BatchTaskStatus.RUNNING, 
                    SparkStatusMapper.mapApplicationStatusToBatchTaskStatus(unknownApp));

        // 测试部分成功的作业列表
        SparkJobInfo successJob = new SparkJobInfo();
        successJob.setStatus("SUCCEEDED");
        SparkJobInfo unknownJob = new SparkJobInfo();
        unknownJob.setStatus("UNKNOWN");
        
        // 部分成功应该返回RUNNING等待进一步监控
        assertEquals(BatchTaskStatus.RUNNING, 
                    SparkStatusMapper.determineStatusFromJobs(Arrays.asList(successJob, unknownJob)));

        System.out.println("边界条件测试通过");
    }
}