package com.trs.moye.batch.engine.client;

import com.trs.moye.batch.engine.entity.SparkApplicationInfo;
import com.trs.moye.batch.engine.entity.SparkJobInfo;
import com.trs.moye.batch.engine.spark.config.SparkApplicationConfig;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Spark状态客户端
 * 统一的Spark应用状态获取入口，支持多种部署模式的自动检测和切换
 * 
 * 核心特性：
 * 1. 多模式支持 - 自动检测Yarn、Standalone、Local模式
 * 2. 智能路由 - 根据部署模式自动选择合适的Provider
 * 3. 降级处理 - 当主要Provider不可用时自动降级
 * 4. 统一接口 - 对外提供统一的API接口，隐藏底层复杂性
 * 5. 性能优化 - Provider缓存和定时清理机制
 * 6. 监控支持 - 详细的操作日志和性能监控
 * 
 * 配置示例：
 * <pre>
 * # 默认部署模式
 * spark.deploy.mode=standalone
 * 
 * # 强制使用特定Provider
 * spark.status.provider.force=yarn
 * 
 * # Provider检测间隔（秒）
 * spark.status.provider.detect-interval=300
 * </pre>
 * 
 * <AUTHOR>
 * @since 2025/08/29
 */
@Slf4j
@Component
public class SparkStatusClient {
    
    // === 依赖注入 ===
    
    @Resource
    private StandaloneSparkStatusProvider standaloneProvider;
    
    @Resource
    private YarnSparkStatusProvider yarnProvider;
    
    @Resource
    private LocalSparkStatusProvider localProvider;
    
    @Resource
    private SparkApplicationConfig sparkApplicationConfig;
    
    // === 配置参数 ===
    
    /**
     * 默认部署模式，从配置文件读取
     */
    @Value("${spark.deploy.mode:standalone}")
    private String defaultDeployMode;
    
    /**
     * 强制使用特定Provider，用于测试和特殊场景
     */
    @Value("${spark.status.provider.force:}")
    private String forceProvider;
    
    /**
     * Provider检测间隔（秒），定期检测最佳Provider
     */
    @Value("${spark.status.provider.detect-interval:300}")
    private int providerDetectIntervalSeconds;
    
    // === 运行时状态 ===
    
    /**
     * 当前活跃的Provider
     */
    private volatile SparkStatusProvider activeProvider;
    
    /**
     * Provider可用性缓存，避免频繁检测
     */
    private final ConcurrentMap<String, ProviderStatus> providerStatusCache = new ConcurrentHashMap<>();
    
    /**
     * Provider状态信息
     */
    private static class ProviderStatus {
        private final boolean available;
        private final LocalDateTime lastCheckTime;
        private final long responseTimeMs;
        
        ProviderStatus(boolean available, long responseTimeMs) {
            this.available = available;
            this.lastCheckTime = LocalDateTime.now();
            this.responseTimeMs = responseTimeMs;
        }
        
        boolean isExpired(int expirySeconds) {
            return LocalDateTime.now().isAfter(lastCheckTime.plusSeconds(expirySeconds));
        }
    }
    
    /**
     * 初始化方法，确定最佳的Provider
     */
    @PostConstruct
    public void initialize() {
        log.info("初始化SparkStatusClient: defaultMode={}, forceProvider={}", 
                defaultDeployMode, forceProvider);
        
        // 如果强制指定了Provider，直接使用
        if (forceProvider != null && !forceProvider.trim().isEmpty()) {
            activeProvider = getProviderByName(forceProvider.trim());
            if (activeProvider != null) {
                log.info("使用强制指定的Provider: {}", activeProvider.getProviderName());
                return;
            } else {
                log.warn("强制指定的Provider不存在: {}", forceProvider);
            }
        }
        
        // 自动检测最佳Provider
        activeProvider = detectBestProvider();
        if (activeProvider != null) {
            log.info("自动检测到最佳Provider: {}", activeProvider.getProviderName());
        } else {
            // 回退到默认Provider
            activeProvider = getProviderByDeployMode(defaultDeployMode);
            log.warn("无法检测到可用Provider，使用默认Provider: {}", 
                    activeProvider != null ? activeProvider.getProviderName() : "null");
        }
    }
    
    /**
     * 获取Spark应用程序的详细信息
     * 
     * @param applicationId Spark应用程序ID
     * @return Spark应用程序信息，如果应用不存在或获取失败则返回null
     */
    public SparkApplicationInfo getApplicationInfo(String applicationId) {
        SparkStatusProvider provider = getCurrentProvider();
        if (provider == null) {
            log.error("没有可用的SparkStatusProvider");
            return null;
        }
        
        long startTime = System.currentTimeMillis();
        try {
            SparkApplicationInfo result = provider.getApplicationInfo(applicationId);
            long duration = System.currentTimeMillis() - startTime;
            
            log.debug("获取应用信息完成: appId={}, provider={}, duration={}ms, result={}", 
                     applicationId, provider.getProviderName(), duration, result != null ? "成功" : "失败");
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("获取应用信息异常: appId={}, provider={}, duration={}ms", 
                     applicationId, provider.getProviderName(), duration, e);
            
            // 尝试使用备用Provider
            return tryFallbackProvider(applicationId, "getApplicationInfo");
        }
    }
    
    /**
     * 获取Spark应用程序的所有作业信息
     * 
     * @param applicationId Spark应用程序ID
     * @return Spark作业信息列表，如果获取失败则返回空列表
     */
    public List<SparkJobInfo> getApplicationJobs(String applicationId) {
        SparkStatusProvider provider = getCurrentProvider();
        if (provider == null) {
            log.error("没有可用的SparkStatusProvider");
            return List.of();
        }
        
        long startTime = System.currentTimeMillis();
        try {
            List<SparkJobInfo> result = provider.getApplicationJobs(applicationId);
            long duration = System.currentTimeMillis() - startTime;
            
            log.debug("获取作业信息完成: appId={}, provider={}, duration={}ms, jobCount={}", 
                     applicationId, provider.getProviderName(), duration, result.size());
            
            return result;
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("获取作业信息异常: appId={}, provider={}, duration={}ms", 
                     applicationId, provider.getProviderName(), duration, e);
            
            return List.of();
        }
    }
    
    /**
     * 检查Spark应用程序是否存在
     * 
     * @param applicationId Spark应用程序ID
     * @return true表示应用存在，false表示应用不存在或无法访问
     */
    public boolean isApplicationExists(String applicationId) {
        SparkStatusProvider provider = getCurrentProvider();
        if (provider == null) {
            log.error("没有可用的SparkStatusProvider");
            return false;
        }
        
        try {
            boolean result = provider.isApplicationExists(applicationId);
            log.debug("检查应用存在性: appId={}, provider={}, exists={}", 
                     applicationId, provider.getProviderName(), result);
            return result;
            
        } catch (Exception e) {
            log.error("检查应用存在性异常: appId={}, provider={}", 
                     applicationId, provider.getProviderName(), e);
            return false;
        }
    }
    
    /**
     * 获取应用程序的简单状态字符串
     * 
     * @param applicationId Spark应用程序ID
     * @return 应用状态字符串，如果获取失败则返回null
     */
    public String getApplicationStatus(String applicationId) {
        SparkApplicationInfo applicationInfo = getApplicationInfo(applicationId);
        if (applicationInfo != null) {
            String status = applicationInfo.getState();
            log.debug("获取应用状态: appId={}, status={}", applicationId, status);
            return status;
        }
        return null;
    }
    
    /**
     * 测试与当前Spark集群的连接
     * 
     * @return true表示连接正常，false表示连接失败
     */
    public boolean testConnection() {
        SparkStatusProvider provider = getCurrentProvider();
        if (provider == null) {
            log.error("没有可用的SparkStatusProvider进行连接测试");
            return false;
        }
        
        try {
            boolean result = provider.testConnection();
            log.debug("连接测试: provider={}, result={}", provider.getProviderName(), result);
            return result;
            
        } catch (Exception e) {
            log.error("连接测试异常: provider={}", provider.getProviderName(), e);
            return false;
        }
    }
    
    /**
     * 定时检测最佳Provider
     * 每5分钟检测一次，确保使用最优的Provider
     */
    @Scheduled(fixedRateString = "#{${spark.status.provider.detect-interval:300} * 1000}")
    public void detectBestProviderPeriodically() {
        if (forceProvider != null && !forceProvider.trim().isEmpty()) {
            // 如果强制指定了Provider，不进行自动检测
            return;
        }
        
        try {
            SparkStatusProvider bestProvider = detectBestProvider();
            if (bestProvider != null && bestProvider != activeProvider) {
                log.info("检测到更好的Provider，切换: {} -> {}", 
                        activeProvider != null ? activeProvider.getProviderName() : "null",
                        bestProvider.getProviderName());
                activeProvider = bestProvider;
            }
            
            // 清理过期的状态缓存
            cleanupExpiredProviderStatus();
            
        } catch (Exception e) {
            log.error("定时Provider检测失败", e);
        }
    }
    
    /**
     * 手动清理缓存
     */
    public void cleanupCaches() {
        // 清理Provider状态缓存
        cleanupExpiredProviderStatus();
        
        // 清理各Provider的内部缓存
        if (standaloneProvider != null) {
            standaloneProvider.cleanupExpiredCache();
        }
        if (yarnProvider != null) {
            yarnProvider.cleanupExpiredCache();
        }
        if (localProvider != null) {
            localProvider.cleanupExpiredCache();
        }
        
        log.debug("手动清理缓存完成");
    }
    
    // === 私有辅助方法 ===
    
    private SparkStatusProvider getCurrentProvider() {
        if (activeProvider == null) {
            log.warn("当前没有活跃的Provider，尝试重新初始化");
            initialize();
        }
        return activeProvider;
    }
    
    private SparkStatusProvider detectBestProvider() {
        List<SparkStatusProvider> providers = Arrays.asList(
            standaloneProvider, yarnProvider, localProvider
        );
        
        SparkStatusProvider bestProvider = null;
        long bestResponseTime = Long.MAX_VALUE;
        
        for (SparkStatusProvider provider : providers) {
            if (provider == null) {
                continue;
            }
            
            // 检查缓存的状态
            ProviderStatus cachedStatus = providerStatusCache.get(provider.getProviderName());
            if (cachedStatus != null && !cachedStatus.isExpired(60)) {
                if (cachedStatus.available && cachedStatus.responseTimeMs < bestResponseTime) {
                    bestProvider = provider;
                    bestResponseTime = cachedStatus.responseTimeMs;
                }
                continue;
            }
            
            // 测试Provider可用性
            long startTime = System.currentTimeMillis();
            boolean available = false;
            try {
                available = provider.testConnection();
            } catch (Exception e) {
                log.debug("Provider连接测试异常: {}", provider.getProviderName(), e);
            }
            long responseTime = System.currentTimeMillis() - startTime;
            
            // 缓存测试结果
            providerStatusCache.put(provider.getProviderName(), 
                new ProviderStatus(available, responseTime));
            
            // 选择最佳Provider
            if (available && responseTime < bestResponseTime) {
                bestProvider = provider;
                bestResponseTime = responseTime;
            }
            
            log.debug("Provider测试: name={}, available={}, responseTime={}ms", 
                     provider.getProviderName(), available, responseTime);
        }
        
        if (bestProvider != null) {
            log.debug("检测到最佳Provider: name={}, responseTime={}ms", 
                     bestProvider.getProviderName(), bestResponseTime);
        } else {
            log.warn("没有检测到可用的Provider");
        }
        
        return bestProvider;
    }
    
    private SparkStatusProvider getProviderByName(String name) {
        if (name == null) {
            return null;
        }
        
        switch (name.toLowerCase()) {
            case "standalone":
                return standaloneProvider;
            case "yarn":
                return yarnProvider;
            case "local":
                return localProvider;
            default:
                log.warn("未知的Provider名称: {}", name);
                return null;
        }
    }
    
    private SparkStatusProvider getProviderByDeployMode(String deployMode) {
        if (deployMode == null) {
            return standaloneProvider; // 默认使用Standalone
        }
        
        // 检查每个Provider是否支持该部署模式
        List<SparkStatusProvider> providers = Arrays.asList(
            standaloneProvider, yarnProvider, localProvider
        );
        
        for (SparkStatusProvider provider : providers) {
            if (provider != null && provider.supports(deployMode)) {
                log.debug("找到支持部署模式的Provider: mode={}, provider={}", 
                         deployMode, provider.getProviderName());
                return provider;
            }
        }
        
        log.warn("没有找到支持部署模式的Provider: {}", deployMode);
        return standaloneProvider; // 默认返回Standalone
    }
    
    private SparkApplicationInfo tryFallbackProvider(String applicationId, String operation) {
        // 简单的降级策略：按优先级尝试其他Provider
        List<SparkStatusProvider> fallbackProviders = Arrays.asList(
            localProvider, standaloneProvider, yarnProvider
        );
        
        for (SparkStatusProvider provider : fallbackProviders) {
            if (provider == null || provider == activeProvider) {
                continue;
            }
            
            try {
                log.debug("尝试降级Provider: operation={}, appId={}, fallbackProvider={}", 
                         operation, applicationId, provider.getProviderName());
                
                SparkApplicationInfo result = provider.getApplicationInfo(applicationId);
                if (result != null) {
                    log.info("降级Provider成功: operation={}, appId={}, fallbackProvider={}", 
                            operation, applicationId, provider.getProviderName());
                    return result;
                }
            } catch (Exception e) {
                log.debug("降级Provider失败: operation={}, appId={}, fallbackProvider={}", 
                         operation, applicationId, provider.getProviderName(), e);
            }
        }
        
        log.warn("所有降级Provider都失败: operation={}, appId={}", operation, applicationId);
        return null;
    }
    
    private void cleanupExpiredProviderStatus() {
        providerStatusCache.entrySet().removeIf(entry -> 
            entry.getValue().isExpired(providerDetectIntervalSeconds * 2));
    }
    
    // === Getter方法，用于监控和调试 ===
    
    public String getCurrentProviderName() {
        return activeProvider != null ? activeProvider.getProviderName() : "null";
    }
    
    public String getDefaultDeployMode() {
        return defaultDeployMode;
    }
    
    public String getForceProvider() {
        return forceProvider;
    }
    
    public int getProviderDetectIntervalSeconds() {
        return providerDetectIntervalSeconds;
    }
    
    /**
     * 获取所有Provider的状态信息，用于监控和调试
     * 
     * @return Provider状态信息
     */
    public String getProviderStatusSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("Active: %s, Default: %s, Force: %s\n", 
                                    getCurrentProviderName(), defaultDeployMode, forceProvider));
        
        providerStatusCache.forEach((name, status) -> {
            summary.append(String.format("%s: available=%s, responseTime=%dms, lastCheck=%s\n",
                                       name, status.available, status.responseTimeMs, 
                                       status.lastCheckTime));
        });
        
        return summary.toString();
    }
}