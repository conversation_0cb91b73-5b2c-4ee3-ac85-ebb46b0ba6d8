package com.trs.moye.batch.engine.utils;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.web.client.RestTemplate;

/**
 * SparkLogUtil
 *
 * <AUTHOR>
 * @since 2024/12/27 11:14
 */
public class SparkLogUtil {

    private SparkLogUtil() {
    }

    private static final RestTemplate REST_TEMPLATE = new RestTemplate();

    /**
     * 获取某个executor的stderr或stdout日志
     *
     * @param executorLink e.g.
     *                     http://192.168.210.56:8081/logPage/?appId=app-20241227074358-0028&executorId=2&logType=stderr
     * @return 日志内容
     */
    public static String analyseExecutorLog(String executorLink) {
        String html = REST_TEMPLATE.getForObject(executorLink, String.class);
        Document parsed = Jsoup.parse(Objects.requireNonNull(html));
        return parsed.select("pre").text();
    }

    /**
     * 获取所有executor的链接 executorId_logType 作为 key 链接 作为 value
     *
     * @param baseUrl web ui 地址
     * @param appId   spark application id
     * @return executor 链接
     */
    public static Map<String, String> getExecutorLogLinks(String baseUrl, String appId) throws URISyntaxException {
        String html = REST_TEMPLATE.getForObject(appUri(baseUrl, appId), String.class);
        Document parsed = Jsoup.parse(Objects.requireNonNull(html));
        // 过滤出 http 开头，并且包含 logPage 的链接，处理出executor和logType作为key
        return parsed.select("a[href]").stream()
            .map(element -> element.attr("href"))
            .filter(link -> link.startsWith("http"))
            .filter(link -> link.contains("logPage"))
            .collect(Collectors.toMap(SparkLogUtil::analyseExecutorLogLink, link -> link));
    }

    /**
     * 解析executor链接，获取executorId和logType
     *
     * @param url e.g. http://192.168.210.56:8081/logPage/?appId=app-20241227071555-0014&executorId=2&logType=stdout
     * @return executorId_logType
     */
    private static String analyseExecutorLogLink(String url) {
        try {
            URIBuilder uriBuilder = new URIBuilder(url);
            StringBuilder stringBuilder = new StringBuilder();
            for (NameValuePair param : uriBuilder.getQueryParams()) {
                if ("executorId".equals(param.getName())) {
                    stringBuilder.append("executor").append(param.getValue()).append("_");
                } else if ("logType".equals(param.getName())) {
                    stringBuilder.append(param.getValue());
                }
            }
            return stringBuilder.toString();
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * web ui 上 spark application 页面 e.g. http://192.168.210.56:8080/app/?appId=app-20241227071555-0014
     * 其中包含了所有executor的链接
     *
     * @param baseUrl http://192.168.210.56:8080
     * @param appId   spark application id
     * @return URI
     */
    private static URI appUri(String baseUrl, String appId) throws URISyntaxException {
        return new URIBuilder(baseUrl)
            .setPath("app")
            .addParameter("appId", appId)
            .build();
    }
}
