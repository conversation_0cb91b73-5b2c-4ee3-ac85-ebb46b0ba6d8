package com.trs.moye.batch.engine.utils;

import com.trs.moye.batch.engine.entity.SparkApplicationInfo;
import com.trs.moye.batch.engine.entity.SparkJobInfo;
import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * Spark状态映射工具类
 * 负责将Spark集群中的应用和作业状态映射为BatchTaskStatus
 *
 * <AUTHOR>
 * @since 2025/08/29
 */
@Slf4j
public class SparkStatusMapper {

    private SparkStatusMapper() {
        // 工具类，禁止实例化
    }

    /**
     * 将Spark应用状态映射为BatchTaskStatus
     * 映射规则：
     * - SUBMITTED/RUNNING -> RUNNING
     * - FINISHED + SUCCEEDED -> SUCCESS  
     * - FAILED/KILLED -> FAILED
     * - FINISHED + FAILED -> FAILED
     *
     * @param sparkApplicationInfo Spark应用信息
     * @return 对应的BatchTaskStatus
     */
    public static BatchTaskStatus mapApplicationStatusToBatchTaskStatus(SparkApplicationInfo sparkApplicationInfo) {
        if (sparkApplicationInfo == null) {
            log.warn("Spark应用信息为空，返回FAILED状态");
            return BatchTaskStatus.FAILED;
        }

        String state = sparkApplicationInfo.getState();
        String finalState = sparkApplicationInfo.getFinalState();
        
        log.debug("映射Spark应用状态: state={}, finalState={}", state, finalState);

        // 处理运行状态
        if ("SUBMITTED".equals(state) || "RUNNING".equals(state)) {
            return BatchTaskStatus.RUNNING;
        }

        // 处理完成状态
        if ("FINISHED".equals(state)) {
            if ("SUCCEEDED".equals(finalState)) {
                return BatchTaskStatus.SUCCESS;
            } else if ("FAILED".equals(finalState)) {
                return BatchTaskStatus.FAILED;
            } else {
                // finalState可能为UNDEFINED或其他值，根据作业状态进一步判断
                log.warn("Spark应用已完成但finalState未明确: {}, 需要检查作业状态", finalState);
                return BatchTaskStatus.RUNNING; // 返回运行状态，等待进一步检查
            }
        }

        // 处理失败和被杀死状态
        if ("FAILED".equals(state) || "KILLED".equals(state)) {
            return BatchTaskStatus.FAILED;
        }

        // 未知状态，默认为运行中
        log.warn("未知的Spark应用状态: {}, 默认返回RUNNING", state);
        return BatchTaskStatus.RUNNING;
    }

    /**
     * 根据Spark作业列表状态确定整体应用状态
     * 判断逻辑：
     * - 如果有任何作业在运行，则整体为RUNNING
     * - 如果所有作业都成功完成，则整体为SUCCESS
     * - 如果有任何作业失败，则整体为FAILED
     * - 如果没有作业或状态未知，则为RUNNING
     *
     * @param jobs Spark作业列表
     * @return 根据作业状态确定的BatchTaskStatus
     */
    public static BatchTaskStatus determineStatusFromJobs(List<SparkJobInfo> jobs) {
        if (jobs == null || jobs.isEmpty()) {
            log.debug("作业列表为空，返回RUNNING状态");
            return BatchTaskStatus.RUNNING;
        }

        boolean hasRunningJob = false;
        boolean hasFailedJob = false;
        int successfulJobCount = 0;
        int totalJobs = jobs.size();

        for (SparkJobInfo job : jobs) {
            String status = job.getStatus();
            log.debug("检查作业状态: jobId={}, status={}", job.getJobId(), status);

            if ("RUNNING".equals(status)) {
                hasRunningJob = true;
            } else if ("FAILED".equals(status)) {
                hasFailedJob = true;
            } else if ("SUCCEEDED".equals(status)) {
                successfulJobCount++;
            }
            // UNKNOWN状态的作业不影响整体判断
        }

        // 优先级判断：运行中 > 失败 > 成功
        if (hasRunningJob) {
            log.debug("存在运行中的作业，整体状态为RUNNING");
            return BatchTaskStatus.RUNNING;
        }

        if (hasFailedJob) {
            log.debug("存在失败的作业，整体状态为FAILED");
            return BatchTaskStatus.FAILED;
        }

        if (successfulJobCount == totalJobs) {
            log.debug("所有作业都成功完成，整体状态为SUCCESS");
            return BatchTaskStatus.SUCCESS;
        }

        // 部分成功或状态未知的情况
        log.debug("作业状态混合或未知，返回RUNNING状态进行进一步监控");
        return BatchTaskStatus.RUNNING;
    }

    /**
     * 综合判断Spark应用的最终状态
     * 优先使用应用级状态，如果应用状态不明确则参考作业状态
     *
     * @param applicationInfo Spark应用信息
     * @param jobs Spark作业列表
     * @return 综合判断后的BatchTaskStatus
     */
    public static BatchTaskStatus determineOverallStatus(SparkApplicationInfo applicationInfo, List<SparkJobInfo> jobs) {
        if (applicationInfo == null) {
            log.warn("Spark应用信息为空，仅根据作业状态判断");
            return determineStatusFromJobs(jobs);
        }

        BatchTaskStatus applicationStatus = mapApplicationStatusToBatchTaskStatus(applicationInfo);
        
        // 如果应用状态明确为成功或失败，直接返回
        if (applicationStatus == BatchTaskStatus.SUCCESS || applicationStatus == BatchTaskStatus.FAILED) {
            log.debug("应用状态明确: {}, 直接返回", applicationStatus);
            return applicationStatus;
        }

        // 如果应用状态为运行中，进一步检查作业状态
        if (applicationStatus == BatchTaskStatus.RUNNING) {
            BatchTaskStatus jobStatus = determineStatusFromJobs(jobs);
            
            // 如果作业状态表明已完成或失败，优先采用作业状态
            if (jobStatus == BatchTaskStatus.SUCCESS || jobStatus == BatchTaskStatus.FAILED) {
                log.debug("应用状态为运行中，但作业状态为: {}, 采用作业状态", jobStatus);
                return jobStatus;
            }
        }

        log.debug("最终确定状态: {}", applicationStatus);
        return applicationStatus;
    }

    /**
     * 检查Spark应用是否需要状态恢复
     * 判断条件：
     * - 应用不存在（可能已结束但数据库状态未更新）
     * - 应用已失败或被杀死
     * - 应用已成功完成
     *
     * @param applicationInfo Spark应用信息，null表示应用不存在
     * @return true表示需要状态恢复
     */
    public static boolean needsStatusRecovery(SparkApplicationInfo applicationInfo) {
        if (applicationInfo == null) {
            log.debug("Spark应用不存在，需要状态恢复");
            return true;
        }

        boolean needsRecovery = applicationInfo.isCompleted();
        log.debug("Spark应用状态检查: state={}, 需要恢复={}", 
                 applicationInfo.getState(), needsRecovery);
        
        return needsRecovery;
    }
}