package com.trs.moye.batch.engine.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.util.List;
import lombok.Data;

/**
 * Spark应用程序信息实体
 * 对应Spark REST API返回的应用程序信息
 *
 * <AUTHOR>
 * @since 2025/08/29
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SparkApplicationInfo {

    /**
     * 应用程序ID
     */
    @JsonProperty("id")
    private String id;

    /**
     * 应用程序名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 应用程序状态
     * 可能的值: SUBMITTED, RUNNING, FINISHED, FAILED, KILLED
     */
    @JsonProperty("state")
    private String state;

    /**
     * 最终状态
     * 可能的值: SUCCEEDED, FAILED, KILLED, UNDEFINED
     */
    @JsonProperty("finalState")
    private String finalState;

    /**
     * 应用程序开始时间
     */
    @JsonProperty("startTime")
    private Instant startTime;

    /**
     * 应用程序结束时间
     */
    @JsonProperty("endTime")
    private Instant endTime;

    /**
     * 总CPU核心数
     */
    @JsonProperty("totalCores")
    private Integer totalCores;

    /**
     * 每个执行器内存大小（MB）
     */
    @JsonProperty("memoryPerExecutor")
    private Long memoryPerExecutor;

    /**
     * 驱动程序内存大小（MB）
     */
    @JsonProperty("driverMemory")
    private Long driverMemory;

    /**
     * 执行器数量
     */
    @JsonProperty("numExecutors")
    private Integer numExecutors;

    /**
     * 应用程序尝试列表
     */
    @JsonProperty("attempts")
    private List<SparkApplicationAttempt> attempts;

    /**
     * Spark应用程序尝试信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SparkApplicationAttempt {
        /**
         * 尝试ID
         */
        @JsonProperty("attemptId")
        private String attemptId;

        /**
         * 开始时间
         */
        @JsonProperty("startTime")
        private Instant startTime;

        /**
         * 结束时间
         */
        @JsonProperty("endTime")
        private Instant endTime;

        /**
         * 持续时间（毫秒）
         */
        @JsonProperty("duration")
        private Long duration;

        /**
         * 是否完成
         */
        @JsonProperty("completed")
        private Boolean completed;
    }

    /**
     * 判断应用程序是否正在运行
     *
     * @return true表示正在运行
     */
    public boolean isRunning() {
        return "RUNNING".equals(state);
    }

    /**
     * 判断应用程序是否已完成
     *
     * @return true表示已完成（成功或失败）
     */
    public boolean isCompleted() {
        return "FINISHED".equals(state) || "FAILED".equals(state) || "KILLED".equals(state);
    }

    /**
     * 判断应用程序是否成功完成
     *
     * @return true表示成功完成
     */
    public boolean isSuccessful() {
        return "FINISHED".equals(state) && "SUCCEEDED".equals(finalState);
    }

    /**
     * 判断应用程序是否失败
     *
     * @return true表示失败
     */
    public boolean isFailed() {
        return "FAILED".equals(state) || 
               ("FINISHED".equals(state) && "FAILED".equals(finalState)) ||
               "KILLED".equals(state);
    }
}