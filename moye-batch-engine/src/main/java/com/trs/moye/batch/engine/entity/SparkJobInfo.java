package com.trs.moye.batch.engine.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import lombok.Data;

/**
 * Spark作业信息实体
 * 对应Spark REST API返回的作业信息
 *
 * <AUTHOR>
 * @since 2025/08/29
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SparkJobInfo {

    /**
     * 作业ID
     */
    @JsonProperty("jobId")
    private Long jobId;

    /**
     * 作业名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 作业状态
     * 可能的值: RUNNING, SUCCEEDED, FAILED, UNKNOWN
     */
    @JsonProperty("status")
    private String status;

    /**
     * 作业提交时间
     */
    @JsonProperty("submissionTime")
    private Instant submissionTime;

    /**
     * 作业完成时间
     */
    @JsonProperty("completionTime")
    private Instant completionTime;

    /**
     * 阶段ID列表
     */
    @JsonProperty("stageIds")
    private Long[] stageIds;

    /**
     * 作业组
     */
    @JsonProperty("jobGroup")
    private String jobGroup;

    /**
     * 总任务数
     */
    @JsonProperty("numTasks")
    private Integer numTasks;

    /**
     * 活跃任务数
     */
    @JsonProperty("numActiveTasks")
    private Integer numActiveTasks;

    /**
     * 已完成任务数
     */
    @JsonProperty("numCompletedTasks")
    private Integer numCompletedTasks;

    /**
     * 已跳过任务数
     */
    @JsonProperty("numSkippedTasks")
    private Integer numSkippedTasks;

    /**
     * 失败任务数
     */
    @JsonProperty("numFailedTasks")
    private Integer numFailedTasks;

    /**
     * 活跃阶段数
     */
    @JsonProperty("numActiveStages")
    private Integer numActiveStages;

    /**
     * 已完成阶段数
     */
    @JsonProperty("numCompletedStages")
    private Integer numCompletedStages;

    /**
     * 已跳过阶段数
     */
    @JsonProperty("numSkippedStages")
    private Integer numSkippedStages;

    /**
     * 失败阶段数
     */
    @JsonProperty("numFailedStages")
    private Integer numFailedStages;

    /**
     * 判断作业是否正在运行
     *
     * @return true表示正在运行
     */
    public boolean isRunning() {
        return "RUNNING".equals(status);
    }

    /**
     * 判断作业是否成功完成
     *
     * @return true表示成功完成
     */
    public boolean isSuccessful() {
        return "SUCCEEDED".equals(status);
    }

    /**
     * 判断作业是否失败
     *
     * @return true表示失败
     */
    public boolean isFailed() {
        return "FAILED".equals(status);
    }

    /**
     * 判断作业状态是否未知
     *
     * @return true表示状态未知
     */
    public boolean isUnknown() {
        return "UNKNOWN".equals(status);
    }

    /**
     * 获取作业进度百分比
     *
     * @return 进度百分比（0-100）
     */
    public double getProgressPercentage() {
        if (numTasks == null || numTasks == 0) {
            return 0.0;
        }
        int completedAndSkipped = (numCompletedTasks != null ? numCompletedTasks : 0) + 
                                 (numSkippedTasks != null ? numSkippedTasks : 0);
        return (completedAndSkipped * 100.0) / numTasks;
    }
}