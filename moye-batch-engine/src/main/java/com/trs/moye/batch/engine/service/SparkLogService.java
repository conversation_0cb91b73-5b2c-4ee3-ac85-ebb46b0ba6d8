package com.trs.moye.batch.engine.service;

import com.trs.moye.batch.engine.config.HadoopProperties;
import com.trs.moye.batch.engine.constants.SparkConstants.TaskParameter;
import com.trs.moye.batch.engine.exception.MinioOperationException;
import com.trs.moye.batch.engine.spark.SparkApplication;
import com.trs.moye.batch.engine.spark.SparkApplicationContext;
import com.trs.moye.batch.engine.spark.config.SparkApplicationConfig;
import com.trs.moye.batch.engine.utils.SparkLogUtil;
import com.trs.moye.batch.engine.utils.yarnlog.YarnLogUtil;
import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;


/**
 * SparkLogService 实现类
 */
@Slf4j
@Service
public class SparkLogService {

    @Resource
    private HadoopProperties hadoopProperties;
    @Resource
    private SparkApplicationConfig sparkApplicationConfig;
    @Resource
    private MinioService minioService;


    /**
     * 存储日志 1. standalone 模式下 spark集群上executor的日志 3. yarn 模式下 集群上的日志
     *
     * @param sparkApplication 根据 spark 任务拉取日志
     */
    public void storageLogs(SparkApplication sparkApplication) {
        // 如果是standalone模式拉取web ui上的日志
        storageStandaloneLog(sparkApplication);
        // 如果是yarn模式的拉取yarn日志
        storageYarnLog(sparkApplication);
    }

    /**
     * 请求web ui，存储到minio
     *
     * @param sparkApplication spark application
     */
    public void storageStandaloneLog(SparkApplication sparkApplication) {
        if (!sparkApplication.isStandaloneMode()) {
            log.info("非 standalone 模式，不拉取 web ui 日志");
            return;
        }

        try {
            Map<String, String> executorLogLinks = SparkLogUtil.getExecutorLogLinks(
                sparkApplicationConfig.getWebUiUrl(),
                sparkApplication.getApplicationId());
            String taskName = sparkApplication.getContext().getTaskName();
            for (Map.Entry<String, String> entry : executorLogLinks.entrySet()) {
                String logContent = SparkLogUtil.analyseExecutorLog(entry.getValue());
                minioService.saveTextToFile(sparkApplication.getContext().getLogPath() + "/" + taskName + "_" + entry.getKey(),
                    logContent);
            }
        } catch (URISyntaxException e) {
            log.warn("standalone web ui 相关地址解析异常", e);
        }

    }

    /**
     * 存放到本地临时目录，再复制到minio，删除本地日志
     *
     * @param sparkApplication spark 应用
     */
    private void storageYarnLog(SparkApplication sparkApplication) {
        if (!sparkApplication.isYarnMode()) {
            log.info("非 yarn 模式，不拉取 yarn 日志");
            return;
        }
        String yarnLogDir = "/tmp/yarn-log";
        SparkApplicationContext context = sparkApplication.getContext();
        Map<String, String> sparkTaskParameters = context.getConfigParameters();

        // 日志拉到本地
        String outputDir = Paths.get(yarnLogDir, context.getLogPath()).toString();
        File yarnLogFile = fetchYarnLog(
            sparkApplication.getApplicationId(),
            sparkTaskParameters.getOrDefault(TaskParameter.KERBEROS_PRINCIPAL, null),
            sparkTaskParameters.getOrDefault(TaskParameter.KERBEROS_KEYTAB, null),
            outputDir
        );

        // 复制到minio
        if (yarnLogFile != null) {
            try {
                String taskName = context.getTaskName();
                minioService.copyLocalFileToMinio(yarnLogDir, yarnLogFile, taskName);
                FileUtils.deleteDirectory(yarnLogFile);
            } catch (MinioOperationException e) {
                log.warn("复制文件 {} 到 minio 失败", yarnLogFile, e);
            } catch (IOException e) {
                log.warn("删除文件 {} 异常", yarnLogFile, e);
            }
        }
    }

    /**
     * 根据 appId 拉取日志，通过principal和keytab进行kerberos认证
     *
     * @param appId     需要拉取的spark任务id
     * @param principal kerberos用户
     * @param keytab    kerberos凭据
     * @param dir       拉取到的日志存放的本地目录路径
     * @return 实际存放文件的目录路径，如果拉取失败则返回null
     */
    private File fetchYarnLog(String appId, String principal, String keytab, String dir) {
        if (appId == null) {
            return null;
        }

        Set<String> resources = new HashSet<>(3);
        resources.add(hadoopProperties.getCoreSiteXmlPath());
        resources.add(hadoopProperties.getHdfsSiteXmlPath());
        resources.add(hadoopProperties.getYarnSiteXmlPath());

        try {
            if (principal != null) {
                YarnLogUtil.fetchLogs(appId, principal, keytab, dir, resources);
            } else {
                YarnLogUtil.fetchLogs(appId, dir, resources);
            }
            return new File(dir);
        } catch (IOException e) {
            log.error("拉取yarn日志失败", e);
            return null;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        }
    }
}
