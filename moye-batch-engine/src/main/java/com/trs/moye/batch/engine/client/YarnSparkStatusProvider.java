package com.trs.moye.batch.engine.client;

import com.trs.moye.batch.engine.entity.SparkApplicationInfo;
import com.trs.moye.batch.engine.entity.SparkJobInfo;
import com.trs.moye.batch.engine.utils.SparkWebUiParser;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Yarn模式Spark状态提供者
 * 通过Yarn ResourceManager Web UI获取Spark应用状态信息
 * 
 * 支持的URL格式：
 * - 应用信息：http://resource-manager:8088/cluster/app/{applicationId}
 * - 集群状态：http://resource-manager:8088/cluster (用于连接测试)
 * - 应用列表：http://resource-manager:8088/cluster/apps (用于查找Spark应用)
 * 
 * 特点：
 * 1. 解析Yarn ResourceManager Web UI获取应用状态
 * 2. 支持Yarn应用状态到Spark状态的映射
 * 3. 提供Yarn集群连接测试功能
 * 4. 继承基类的缓存和重试机制
 * 
 * 注意：Yarn模式下作业信息通常需要通过Spark History Server获取，
 * 当前实现主要关注应用级状态信息。
 * 
 * <AUTHOR>
 * @since 2025/08/29
 */
@Slf4j
@Component
public class YarnSparkStatusProvider extends BaseSparkStatusProvider {
    
    /**
     * Yarn ResourceManager Web UI URL
     * 配置示例：yarn.resource-manager.web-ui-url=http://resource-manager:8088
     */
    @Value("${yarn.resource-manager.web-ui-url:http://localhost:8088}")
    private String yarnResourceManagerUrl;
    
    private static final String PROVIDER_NAME = "Yarn";
    
    // URL路径常量
    private static final String CLUSTER_PATH = "/cluster";
    private static final String APP_PATH = "/cluster/app/";
    private static final String APPS_PATH = "/cluster/apps";
    
    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }
    
    @Override
    public boolean supports(String deployMode) {
        return "yarn".equalsIgnoreCase(deployMode) || 
               "yarn-cluster".equalsIgnoreCase(deployMode) ||
               "yarn-client".equalsIgnoreCase(deployMode);
    }
    
    @Override
    public boolean testConnection() {
        return executeWithRetry(() -> {
            String testUrl = normalizeUrl(yarnResourceManagerUrl) + CLUSTER_PATH;
            log.debug("测试Yarn ResourceManager连接: url={}", testUrl);
            
            Document document = parseHtmlPage(testUrl);
            if (document == null) {
                return false;
            }
            
            // 检查页面是否包含Yarn ResourceManager的标识
            String pageText = document.text().toLowerCase();
            boolean isYarnRM = pageText.contains("resourcemanager") || 
                              pageText.contains("cluster overview") ||
                              pageText.contains("applications") ||
                              pageText.contains("yarn") ||
                              document.title().toLowerCase().contains("resourcemanager");
                              
            log.debug("Yarn ResourceManager连接测试: url={}, connected={}", testUrl, isYarnRM);
            return isYarnRM;
            
        }, "Yarn连接测试", null);
    }
    
    @Override
    protected SparkApplicationInfo fetchApplicationInfoFromWebUI(String applicationId) {
        return executeWithRetry(() -> {
            String appUrl = buildApplicationUrl(applicationId);
            log.debug("获取Yarn应用信息: url={}", appUrl);
            
            Document document = parseHtmlPage(appUrl);
            if (document == null) {
                return null;
            }
            
            // 检查应用是否存在
            if (SparkWebUiParser.isApplicationNotFound(document)) {
                log.debug("Yarn应用不存在: appId={}", applicationId);
                return null;
            }
            
            // 解析应用信息
            SparkApplicationInfo appInfo = SparkWebUiParser.parseYarnApplicationInfo(document, applicationId);
            
            if (SparkWebUiParser.isValidApplicationInfo(appInfo)) {
                // 验证是否为Spark应用
                if (isSparkApplication(appInfo)) {
                    log.debug("成功获取Yarn Spark应用信息: appId={}, state={}, finalState={}", 
                             applicationId, appInfo.getState(), appInfo.getFinalState());
                    return appInfo;
                } else {
                    log.debug("应用不是Spark应用: appId={}, name={}", applicationId, appInfo.getName());
                    return null;
                }
            } else {
                log.warn("解析的Yarn应用信息无效: appId={}", applicationId);
                return null;
            }
            
        }, "获取Yarn应用信息", applicationId);
    }
    
    @Override
    protected List<SparkJobInfo> fetchApplicationJobsFromWebUI(String applicationId) {
        // Yarn模式下，作业信息通常通过Spark History Server获取
        // 当前实现返回空列表，可以在后续版本中通过History Server API补充
        log.debug("Yarn模式暂不支持直接获取作业信息，建议通过Spark History Server获取: appId={}", applicationId);
        return Collections.emptyList();
    }
    
    /**
     * 构建应用详情页面URL
     * 
     * @param applicationId 应用ID
     * @return 完整的应用详情页面URL
     */
    private String buildApplicationUrl(String applicationId) {
        return normalizeUrl(yarnResourceManagerUrl) + APP_PATH + applicationId;
    }
    
    /**
     * 标准化URL格式
     * 移除末尾的斜杠以保持URL一致性
     * 
     * @param url 原始URL
     * @return 标准化后的URL
     */
    private String normalizeUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            throw new IllegalStateException("Yarn ResourceManager URL未配置");
        }
        
        String normalizedUrl = url.trim();
        if (normalizedUrl.endsWith("/")) {
            normalizedUrl = normalizedUrl.substring(0, normalizedUrl.length() - 1);
        }
        
        return normalizedUrl;
    }
    
    /**
     * 验证应用是否为Spark应用
     * 通过应用类型和名称判断
     * 
     * @param appInfo 应用信息
     * @return true表示是Spark应用
     */
    private boolean isSparkApplication(SparkApplicationInfo appInfo) {
        if (appInfo == null) {
            return false;
        }
        
        // 检查应用名称是否包含Spark标识
        String name = appInfo.getName();
        if (name != null) {
            String lowerName = name.toLowerCase();
            if (lowerName.contains("spark") || lowerName.contains("scala") || lowerName.contains("pyspark")) {
                return true;
            }
        }
        
        // 检查应用ID格式（Spark应用ID通常以application_开头）
        String appId = appInfo.getId();
        if (appId != null && appId.startsWith("application_")) {
            return true;
        }
        
        // 默认认为是Spark应用（如果通过其他方式无法确定）
        log.debug("无法确定应用类型，默认认为是Spark应用: appId={}, name={}", 
                 appInfo.getId(), name);
        return true;
    }
    
    /**
     * 获取当前配置的Yarn ResourceManager URL
     * 
     * @return Yarn ResourceManager URL
     */
    public String getYarnResourceManagerUrl() {
        return yarnResourceManagerUrl;
    }
    
    /**
     * 设置Yarn ResourceManager URL（主要用于测试）
     * 
     * @param url Yarn ResourceManager URL
     */
    public void setYarnResourceManagerUrl(String url) {
        this.yarnResourceManagerUrl = url;
    }
    
    /**
     * 构建应用列表页面URL，用于查找特定的Spark应用
     * 
     * @return 应用列表页面URL
     */
    public String buildAppsListUrl() {
        return normalizeUrl(yarnResourceManagerUrl) + APPS_PATH;
    }
    
    /**
     * 获取提供者配置信息摘要
     * 
     * @return 配置信息字符串
     */
    public String getConfigSummary() {
        return String.format("YarnRM=%s, Cache=%s, Timeout=%ds/%ds", 
                           yarnResourceManagerUrl, getCacheConfig(), 
                           connectTimeoutSeconds, readTimeoutSeconds);
    }
    
    /**
     * 获取缓存配置信息
     * 
     * @return 缓存配置信息的字符串表示
     */
    public String getCacheConfig() {
        return String.format("enabled=%s, expiryMinutes=%d", cacheEnabled, cacheExpiryMinutes);
    }
}