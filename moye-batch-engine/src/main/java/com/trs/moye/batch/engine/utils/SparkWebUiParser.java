package com.trs.moye.batch.engine.utils;

import com.trs.moye.batch.engine.entity.SparkApplicationInfo;
import com.trs.moye.batch.engine.entity.SparkJobInfo;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

/**
 * Spark Web UI解析工具类 提供统一的HTML解析功能，支持解析各种Spark Web UI页面
 * <p>
 * 支持解析的页面类型： - Standalone模式：Spark Master Web UI页面 - Yarn模式：ResourceManager Web UI页面 - 应用详情页面：应用状态和基本信息 -
 * 作业列表页面：作业状态和执行信息
 *
 * <AUTHOR>
 * @since 2025/08/29
 */
@Slf4j
public class SparkWebUiParser {

    private SparkWebUiParser() {
        // 工具类，禁止实例化
    }

    // === Standalone模式解析 ===

    /**
     * 解析Spark Standalone Master页面，获取应用信息 解析URL: http://spark-master:8080/app/?appId=app-xxx
     *
     * @param document      解析后的HTML文档
     * @param applicationId 应用ID
     * @return Spark应用信息
     */
    public static SparkApplicationInfo parseStandaloneApplicationInfo(Document document, String applicationId) {
        if (document == null) {
            log.warn("HTML文档为空，无法解析Standalone应用信息");
            return null;
        }

        try {
            SparkApplicationInfo appInfo = new SparkApplicationInfo();
            appInfo.setId(applicationId);

            // 解析应用名称 - 通常在页面标题或h3标签中
            Element titleElement = document.selectFirst("h3");
            if (titleElement != null) {
                String titleText = titleElement.text();
                if (titleText.contains("Application Detail")) {
                    // 提取应用名称，格式通常为 "Application Detail (AppName)"
                    int start = titleText.indexOf('(');
                    int end = titleText.lastIndexOf(')');
                    if (start > 0 && end > start) {
                        appInfo.setName(titleText.substring(start + 1, end));
                    }
                }
            }

            // 解析应用状态信息表格
            Elements tables = document.select("table");
            for (Element table : tables) {
                Elements rows = table.select("tr");
                for (Element row : rows) {
                    Elements cells = row.select("td");
                    if (cells.size() >= 2) {
                        String key = cells.get(0).text().trim();
                        String value = cells.get(1).text().trim();

                        switch (key.toLowerCase()) {
                            case "status":
                            case "state":
                                appInfo.setState(value.toUpperCase());
                                break;
                            case "final status":
                            case "final state":
                                appInfo.setFinalState(value.toUpperCase());
                                break;
                            case "user":
                                appInfo.setUser(value);
                                break;
                            case "started time":
                            case "start time":
                                // 解析时间格式并设置startTime
                                break;
                            case "finished time":
                            case "end time":
                                // 解析时间格式并设置endTime
                                break;
                        }
                    }
                }
            }

            // 如果没有找到状态，尝试从页面其他位置获取
            if (appInfo.getState() == null) {
                // 查找状态指示器或徽章
                Elements statusElements = document.select(".badge, .label, .status");
                for (Element statusElement : statusElements) {
                    String statusText = statusElement.text().toUpperCase();
                    if (statusText.matches("RUNNING|FINISHED|FAILED|KILLED|SUBMITTED")) {
                        appInfo.setState(statusText);
                        break;
                    }
                }
            }

            log.debug("解析Standalone应用信息完成: appId={}, state={}, name={}",
                applicationId, appInfo.getState(), appInfo.getName());

            return appInfo;

        } catch (Exception e) {
            log.error("解析Standalone应用信息失败: appId={}", applicationId, e);
            return null;
        }
    }

    /**
     * 解析Spark Standalone作业列表页面
     *
     * @param document      解析后的HTML文档
     * @param applicationId 应用ID
     * @return Spark作业信息列表
     */
    public static List<SparkJobInfo> parseStandaloneApplicationJobs(Document document, String applicationId) {
        List<SparkJobInfo> jobs = new ArrayList<>();

        if (document == null) {
            log.warn("HTML文档为空，无法解析Standalone作业信息");
            return jobs;
        }

        try {
            // 查找作业列表表格
            Elements jobTables = document.select("table");
            for (Element table : jobTables) {
                Element tableHeader = table.selectFirst("thead tr");
                if (tableHeader != null && tableHeader.text().toLowerCase().contains("job")) {
                    // 这是作业列表表格
                    Elements jobRows = table.select("tbody tr");
                    for (Element row : jobRows) {
                        Elements cells = row.select("td");
                        if (cells.size() >= 4) {  // 假设至少有Job ID, Description, Status, Start Time
                            SparkJobInfo jobInfo = new SparkJobInfo();

                            // 解析Job ID
                            String jobIdText = cells.get(0).text().trim();
                            try {
                                jobInfo.setJobId(Integer.parseInt(jobIdText));
                            } catch (NumberFormatException e) {
                                log.debug("无法解析Job ID: {}", jobIdText);
                                continue;
                            }

                            // 解析作业描述
                            if (cells.size() > 1) {
                                jobInfo.setName(cells.get(1).text().trim());
                            }

                            // 解析作业状态
                            if (cells.size() > 2) {
                                String status = cells.get(2).text().trim().toUpperCase();
                                jobInfo.setStatus(status);
                            }

                            // 解析开始时间
                            if (cells.size() > 3) {
                                // 设置submissionTime等时间字段
                            }

                            jobs.add(jobInfo);
                        }
                    }
                    break;  // 找到作业表格后退出
                }
            }

            log.debug("解析Standalone作业信息完成: appId={}, jobCount={}", applicationId, jobs.size());

        } catch (Exception e) {
            log.error("解析Standalone作业信息失败: appId={}", applicationId, e);
        }

        return jobs;
    }

    // === Yarn模式解析 ===

    /**
     * 解析Yarn ResourceManager页面，获取应用信息 解析URL: http://resource-manager:8088/cluster/app/application_xxx
     *
     * @param document      解析后的HTML文档
     * @param applicationId 应用ID
     * @return Spark应用信息
     */
    public static SparkApplicationInfo parseYarnApplicationInfo(Document document, String applicationId) {
        if (document == null) {
            log.warn("HTML文档为空，无法解析Yarn应用信息");
            return null;
        }

        try {
            SparkApplicationInfo appInfo = new SparkApplicationInfo();
            appInfo.setId(applicationId);

            // 解析应用信息表格 - Yarn页面通常有应用概览表格
            Elements infoTables = document.select("table.info");
            if (infoTables.isEmpty()) {
                // 尝试其他可能的表格选择器
                infoTables = document.select("table");
            }

            for (Element table : infoTables) {
                Elements rows = table.select("tr");
                for (Element row : rows) {
                    Elements cells = row.select("td, th");
                    if (cells.size() >= 2) {
                        String key = cells.get(0).text().trim();
                        String value = cells.get(1).text().trim();

                        switch (key.toLowerCase()) {
                            case "application name":
                            case "name":
                                appInfo.setName(value);
                                break;
                            case "application type":
                            case "type":
                                // 验证是否为Spark应用
                                break;
                            case "state":
                            case "yarn application state":
                                appInfo.setState(mapYarnStateToSparkState(value));
                                break;
                            case "final status":
                            case "final state":
                                appInfo.setFinalState(mapYarnFinalStatusToSparkFinalState(value));
                                break;
                            case "user":
                                appInfo.setUser(value);
                                break;
                            case "started":
                            case "start time":
                                // 解析并设置startTime
                                break;
                            case "finished":
                            case "finish time":
                                // 解析并设置endTime
                                break;
                        }
                    }
                }
            }

            log.debug("解析Yarn应用信息完成: appId={}, state={}, finalState={}",
                applicationId, appInfo.getState(), appInfo.getFinalState());

            return appInfo;

        } catch (Exception e) {
            log.error("解析Yarn应用信息失败: appId={}", applicationId, e);
            return null;
        }
    }

    // === 状态映射辅助方法 ===

    /**
     * 将Yarn应用状态映射为Spark状态
     */
    private static String mapYarnStateToSparkState(String yarnState) {
        if (yarnState == null) {
            return null;
        }

        switch (yarnState.toUpperCase()) {
            case "NEW":
            case "NEW_SAVING":
            case "SUBMITTED":
                return "SUBMITTED";
            case "ACCEPTED":
            case "RUNNING":
                return "RUNNING";
            case "FINISHED":
                return "FINISHED";
            case "FAILED":
                return "FAILED";
            case "KILLED":
                return "KILLED";
            default:
                log.debug("未知的Yarn状态: {}", yarnState);
                return yarnState.toUpperCase();
        }
    }

    /**
     * 将Yarn最终状态映射为Spark最终状态
     */
    private static String mapYarnFinalStatusToSparkFinalState(String yarnFinalStatus) {
        if (yarnFinalStatus == null) {
            return null;
        }

        switch (yarnFinalStatus.toUpperCase()) {
            case "SUCCEEDED":
                return "SUCCEEDED";
            case "FAILED":
                return "FAILED";
            case "KILLED":
                return "KILLED";
            case "UNDEFINED":
                return "UNDEFINED";
            default:
                log.debug("未知的Yarn最终状态: {}", yarnFinalStatus);
                return yarnFinalStatus.toUpperCase();
        }
    }

    // === 通用解析辅助方法 ===

    /**
     * 从表格中查找指定键的值
     *
     * @param table 表格元素
     * @param key   要查找的键（不区分大小写）
     * @return 对应的值，未找到时返回null
     */
    public static String findValueInTable(Element table, String key) {
        if (table == null || key == null) {
            return null;
        }

        Elements rows = table.select("tr");
        for (Element row : rows) {
            Elements cells = row.select("td, th");
            if (cells.size() >= 2) {
                String cellKey = cells.get(0).text().trim();
                if (cellKey.toLowerCase().contains(key.toLowerCase())) {
                    return cells.get(1).text().trim();
                }
            }
        }
        return null;
    }

    /**
     * 检查页面是否包含应用不存在的指示
     *
     * @param document HTML文档
     * @return true表示应用不存在
     */
    public static boolean isApplicationNotFound(Document document) {
        if (document == null) {
            return true;
        }

        String pageText = document.text().toLowerCase();
        return pageText.contains("not found") ||
            pageText.contains("does not exist") ||
            pageText.contains("application not found") ||
            pageText.contains("404");
    }

    /**
     * 验证解析结果的有效性
     *
     * @param applicationInfo 应用信息
     * @return true表示信息有效
     */
    public static boolean isValidApplicationInfo(SparkApplicationInfo applicationInfo) {
        return applicationInfo != null &&
            Objects.nonNull(applicationInfo.getId()) &&
            !applicationInfo.getId().trim().isEmpty();
    }
}