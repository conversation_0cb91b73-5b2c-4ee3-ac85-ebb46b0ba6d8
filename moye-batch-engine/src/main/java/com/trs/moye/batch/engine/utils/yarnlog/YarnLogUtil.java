package com.trs.moye.batch.engine.utils.yarnlog;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.Path;
import org.apache.hadoop.security.UserGroupInformation;
import org.apache.hadoop.yarn.api.records.ApplicationId;
import org.apache.hadoop.yarn.conf.YarnConfiguration;
import org.apache.hadoop.yarn.logaggregation.ContainerLogsRequest;
import org.apache.hadoop.yarn.logaggregation.LogCLIHelpers;
import org.jetbrains.annotations.NotNull;


/**
 * yarn 日志工具
 *
 * <AUTHOR>
 * @since 2024/06/25
 */
@Slf4j
public class YarnLogUtil {


    /**
     * 通过 yarn logs -applicationId 命令获取 spark 任务日志并输入到流
     *
     * @param appId      yarn上的任务id
     * @param principal  kerberos用户
     * @param keytabPath kerberos凭据
     * @return 日志输出流
     */
    public static InputStream streamLogs(String appId, String principal, String keytabPath) throws IOException {
        List<String> commands = new ArrayList<>();
        commands.add("/bin/bash");
        commands.add("-c");
        if (Objects.nonNull(principal)) {
            checkKerberosParams(principal, keytabPath);
            commands.add("source /home/<USER>/bigdata_env;");
            commands.add("kinit -k -t " + keytabPath + " " + principal + ";");
        }
        commands.add("yarn logs -applicationId " + appId);

        ProcessBuilder processBuilder = new ProcessBuilder(commands);
        Process process = processBuilder.start();
        return process.getInputStream();
    }


    /**
     * 以系统用户去获取 yarn 上 stderr 和 stdout 类型的日志<br>
     * <b>存在失败重试机制，耗时有可能很长</b>
     *
     * @param appId     yarn上的任务id
     * @param outputDir 日志输出到本地路径
     * @param resources 查询日志需要的配置文件资源
     */
    public static void fetchLogs(String appId, String outputDir, Set<String> resources)
        throws IOException, InterruptedException {

        checkAppId(appId);
        checkOutputDir(outputDir);
        checkResources(resources);

        Configuration configuration = buildConfiguration(resources);
        String appOwner = UserGroupInformation.getCurrentUser().getShortUserName();
        log.info("开始拉取yarn日志，无指定用户，使用当前用户 {}", appOwner);
        fetchLogs(appId, appOwner, outputDir, configuration);
    }


    /**
     * 以 kerberos 用户 获取 yarn 日志<br>
     * <b>存在失败重试机制，耗时有可能很长</b>
     *
     * @param appId      yarn上的任务id
     * @param principal  用户名
     * @param keytabPath keytab文件路径
     * @param outputDir  日志输出到本地路径
     * @param resources  查询日志需要的配置文件资源
     */
    public static void fetchLogs(String appId, String principal, String keytabPath, String outputDir,
        Set<String> resources)
        throws IOException, InterruptedException {

        checkAppId(appId);
        checkKerberosParams(principal, keytabPath);
        checkOutputDir(outputDir);
        checkResources(resources);

        Configuration configuration = buildConfiguration(resources);

        UserGroupInformation.setConfiguration(configuration);
        UserGroupInformation.loginUserFromKeytab(principal, keytabPath);
        String appOwner = UserGroupInformation.getCurrentUser().getShortUserName();
        log.info("开始拉取yarn日志，指定用户 user:{}, principal:{}, keytab:{}, 使用配置文件 {}", appOwner, principal,
            keytabPath, String.join(", ", resources));

        fetchLogs(appId, appOwner, outputDir, configuration);

        // 退出kerberos认证
        UserGroupInformation.reset();
    }


    /**
     * <p>
     * 获取 yarn 上 stderr 和 stdout 类型的日志<br>
     * <b>存在失败重试机制，耗时有可能很长</b>
     * </p>
     *
     * @param appId         yarn上的任务id
     * @param appOwner      任务所有者
     * @param outputDir     日志输出到本地路径
     * @param configuration 查询yarn日志需要的配置
     */
    private static void fetchLogs(String appId, String appOwner, String outputDir, Configuration configuration)
        throws IOException, InterruptedException {

        LogCLIHelpers logHelpers = new LogCLIHelpers();
        logHelpers.setConf(configuration);

        // 指定要读取的日志类型
        Set<String> logTypes = new HashSet<>(3);
        logTypes.add(YarnLogType.STDERR.getLogType());
        logTypes.add(YarnLogType.STDOUT.getLogType());
        logTypes.add(YarnLogType.STDOUT_EXT.getLogType());

        // 构建 ContainerLogsRequest 对象
        ContainerLogsRequest containerLogsRequest = buildContainerLogsRequest(appId, appOwner, outputDir, logTypes);

        try {
            dumpAllContainersLogs(logHelpers, containerLogsRequest);
        } catch (IOException e) {
            String errorMsg = String.format("为 applicationId [%s] 拉取 yarn 日志失败，使用用户身份 [%s]", appId,
                appOwner);
            throw new IOException(errorMsg, e);
        } catch (RuntimeException e) {
            String errorMsg = String.format("为 applicationId [%s] 拉取 yarn 日志失败，使用用户身份 [%s]", appId,
                appOwner);
            throw new RuntimeException(errorMsg, e);
        }
    }

    /**
     * 日志拉取失败重试
     *
     * @param logHelpers           日志工具
     * @param containerLogsRequest 拉取日志的请求体
     */
    private static void dumpAllContainersLogs(LogCLIHelpers logHelpers, ContainerLogsRequest containerLogsRequest)
        throws IOException, InterruptedException {

        int count = 0;
        boolean hasFetchLog = false;
        int sleepTimeMultiplier = 1;

        while (count < 6 && !hasFetchLog) {
            count++;
            try {
                // 拉取日志
                logHelpers.dumpAllContainersLogs(containerLogsRequest);
                hasFetchLog = true;
            } catch (NullPointerException e) {
                log.warn("第{}次拉取yarn日志失败", count, e);
                Thread.sleep(sleepTimeMultiplier * 1000L);
                sleepTimeMultiplier = sleepTimeMultiplier * 2;
            }
        }
    }

    @NotNull
    private static Configuration buildConfiguration(Set<String> resources) {
        Configuration configuration = new YarnConfiguration();
        for (String resource : resources) {
            configuration.addResource(new Path(URI.create(resource)));
        }
        return configuration;
    }

    @NotNull
    private static ContainerLogsRequest buildContainerLogsRequest(String appId, String appOwner, String outputDir,
        Set<String> logTypes) {
        ContainerLogsRequest containerLogsRequest = new ContainerLogsRequest();
        containerLogsRequest.setAppId(ApplicationId.fromString(appId));
        containerLogsRequest.setAppOwner(appOwner);
        containerLogsRequest.setLogTypes(logTypes);
        containerLogsRequest.setBytes(Long.MAX_VALUE);
        containerLogsRequest.setOutputLocalDir(outputDir);
        log.info("拉取 yarn 日志请求体 [appId:{}, appOwner:{}, outputLocalDir:{}]", appId, appOwner, outputDir);
        return containerLogsRequest;
    }


    private static void checkAppId(String appId) {
        if (StringUtils.isBlank(appId)) {
            String errorMsg = String.format("appId is required, and can't be empty, present value [%s]", appId);
            throw new IllegalArgumentException(errorMsg);
        }
    }


    private static void checkKerberosParams(String principal, String keytabPath) {
        if (StringUtils.isBlank(principal)) {
            String errorMsg = String.format("principal is required, and can't be empty, present value [%s]", principal);
            throw new IllegalArgumentException(errorMsg);
        }
        if (StringUtils.isBlank(keytabPath)) {
            String errorMsg = String.format("keytabPath is required, and can't be empty, present value [%s]",
                keytabPath);
            throw new IllegalArgumentException(errorMsg);
        }

        File file = new File(keytabPath);
        if (!file.exists()) {
            String errorMsg = String.format("keytabPath does not exist, present value [%s]", keytabPath);
            throw new IllegalArgumentException(errorMsg);
        }
        if (!file.isFile() || !file.getName().endsWith(".keytab")) {
            String errorMsg = String.format("keytabPath should be end with '.keytab', present value [%s]", keytabPath);
            throw new IllegalArgumentException(errorMsg);
        }
    }


    private static void checkOutputDir(String outputDir) {
        if (StringUtils.isBlank(outputDir)) {
            String errorMsg = String.format("outputDir is required, and can't be empty, present value [%s]", outputDir);
            throw new IllegalArgumentException(errorMsg);
        }
    }


    private static void checkResources(Set<String> resources) {
        if (resources == null) {
            String errorMsg = "resources can't be null";
            throw new IllegalArgumentException(errorMsg);
        }
    }
}
