package com.trs.moye.batch.engine.service;

import com.trs.moye.batch.engine.client.SparkStatusClient;
import com.trs.moye.batch.engine.dao.clickhouse.BatchTaskRecordMapper;
import com.trs.moye.batch.engine.entity.BatchTaskRecord;
import com.trs.moye.batch.engine.entity.SparkApplicationInfo;
import com.trs.moye.batch.engine.entity.SparkJobInfo;
import com.trs.moye.batch.engine.enums.BatchTaskStatus;
import com.trs.moye.batch.engine.spark.SparkTaskManager;
import com.trs.moye.batch.engine.utils.SparkStatusMapper;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 任务状态恢复服务
 * 负责处理服务重启后的任务状态恢复和状态不一致修复
 * 
 * 优化升级说明：
 * 1. 集成多模式Spark状态客户端，支持Yarn、Standalone、Local模式
 * 2. 替换基于时间的判断逻辑为基于真实状态的判断
 * 3. 支持网络异常时的降级处理和Provider自动切换
 * 4. 提供更准确的状态恢复机制
 *
 * <AUTHOR>
 * @since 2025/08/29
 */
@Slf4j
@Service
public class TaskStatusRecoveryService {

    @Resource
    private BatchTaskRecordMapper batchTaskRecordMapper;
    
    @Resource
    private SparkStatusClient sparkStatusClient;
    
    @Resource
    private SparkTaskManager sparkTaskManager;

    /**
     * 应用启动完成后自动执行状态恢复
     *
     * @param event 应用启动完成事件
     */
    @EventListener(ApplicationReadyEvent.class)
    @Async
    public void onApplicationReady(ApplicationReadyEvent event) {
        log.info("应用启动完成，开始执行任务状态恢复检查");
        try {
            Map<String, Object> recoveryResult = performStartupRecovery();
            log.info("任务状态恢复完成，结果：{}", recoveryResult);
        } catch (Exception e) {
            log.error("任务状态恢复过程中发生异常", e);
        }
    }

    /**
     * 执行启动时的状态恢复
     *
     * @return 恢复结果统计信息
     */
    public Map<String, Object> performStartupRecovery() {
        Map<String, Object> result = new HashMap<>();
        int totalProcessed = 0;
        int recoveredCount = 0;

        try {
            // 查询所有未结束状态的任务
            List<BatchTaskRecord> activeTaskRecords = findActiveTasks();
            totalProcessed = activeTaskRecords.size();

            log.info("发现 {} 个活跃状态任务，开始基于多模式Spark状态客户端的状态恢复检查", totalProcessed);

            for (BatchTaskRecord record : activeTaskRecords) {
                boolean recovered = recoverSingleTaskStatus(record);
                if (recovered) {
                    recoveredCount++;
                }
            }

            result.put("totalProcessed", totalProcessed);
            result.put("recoveredCount", recoveredCount);
            result.put("recoveryTime", LocalDateTime.now());

        } catch (Exception e) {
            log.error("状态恢复过程中发生异常", e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 查找所有活跃状态的任务
     *
     * @return 活跃任务列表
     */
    private List<BatchTaskRecord> findActiveTasks() {
        try {
            // 使用原生的mapper方法查询活跃任务
            return batchTaskRecordMapper.selectActiveTasksByStatus();
        } catch (Exception e) {
            log.error("查询活跃任务时发生异常", e);
            return new java.util.ArrayList<>();
        }
    }

    /**
     * 恢复单个任务的状态
     *
     * @param record 任务记录
     * @return 是否进行了状态恢复
     */
    private boolean recoverSingleTaskStatus(BatchTaskRecord record) {
        try {
            String executeId = record.getExecuteId();
            BatchTaskStatus currentStatus = record.getStatus();

            // 判断任务是否需要恢复
            boolean needsRecovery = shouldRecoverTask(record);

            if (!needsRecovery) {
                log.debug("任务 {} 状态 {} 无需恢复", executeId, currentStatus);
                return false;
            }

            // 通过Spark REST API获取真实状态进行恢复
            BatchTaskStatus recoveredStatus = determineRecoveryStatus(record);

            log.info("恢复任务状态：executeId={}, 原状态={}, 恢复为={}",
                executeId, currentStatus, recoveredStatus);

            // 更新任务状态
            BatchTaskRecord recoveryRecord = BatchTaskRecord.recoverTaskStatus(
                executeId, recoveredStatus, LocalDateTime.now());

            BatchTaskMonitor.insertOrUpdateRecord(recoveryRecord);

            return true;

        } catch (Exception e) {
            log.error("恢复任务 {} 状态时发生异常", record.getExecuteId(), e);
            return false;
        }
    }

    /**
     * 判断任务是否需要状态恢复
     * 
     * 升级逻辑：
     * 1. 优先通过多模式Spark状态客户端检查真实状态
     * 2. 如果状态客户端不可用，降级为时间判断
     * 3. 综合考虑任务实际执行状态
     *
     * @param record 任务记录
     * @return true表示需要恢复
     */
    private boolean shouldRecoverTask(BatchTaskRecord record) {
        String executeId = record.getExecuteId();
        
        // 首先尝试通过多模式Spark状态客户端检查真实状态
        try {
            String applicationId = getApplicationIdByExecuteId(executeId);
            if (applicationId != null) {
                SparkApplicationInfo applicationInfo = sparkStatusClient.getApplicationInfo(applicationId);
                
                // 如果能获取到应用信息，根据真实状态判断
                if (applicationInfo != null) {
                    boolean needsRecovery = SparkStatusMapper.needsStatusRecovery(applicationInfo);
                    log.debug("基于多模式Spark状态客户端判断任务是否需要恢复: executeId={}, applicationId={}, needsRecovery={}", 
                             executeId, applicationId, needsRecovery);
                    return needsRecovery;
                } else {
                    // 应用信息不存在，可能已结束，需要恢复
                    log.debug("Spark应用信息不存在，任务可能已结束: executeId={}, applicationId={}", 
                             executeId, applicationId);
                    return true;
                }
            }
        } catch (Exception e) {
            log.warn("通过多模式Spark状态客户端检查状态时发生异常，降级为时间判断: executeId={}, error={}", 
                    executeId, e.getMessage());
        }
        
        // 降级处理：使用原有的时间判断逻辑
        return shouldRecoverTaskByTime(record);
    }
    
    /**
     * 基于时间的任务恢复判断（降级方法）
     *
     * @param record 任务记录
     * @return true表示需要恢复
     */
    private boolean shouldRecoverTaskByTime(BatchTaskRecord record) {
        LocalDateTime startTime = record.getStartTime();
        if (startTime == null) {
            log.debug("任务开始时间为空，无需恢复: executeId={}", record.getExecuteId());
            return false;
        }

        // 对于运行超过2小时的任务，认为可能需要状态恢复
        LocalDateTime twoHoursAgo = LocalDateTime.now().minusHours(2);
        boolean needsRecovery = startTime.isBefore(twoHoursAgo);
        log.debug("基于时间判断任务是否需要恢复: executeId={}, startTime={}, needsRecovery={}", 
                 record.getExecuteId(), startTime, needsRecovery);
        return needsRecovery;
    }

    /**
     * 确定恢复后的状态
     * 
     * 升级逻辑：
     * 1. 优先通过多模式Spark状态客户端获取真实状态
     * 2. 结合应用状态和作业状态综合判断
     * 3. 状态客户端不可用时降级为时间判断
     *
     * @param record 任务记录
     * @return 恢复后的状态
     */
    private BatchTaskStatus determineRecoveryStatus(BatchTaskRecord record) {
        String executeId = record.getExecuteId();
        
        // 首先尝试通过多模式Spark状态客户端获取真实状态
        try {
            BatchTaskStatus sparkStatus = checkSparkApplicationStatus(executeId);
            if (sparkStatus != null) {
                log.info("通过多模式Spark状态客户端确定恢复状态: executeId={}, sparkStatus={}", 
                        executeId, sparkStatus);
                return sparkStatus;
            }
        } catch (Exception e) {
            log.warn("通过多模式Spark状态客户端确定状态时发生异常，降级为时间判断: executeId={}, error={}", 
                    executeId, e.getMessage());
        }
        
        // 降级处理：使用原有的时间判断逻辑
        return determineRecoveryStatusByTime(record);
    }
    
    /**
     * 基于时间的状态恢复判断（降级方法）
     *
     * @param record 任务记录
     * @return 恢复后的状态
     */
    private BatchTaskStatus determineRecoveryStatusByTime(BatchTaskRecord record) {
        LocalDateTime startTime = record.getStartTime();
        BatchTaskStatus currentStatus = record.getStatus();

        // 对于长时间运行的任务，默认标记为失败
        if (startTime != null) {
            LocalDateTime fourHoursAgo = LocalDateTime.now().minusHours(4);
            if (startTime.isBefore(fourHoursAgo)) {
                log.warn("任务 {} 运行时间过长（超过4小时），标记为失败", record.getExecuteId());
                return BatchTaskStatus.FAILED;
            }
        }

        // 对于其他情况，根据当前状态决定
        if (currentStatus == BatchTaskStatus.WAITING || currentStatus == BatchTaskStatus.RUNNING) {
            log.debug("基于时间判断，将状态 {} 恢复为 FAILED: executeId={}", 
                     currentStatus, record.getExecuteId());
            return BatchTaskStatus.FAILED;
        }

        return BatchTaskStatus.FAILED;
    }
    
    /**
     * 通过多模式Spark状态客户端检查应用的真实状态
     *
     * @param executeId 执行ID
     * @return 真实的任务状态，如果无法确定则返回null
     */
    private BatchTaskStatus checkSparkApplicationStatus(String executeId) {
        try {
            String applicationId = getApplicationIdByExecuteId(executeId);
            if (applicationId == null) {
                log.debug("无法获取applicationId，可能任务尚未启动或已清理: executeId={}", executeId);
                return BatchTaskStatus.FAILED;
            }
            
            // 获取应用信息
            SparkApplicationInfo applicationInfo = sparkStatusClient.getApplicationInfo(applicationId);
            if (applicationInfo == null) {
                log.debug("Spark应用不存在，标记为失败: executeId={}, applicationId={}", 
                         executeId, applicationId);
                return BatchTaskStatus.FAILED;
            }
            
            // 获取作业信息以更准确判断状态
            List<SparkJobInfo> jobs = sparkStatusClient.getApplicationJobs(applicationId);
            
            // 综合判断应用状态
            BatchTaskStatus finalStatus = SparkStatusMapper.determineOverallStatus(applicationInfo, jobs);
            
            log.debug("Spark状态检查完成: executeId={}, applicationId={}, appState={}, finalStatus={}", 
                     executeId, applicationId, applicationInfo.getState(), finalStatus);
            
            return finalStatus;
            
        } catch (Exception e) {
            log.error("检查Spark应用状态时发生异常: executeId={}, error={}", executeId, e.getMessage());
            return null;
        }
    }
    
    /**
     * 根据executeId获取对应的Spark applicationId
     * 通过SparkTaskManager查找正在运行的应用
     *
     * @param executeId 执行ID
     * @return Spark应用ID，如果找不到则返回null
     */
    private String getApplicationIdByExecuteId(String executeId) {
        try {
            // 从SparkTaskManager获取SparkApplication实例
            var sparkApplication = sparkTaskManager.getApplication(executeId);
            if (sparkApplication != null) {
                String applicationId = sparkApplication.getApplicationId();
                log.debug("从SparkTaskManager获取applicationId: executeId={}, applicationId={}", 
                         executeId, applicationId);
                return applicationId;
            }
            
            log.debug("未找到对应的SparkApplication: executeId={}", executeId);
            return null;
            
        } catch (Exception e) {
            log.debug("获取applicationId时发生异常: executeId={}, error={}", executeId, e.getMessage());
            return null;
        }
    }

    /**
     * 手动触发状态恢复
     *
     * @param executeId 执行ID，如果为null则恢复所有活跃任务
     * @return 恢复结果
     */
    public Map<String, Object> manualRecovery(String executeId) {
        log.info("手动触发状态恢复，executeId: {}", executeId);

        if (executeId != null) {
            // 恢复指定任务
            return recoverSpecificTask(executeId);
        } else {
            // 恢复所有活跃任务
            return performStartupRecovery();
        }
    }

    /**
     * 恢复指定的任务
     *
     * @param executeId 执行ID
     * @return 恢复结果
     */
    private Map<String, Object> recoverSpecificTask(String executeId) {
        Map<String, Object> result = new HashMap<>();

        try {
            BatchTaskRecord record = batchTaskRecordMapper.getByExecuteId(executeId);

            if (record == null) {
                result.put("error", "未找到指定的任务记录: " + executeId);
                return result;
            }

            boolean recovered = recoverSingleTaskStatus(record);
            result.put("executeId", executeId);
            result.put("recovered", recovered);
            result.put("originalStatus", record.getStatus());
            result.put("recoveryTime", LocalDateTime.now());

        } catch (Exception e) {
            log.error("恢复指定任务 {} 状态时发生异常", executeId, e);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取状态恢复统计信息
     *
     * @return 统计信息
     */
    public Map<String, Object> getRecoveryStatistics() {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 统计各状态的任务数量
            Map<BatchTaskStatus, Long> statusCounts = batchTaskRecordMapper.selectStatusCounts();
            stats.put("statusCounts", statusCounts);

            // 统计活跃任务数量
            Long activeCount = batchTaskRecordMapper.selectActiveTasksCount();
            stats.put("activeTasksCount", activeCount);

            stats.put("lastCheckTime", LocalDateTime.now());

        } catch (Exception e) {
            log.error("获取状态恢复统计信息时发生异常", e);
            stats.put("error", e.getMessage());
        }

        return stats;
    }
    
    /**
     * 测试与Spark集群的连接状态
     * 用于诊断和监控
     *
     * @return 连接状态信息
     */
    public Map<String, Object> testSparkConnection() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean isConnected = sparkStatusClient.testConnection();
            result.put("connected", isConnected);
            result.put("testTime", LocalDateTime.now());
            result.put("currentProvider", sparkStatusClient.getCurrentProviderName());
            
            if (isConnected) {
                result.put("message", "Spark集群连接正常");
                log.debug("Spark集群连接测试成功，使用Provider: {}", sparkStatusClient.getCurrentProviderName());
            } else {
                result.put("message", "Spark集群连接失败");
                log.warn("Spark集群连接测试失败，当前Provider: {}", sparkStatusClient.getCurrentProviderName());
            }
            
            // 添加Provider状态摘要
            result.put("providerStatus", sparkStatusClient.getProviderStatusSummary());
            
        } catch (Exception e) {
            result.put("connected", false);
            result.put("error", e.getMessage());
            result.put("message", "连接测试时发生异常");
            result.put("currentProvider", sparkStatusClient.getCurrentProviderName());
            log.error("测试Spark集群连接时发生异常", e);
        }
        
        return result;
    }
}