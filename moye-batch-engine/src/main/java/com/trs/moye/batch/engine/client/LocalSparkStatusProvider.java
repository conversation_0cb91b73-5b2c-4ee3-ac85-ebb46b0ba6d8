package com.trs.moye.batch.engine.client;

import com.trs.moye.batch.engine.entity.SparkApplicationInfo;
import com.trs.moye.batch.engine.entity.SparkJobInfo;
import com.trs.moye.batch.engine.spark.SparkTaskManager;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 本地模式Spark状态提供者
 * 通过SparkTaskManager获取本地运行的Spark应用状态信息
 * 
 * 适用场景：
 * - Local模式：spark://local[*]
 * - 开发环境：本地IDE中运行的Spark应用
 * - 测试环境：单机测试场景
 * 
 * 特点：
 * 1. 直接通过SparkTaskManager获取应用状态
 * 2. 支持本地进程状态监控
 * 3. 提供快速的状态响应
 * 4. 适合开发和测试环境使用
 * 
 * <AUTHOR>
 * @since 2025/08/29
 */
@Slf4j
@Component
public class LocalSparkStatusProvider extends BaseSparkStatusProvider {
    
    @Resource
    private SparkTaskManager sparkTaskManager;
    
    private static final String PROVIDER_NAME = "Local";
    
    @Override
    public String getProviderName() {
        return PROVIDER_NAME;
    }
    
    @Override
    public boolean supports(String deployMode) {
        return "local".equalsIgnoreCase(deployMode) || 
               "local[*]".equalsIgnoreCase(deployMode) ||
               "local[1]".equalsIgnoreCase(deployMode) ||
               "local[2]".equalsIgnoreCase(deployMode) ||
               "local[4]".equalsIgnoreCase(deployMode) ||
               deployMode != null && deployMode.toLowerCase().startsWith("local");
    }
    
    @Override
    public boolean testConnection() {
        try {
            // 测试SparkTaskManager是否可用
            if (sparkTaskManager != null) {
                log.debug("本地模式连接测试: SparkTaskManager可用");
                return true;
            } else {
                log.warn("本地模式连接测试: SparkTaskManager不可用");
                return false;
            }
        } catch (Exception e) {
            log.error("本地模式连接测试失败", e);
            return false;
        }
    }
    
    @Override
    protected SparkApplicationInfo fetchApplicationInfoFromWebUI(String applicationId) {
        try {
            log.debug("获取本地应用信息: appId={}", applicationId);
            
            if (sparkTaskManager == null) {
                log.warn("SparkTaskManager未注入，无法获取本地应用信息");
                return null;
            }
            
            // 通过SparkTaskManager获取应用状态
            // 注意：这里需要根据SparkTaskManager的实际接口进行调整
            SparkApplicationInfo appInfo = createLocalApplicationInfo(applicationId);
            
            if (appInfo != null) {
                log.debug("成功获取本地应用信息: appId={}, state={}", 
                         applicationId, appInfo.getState());
            } else {
                log.debug("本地应用不存在或获取失败: appId={}", applicationId);
            }
            
            return appInfo;
            
        } catch (Exception e) {
            log.error("获取本地应用信息失败: appId={}", applicationId, e);
            return null;
        }
    }
    
    @Override
    protected List<SparkJobInfo> fetchApplicationJobsFromWebUI(String applicationId) {
        try {
            log.debug("获取本地作业信息: appId={}", applicationId);
            
            if (sparkTaskManager == null) {
                log.warn("SparkTaskManager未注入，无法获取本地作业信息");
                return Collections.emptyList();
            }
            
            // 本地模式通常不需要详细的作业信息
            // 可以根据需要实现具体的作业信息获取逻辑
            List<SparkJobInfo> jobs = Collections.emptyList();
            
            log.debug("本地模式作业信息获取完成: appId={}, jobCount={}", applicationId, jobs.size());
            return jobs;
            
        } catch (Exception e) {
            log.error("获取本地作业信息失败: appId={}", applicationId, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 创建本地应用信息对象
     * 根据SparkTaskManager的状态信息构造SparkApplicationInfo
     * 
     * @param applicationId 应用ID
     * @return Spark应用信息
     */
    private SparkApplicationInfo createLocalApplicationInfo(String applicationId) {
        try {
            // 检查应用是否存在于本地任务管理器中
            if (!isApplicationExistsLocally(applicationId)) {
                return null;
            }
            
            SparkApplicationInfo appInfo = new SparkApplicationInfo();
            appInfo.setId(applicationId);
            appInfo.setName("Local Spark Application - " + applicationId);
            appInfo.setUser(System.getProperty("user.name"));
            
            // 获取应用状态
            String state = getLocalApplicationState(applicationId);
            appInfo.setState(state);
            
            // 根据状态设置最终状态
            if ("FINISHED".equals(state)) {
                appInfo.setFinalState("SUCCEEDED");  // 本地模式默认认为完成即成功
            } else if ("FAILED".equals(state)) {
                appInfo.setFinalState("FAILED");
            } else {
                appInfo.setFinalState("UNDEFINED");
            }
            
            log.debug("创建本地应用信息: appId={}, state={}, finalState={}", 
                     applicationId, appInfo.getState(), appInfo.getFinalState());
            
            return appInfo;
            
        } catch (Exception e) {
            log.error("创建本地应用信息失败: appId={}", applicationId, e);
            return null;
        }
    }
    
    /**
     * 检查应用是否在本地存在
     * 
     * @param applicationId 应用ID
     * @return true表示应用存在
     */
    private boolean isApplicationExistsLocally(String applicationId) {
        try {
            // 这里需要根据SparkTaskManager的实际接口实现
            // 示例逻辑：检查是否有对应的任务记录
            return sparkTaskManager != null && applicationId != null;
        } catch (Exception e) {
            log.debug("检查本地应用存在性失败: appId={}", applicationId, e);
            return false;
        }
    }
    
    /**
     * 获取本地应用的状态
     * 
     * @param applicationId 应用ID
     * @return 应用状态字符串
     */
    private String getLocalApplicationState(String applicationId) {
        try {
            // 这里需要根据SparkTaskManager的实际接口实现
            // 示例逻辑：从任务管理器获取状态
            
            // 目前返回默认状态，实际实现时需要根据SparkTaskManager的接口调整
            return "RUNNING";  // 默认状态
            
        } catch (Exception e) {
            log.debug("获取本地应用状态失败: appId={}", applicationId, e);
            return "UNKNOWN";
        }
    }
    
    /**
     * 获取SparkTaskManager实例
     * 
     * @return SparkTaskManager实例
     */
    public SparkTaskManager getSparkTaskManager() {
        return sparkTaskManager;
    }
    
    /**
     * 设置SparkTaskManager实例（主要用于测试）
     * 
     * @param sparkTaskManager SparkTaskManager实例
     */
    public void setSparkTaskManager(SparkTaskManager sparkTaskManager) {
        this.sparkTaskManager = sparkTaskManager;
    }
    
    /**
     * 获取本地模式支持的部署模式列表
     * 
     * @return 支持的部署模式列表
     */
    public String[] getSupportedDeployModes() {
        return new String[]{
            "local", "local[*]", "local[1]", "local[2]", "local[4]", "local[8]"
        };
    }
    
    /**
     * 检查指定的部署模式是否为本地模式
     * 
     * @param deployMode 部署模式
     * @return true表示是本地模式
     */
    public boolean isLocalMode(String deployMode) {
        return supports(deployMode);
    }
    
    /**
     * 获取提供者配置信息摘要
     * 
     * @return 配置信息字符串
     */
    public String getConfigSummary() {
        return String.format("Local模式, SparkTaskManager=%s, Cache=%s", 
                           sparkTaskManager != null ? "可用" : "不可用", 
                           getCacheConfig());
    }
    
    /**
     * 获取缓存配置信息
     * 
     * @return 缓存配置信息的字符串表示
     */
    public String getCacheConfig() {
        return String.format("enabled=%s, expiryMinutes=%d", cacheEnabled, cacheExpiryMinutes);
    }
}