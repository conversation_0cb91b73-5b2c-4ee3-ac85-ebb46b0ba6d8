package com.trs.moye.stream.engine.init;

import com.trs.moye.ability.LocalAbilityScanner;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.enums.AbilityCategory;
import com.trs.moye.ability.enums.AbilityTestStatus;
import com.trs.moye.ability.enums.AbilityType;
import com.trs.moye.ability.enums.AbilityUpdateStatus;
import com.trs.moye.base.data.model.dao.OperatorBusinessCategoryMapper;
import com.trs.moye.base.data.model.entity.OperatorBusinessCategory;
import com.trs.moye.stream.engine.dao.AbilityMapper;
import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 能力初始化
 *
 * <AUTHOR>
 * @since 2025/03/04 16:07:58
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AbilityInitializer implements CommandLineRunner {

    private final AbilityMapper abilityMapper;
    private final OperatorBusinessCategoryMapper categoryMapper;
    private static final String BASE_ABILITY_PACKAGE = "com.trs.moye.ability.base";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void run(String... args) {
        log.info("开始扫描本地基础能力包: {}", BASE_ABILITY_PACKAGE);
        List<Ability> localAbilities = LocalAbilityScanner.scanPackage(BASE_ABILITY_PACKAGE);
        if (CollectionUtils.isEmpty(localAbilities)) {
            log.warn("未发现本地基础能力，同步过程终止");
            return;
        }
        log.info("扫描完成，共发现 {} 个本地能力", localAbilities.size());

        // 加载 details.json 文件
        Map<String, String> detailsMap = loadDetailsFromMdFiles();

        // 处理能力分类
        Map<String, Integer> categoryIdMap = handleCategories(localAbilities);

        // 设置默认值，包括分类ID
        localAbilities.forEach(ability -> {
            setDefaultValues(ability);
            // 如果有分类信息，则设置对应的分类ID
            if (StringUtils.hasText(ability.getCategoryEnAbbr()) && categoryIdMap.containsKey(
                ability.getCategoryEnAbbr())) {
                ability.setOperatorCategoryId(categoryIdMap.get(ability.getCategoryEnAbbr()));
            }
            // 更新 details 字段
            if (detailsMap.containsKey(ability.getEnName())) {
                ability.setDetails(detailsMap.get(ability.getEnName()));
            }
        });

        // 批量查询数据库已有能力
        Map<String, Ability> dbAbilityMap = getExistingAbilities(localAbilities);
        int inserted = 0;
        int updated = 0;
        // 遍历本地能力列表，进行新增或更新
        for (Ability localAbility : localAbilities) {
            String abilityKey = getAbilityKey(localAbility);
            Ability dbAbility = dbAbilityMap.get(abilityKey);
            if (Objects.isNull(dbAbility)) {
                abilityMapper.insert(localAbility);
                log.debug("新增能力: [{}] {}", localAbility.getPath(), localAbility.getEnName());
                inserted++;
            } else {
                // 如果能力的输入输出参数和请求配置没有变化，则不更新
                if (Objects.equals(localAbility.getInputSchema(), dbAbility.getInputSchema())
                    && Objects.equals(localAbility.getOutputSchema(), dbAbility.getOutputSchema())
                    && Objects.equals(localAbility.getHttpRequestConfig(), dbAbility.getHttpRequestConfig())
                    && Objects.equals(localAbility.getOperatorCategoryId(), dbAbility.getOperatorCategoryId())
                    // 新增详情字段不同也要更新
                    && Objects.equals(localAbility.getDetails(), dbAbility.getDetails())) {
                    continue;
                }
                updateExistingAbility(localAbility, dbAbility);
                updated++;
            }
        }
        log.info("能力同步完成，新增: {} 个，更新: {} 个", inserted, updated);
    }

    /**
     * 处理能力分类
     *
     * @param abilities 能力
     * @return {@link Map }<{@link String }, {@link Integer }>
     * <AUTHOR>
     * @since 2025/04/07 17:20:25
     */
    private Map<String, Integer> handleCategories(List<Ability> abilities) {
        Map<String, OperatorBusinessCategory> categoriesMap = collectCategories(abilities);

        if (categoriesMap.isEmpty()) {
            return Collections.emptyMap();
        }

        // 查询已有分类
        List<String> enAbbrs = categoriesMap.keySet().stream().toList();
        List<OperatorBusinessCategory> existingCategories = categoryMapper.selectByEnAbbrs(enAbbrs);
        Map<String, OperatorBusinessCategory> existingCategoryMap = existingCategories.stream()
            .collect(Collectors.toMap(OperatorBusinessCategory::getEnAbbr, Function.identity()));

        return processCategories(categoriesMap, existingCategoryMap);
    }

    private Map<String, OperatorBusinessCategory> collectCategories(List<Ability> abilities) {
        Map<String, OperatorBusinessCategory> categoriesMap = new HashMap<>();

        // 收集所有分类信息
        for (Ability ability : abilities) {
            if (!StringUtils.hasText(ability.getCategoryEnAbbr())) {
                continue;
            }

            OperatorBusinessCategory category = new OperatorBusinessCategory();
            category.setEnAbbr(ability.getCategoryEnAbbr());
            category.setZhName(ability.getCategoryName());
            category.setDescription(ability.getCategoryDescription());
            category.setCreateBy(1);
            category.setUpdateBy(1);
            category.setCreateTime(LocalDateTime.now());
            category.setUpdateTime(LocalDateTime.now());
            if (ability.getType().equals(AbilityType.NLP)) {
                //nlp算子
                ability.setOperatorCategoryId(AbilityCategory.NLP.getId());
                ability.setIconName(AbilityCategory.NLP.getIconName());
                category.setPid(AbilityCategory.NLP.getId());
            } else {
                // 默认为1（通用算子）
                ability.setOperatorCategoryId(AbilityCategory.GENERAL.getId());
                category.setPid(AbilityCategory.GENERAL.getId());
            }
            categoriesMap.put(ability.getCategoryEnAbbr(), category);
        }

        return categoriesMap;
    }

    private Map<String, Integer> processCategories(
        Map<String, OperatorBusinessCategory> categoriesMap,
        Map<String, OperatorBusinessCategory> existingCategoryMap) {

        Map<String, Integer> result = new HashMap<>();

        for (Map.Entry<String, OperatorBusinessCategory> entry : categoriesMap.entrySet()) {
            String enAbbr = entry.getKey();
            OperatorBusinessCategory category = entry.getValue();

            if (existingCategoryMap.containsKey(enAbbr)) {
                // 已存在的分类
                processExistingCategory(enAbbr, category, existingCategoryMap.get(enAbbr), result);
            } else {
                // 新分类，需要插入
                categoryMapper.insert(category);
                result.put(enAbbr, category.getId());
                log.debug("新增分类: [{}] {}", enAbbr, category.getZhName());
            }
        }

        return result;
    }

    private void processExistingCategory(
        String enAbbr,
        OperatorBusinessCategory category,
        OperatorBusinessCategory existingCategory,
        Map<String, Integer> result) {

        // 记录ID
        result.put(enAbbr, existingCategory.getId());

        // 检查是否需要更新
        if (!Objects.equals(category.getZhName(), existingCategory.getZhName())
            || !Objects.equals(category.getDescription(), existingCategory.getDescription())
            || !Objects.equals(category.getPid(), existingCategory.getPid())
            || !Objects.equals(category.getEnAbbr(), existingCategory.getEnAbbr())) {
            existingCategory.setZhName(category.getZhName());
            existingCategory.setDescription(category.getDescription());
            existingCategory.setPid(category.getPid());
            existingCategory.setEnAbbr(category.getEnAbbr());
            categoryMapper.updateById(existingCategory);
            log.debug("更新分类: [{}] {}", enAbbr, category.getZhName());
        }
    }

    /**
     * 批量查询数据库已有能力 使用路径和英文名的组合作为键，以处理同一路径下不同名称的能力
     *
     * @param localAbilities 本地能力列表
     * @return 数据库中存在的能力映射（键为"路径:英文名"）
     */
    private Map<String, Ability> getExistingAbilities(List<Ability> localAbilities) {
        List<String> paths = localAbilities.stream()
            .map(Ability::getPath)
            .distinct()
            .toList();
        if (CollectionUtils.isEmpty(paths)) {
            return Collections.emptyMap();
        }

        // 查询所有相关路径的能力
        List<Ability> dbAbilities = abilityMapper.selectByPaths(paths);

        // 使用路径:英文名作为键创建映射
        return dbAbilities.stream()
            .collect(Collectors.toMap(
                this::getAbilityKey,
                Function.identity(),
                // 如果有重复键（理论上不应该发生），保留第一个
                (existing, replacement) -> existing
            ));
    }

    /**
     * 生成能力的唯一键（路径:英文名）
     *
     * @param ability 能力对象
     * @return 唯一键
     */
    private String getAbilityKey(Ability ability) {
        return ability.getPath() + ":" + ability.getEnName();
    }

    private void updateExistingAbility(Ability localAbility, Ability dbAbility) {
        localAbility.setId(dbAbility.getId());

        mergeProperties(localAbility, dbAbility);
        abilityMapper.updateById(localAbility);
        log.debug("更新能力: [{}] {}", localAbility.getPath(), localAbility.getEnName());
    }

    private void setDefaultValues(Ability ability) {
        //如果没有中文名，用英文名作为中文名
        if (!StringUtils.hasText(ability.getZhName())) {
            ability.setZhName(ability.getEnName());
        }
        ability.setUpdateStatus(AbilityUpdateStatus.UPDATED);
        ability.setTestStatus(AbilityTestStatus.UNTESTED);
        ability.setCreateBy(1);
        ability.setUpdateBy(1);
        ability.setCreateTime(LocalDateTime.now());
        ability.setUpdateTime(LocalDateTime.now());
    }

    private void mergeProperties(Ability localAbility, Ability dbAbility) {
        localAbility.setZhName(StringUtils.hasText(localAbility.getZhName())
            ? localAbility.getZhName() : dbAbility.getZhName());
        localAbility.setOperatorCategoryId(Objects.requireNonNullElseGet(
            localAbility.getOperatorCategoryId(),
            dbAbility::getOperatorCategoryId));
        localAbility.setIconName(Objects.requireNonNullElse(
            localAbility.getIconName(),
            dbAbility.getIconName()));
        localAbility.setTestStatus(Objects.requireNonNullElse(
            localAbility.getTestStatus(),
            dbAbility.getTestStatus()));
        localAbility.setUpdateStatus(AbilityUpdateStatus.UPDATED);
        localAbility.setUpdateBy(Objects.requireNonNullElse(
            localAbility.getUpdateBy(),
            dbAbility.getUpdateBy()));
        localAbility.setCreateTime(dbAbility.getCreateTime());
        localAbility.setUpdateTime(LocalDateTime.now());
        // 若 localAbility.getDetails() 和 dbAbility.getDetails() 都为 null，设置默认值 ""
        localAbility.setDetails(Objects.requireNonNullElseGet(
            localAbility.getDetails(),
            () -> Objects.requireNonNullElse(dbAbility.getDetails(), "")
        ));
    }

    /**
     * 从 resources/md 目录下加载 .md 文件
     *
     * @return 键值对映射，键为文件名，值为文件内容
     */
    private Map<String, String> loadDetailsFromMdFiles() {
        Map<String, String> resultMap = new HashMap<>();
        try {
            // 获取 md 目录的 URL
            ClassLoader classLoader = getClass().getClassLoader();
            // 首先尝试获取 md 目录的 URL
            URL mdDirUrl = classLoader.getResource("md");
            if (mdDirUrl == null) {
                log.warn("MD directory not found in classpath");
                return resultMap;
            }

            log.info("resourceProtocol : {}", mdDirUrl.getProtocol());

            // 如果是 jar 协议，说明在 JAR 包内
            if ("jar".equals(mdDirUrl.getProtocol())) {
                processJarResources(classLoader, resultMap);
            } else {
                // 如果是 file 协议，说明在文件系统中（开发环境）
                File mdDirectory = new File(mdDirUrl.toURI());
                if (mdDirectory.isDirectory()) {
                    File[] files = mdDirectory.listFiles((dir, name) -> name.endsWith(".md"));
                    if (files != null) {
                        for (File file : files) {
                            String key = file.getName().replace(".md", "");
                            String content = Files.readString(file.toPath(), StandardCharsets.UTF_8);
                            resultMap.put(key, content);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Failed to load files from the md directory", e);
        }
        return resultMap;
    }

    private void processJarResources(ClassLoader classLoader, Map<String, String> resultMap) {
        try {
            // 获取包含资源的 JAR 文件
            String classPath = getClass().getProtectionDomain().getCodeSource().getLocation().toString();
            String jarPath = classPath.replaceFirst("file:", "").split("!")[0];

            log.info("classPath : {},  jarPath: {}", classPath, jarPath);
            try (JarFile jarFile = new JarFile(new File(jarPath))) {
                Enumeration<JarEntry> entries = jarFile.entries();

                while (entries.hasMoreElements()) {
                    JarEntry entry = entries.nextElement();
                    String name = entry.getName();

                    // 查找 md 目录下的 .md 文件
                    if (name.startsWith("md/") && name.endsWith(".md") && !entry.isDirectory()) {
                        try (InputStream inputStream = classLoader.getResourceAsStream(name)) {
                            if (inputStream != null) {
                                String filename = name.substring(name.lastIndexOf("/") + 1);
                                String key = filename.replace(".md", "");
                                String content = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
                                resultMap.put(key, content);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("Failed to process jar resources", e);
        }
    }
}
