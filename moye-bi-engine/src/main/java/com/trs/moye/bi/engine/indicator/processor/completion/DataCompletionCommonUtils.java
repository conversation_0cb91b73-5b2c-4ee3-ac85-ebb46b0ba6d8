package com.trs.moye.bi.engine.indicator.processor.completion;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 数据补齐通用工具类
 * <p>
 * 提供数据补齐功能中的通用方法，避免代码重复 支持时间序列数据补齐和固定排序顺序数据补齐功能的共同需求
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/29
 */
public class DataCompletionCommonUtils {

    /**
     * 维度分隔符
     */
    public static final String DIMENSION_SEPARATOR = "_";

    /**
     * 空值字符串表示
     */
    public static final String NULL_VALUE_STRING = "null";

    /**
     * 按维度字段对数据进行分组
     * <p>
     * 统一的维度分组逻辑，确保维度键生成的一致性 支持空数据和空维度字段的处理
     * </p>
     *
     * @param data       需要分组的数据列表
     * @param dimsFields 维度字段列表
     * @return 以维度组合键为key，数据列表为value的分组Map
     */
    public static Map<String, List<Map<String, Object>>> groupDataByDimensions(
        List<Map<String, Object>> data, List<String> dimsFields) {

        if (isDataEmpty(data) || CollectionUtils.isEmpty(dimsFields)) {
            return new HashMap<>();
        }

        return data.stream().collect(Collectors.groupingBy(row ->
            generateDimensionKey(row, dimsFields)));
    }

    /**
     * 生成维度组合键
     * <p>
     * 统一的维度键生成逻辑，确保在不同补齐场景中维度键的一致性 使用统一的null值处理和分隔符
     * </p>
     *
     * @param row        数据行
     * @param dimsFields 维度字段列表
     * @return 维度组合键
     */
    public static String generateDimensionKey(Map<String, Object> row, List<String> dimsFields) {
        if (CollectionUtils.isEmpty(dimsFields)) {
            return "";
        }

        return dimsFields.stream()
            .map(field -> {
                Object value = row.get(field);
                return value != null ? value.toString() : NULL_VALUE_STRING;
            })
            .collect(Collectors.joining(DIMENSION_SEPARATOR));
    }

    /**
     * 检查数据是否为空
     * <p>
     * 统一的数据空值检查逻辑
     * </p>
     *
     * @param data 数据列表
     * @return 是否为空
     */
    public static boolean isDataEmpty(List<Map<String, Object>> data) {
        return data == null || data.isEmpty();
    }

    /**
     * 从维度键设置维度字段值
     * <p>
     * 统一的维度字段设置逻辑，支持从维度键解析并设置到记录中
     * </p>
     *
     * @param record     目标记录
     * @param dimKey     维度键
     * @param dimsFields 维度字段列表
     */
    public static void setDimensionFieldsFromKey(Map<String, Object> record,
        String dimKey,
        List<String> dimsFields) {
        if (record == null || dimKey == null || CollectionUtils.isEmpty(dimsFields)) {
            return;
        }

        String[] dimValues = dimKey.split(DIMENSION_SEPARATOR);
        for (int i = 0; i < dimsFields.size() && i < dimValues.length; i++) {
            String value = dimValues[i];
            if (!NULL_VALUE_STRING.equals(value)) {
                record.put(dimsFields.get(i), value);
            }
        }
    }

    /**
     * 创建字段排除集合
     * <p>
     * 创建包含维度字段和时间字段的排除集合，用于字段处理时的过滤
     * </p>
     *
     * @param dimsFields     维度字段列表
     * @param startTimeField 开始时间字段名
     * @param endTimeField   结束时间字段名
     * @return 字段排除集合
     */
    public static Set<String> createExcludeFieldsSet(List<String> dimsFields,
        String startTimeField,
        String endTimeField) {
        Set<String> excludeFields = new HashSet<>();

        if (CollectionUtils.isNotEmpty(dimsFields)) {
            excludeFields.addAll(dimsFields);
        }

        if (startTimeField != null) {
            excludeFields.add(startTimeField);
        }

        if (endTimeField != null) {
            excludeFields.add(endTimeField);
        }

        return excludeFields;
    }

    /**
     * 收集数据中指定字段的现有值
     * <p>
     * 从数据列表中提取指定字段的所有非空值，统一处理字符串转换和null值过滤
     * </p>
     *
     * @param data      数据列表
     * @param fieldName 要收集值的字段名
     * @return 包含所有非空字段值的集合
     */
    public static Set<String> collectExistingFieldValues(List<Map<String, Object>> data, String fieldName) {
        if (isDataEmpty(data) || fieldName == null) {
            return new HashSet<>();
        }

        return data.stream()
            .map(row -> row.get(fieldName))
            .filter(Objects::nonNull)
            .map(Object::toString)
            .filter(stringValue -> !NULL_VALUE_STRING.equals(stringValue) && !stringValue.isEmpty())
            .collect(Collectors.toSet());
    }

    /**
     * 找出固定顺序中缺失的值
     * <p>
     * 对比固定顺序列表和现有值集合，找出缺失的值
     * </p>
     *
     * @param fixedOrder     固定顺序值列表
     * @param existingValues 现有值集合
     * @return 按固定顺序排列的缺失值列表
     */
    public static List<String> findMissingValuesInOrder(List<String> fixedOrder, Set<String> existingValues) {
        if (CollectionUtils.isEmpty(fixedOrder)) {
            return new ArrayList<>();
        }

        if (existingValues == null) {
            existingValues = new HashSet<>();
        }

        Set<String> finalExistingValues = existingValues;
        return fixedOrder.stream()
            .filter(value -> value != null && !finalExistingValues.contains(value))
            .collect(Collectors.toList());
    }

    /**
     * 收集现有值并找出缺失值的组合方法
     *
     * @param data       数据列表
     * @param fieldName  要分析的字段名
     * @param fixedOrder 固定顺序值列表
     * @return 按固定顺序排列的缺失值列表
     */
    public static List<String> findMissingFixedOrderValues(List<Map<String, Object>> data,
        String fieldName,
        List<String> fixedOrder) {
        Set<String> existingValues = collectExistingFieldValues(data, fieldName);
        return findMissingValuesInOrder(fixedOrder, existingValues);
    }
}
