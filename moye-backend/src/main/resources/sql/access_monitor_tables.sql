-- 接入监控相关数据库表结构
-- 创建时间: 2025-01-27
-- 作者: zhao.chengwei

-- 接入监控记录表
CREATE TABLE access_monitor_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    record_id VARCHAR(64) NOT NULL COMMENT '记录ID',
    execute_id VARCHAR(64) NOT NULL COMMENT '执行ID',
    data_model_id INT NOT NULL COMMENT '数据模型ID',
    source_name VARCHAR(255) NOT NULL COMMENT '数据源名称',
    source_type VARCHAR(50) NOT NULL COMMENT '数据源类型',
    data_count BIGINT NOT NULL DEFAULT 0 COMMENT '接入数据量',
    access_time BIGINT NOT NULL COMMENT '接入时间戳',
    access_duration BIGINT NOT NULL DEFAULT 0 COMMENT '接入耗时(毫秒)',
    start_time BIGINT COMMENT '开始时间戳',
    end_time BIGINT COMMENT '结束时间戳',
    is_error BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否错误',
    error_msg TEXT COMMENT '错误信息',
    error_msg_read_flag TINYINT NOT NULL DEFAULT 0 COMMENT '错误读取标志(0:未读,1:已读)',
    connection_info TEXT COMMENT '连接信息JSON',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_data_model_time (data_model_id, access_time),
    INDEX idx_execute_id (execute_id),
    INDEX idx_error_flag (error_msg_read_flag),
    INDEX idx_source_name (source_name),
    INDEX idx_access_time (access_time),
    UNIQUE KEY uk_record_id (record_id)
) COMMENT '接入监控记录表';

-- 执行日志表
CREATE TABLE execution_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    execute_id VARCHAR(64) NOT NULL COMMENT '执行ID',
    log_level VARCHAR(20) NOT NULL COMMENT '日志级别',
    log_message TEXT NOT NULL COMMENT '日志消息',
    log_time BIGINT NOT NULL COMMENT '日志时间戳',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_execute_id (execute_id),
    INDEX idx_log_time (log_time)
) COMMENT '执行日志表';

-- 监控配置表
CREATE TABLE access_monitor_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    data_model_id INT NOT NULL COMMENT '数据模型ID',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用监控',
    refresh_interval INT NOT NULL DEFAULT 30 COMMENT '刷新间隔(秒)',
    error_count_threshold INT NOT NULL DEFAULT 10 COMMENT '错误数量阈值',
    reconnect_count_threshold INT NOT NULL DEFAULT 5 COMMENT '重连次数阈值',
    speed_threshold DECIMAL(10,2) NOT NULL DEFAULT 0.1 COMMENT '速度阈值',
    enable_email BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否启用邮件通知',
    enable_sms BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否启用短信通知',
    email_recipients TEXT COMMENT '邮件接收人列表',
    sms_recipients TEXT COMMENT '短信接收人列表',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_data_model (data_model_id)
) COMMENT '接入监控配置表';

-- 连接状态记录表
CREATE TABLE connection_status_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    data_model_id INT NOT NULL COMMENT '数据模型ID',
    source_name VARCHAR(255) NOT NULL COMMENT '数据源名称',
    is_connected BOOLEAN NOT NULL COMMENT '是否连接',
    reconnect_count INT NOT NULL DEFAULT 0 COMMENT '重连次数',
    last_connect_time BIGINT COMMENT '最后连接时间',
    connection_duration BIGINT NOT NULL DEFAULT 0 COMMENT '连接持续时间(毫秒)',
    error_count INT NOT NULL DEFAULT 0 COMMENT '错误次数',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_data_model_source (data_model_id, source_name),
    INDEX idx_last_connect_time (last_connect_time)
) COMMENT '连接状态记录表';
