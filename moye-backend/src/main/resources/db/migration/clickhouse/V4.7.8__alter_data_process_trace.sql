-- moye_v4.data_process_trace_new definition

-- 移除原表
DROP TABLE IF EXISTS data_process_trace;
-- 创建新表
CREATE TABLE data_process_trace
(
    `record_id` Int64 COMMENT '记录id',
    `execute_id` Int64 COMMENT '执行id',
    `process_id` Int64 COMMENT '算子处理id',
    `msg_title` String COMMENT '消息标题',
    `data_model_id` Int64 COMMENT '建模id',
    `data_model_name` String COMMENT '建模名称',
    `operator_id` Int64 COMMENT '算子id',
    `processing_name` String COMMENT '算子名称',
    `processing_time` Int64 COMMENT '算子执行时长ms',
    `condition` String COMMENT '过滤条件',
    `condition_result` Int8 COMMENT '过滤条件结果',
    `input` String COMMENT '输入参数' CODEC(ZSTD(1)) TTL storage_time + toIntervalDay(15),
    `output` String COMMENT '输出结果' CODEC(ZSTD(1)) TTL storage_time + toIntervalDay(15),
    `storage_time` DateTime COMMENT '入库时间',
    `start_time`   DateTime COMMENT '开始时间',
    `end_time`     DateTime COMMENT '结束时间',
    `processing_order` Int64 COMMENT '执行顺序',
    `is_error` Int8 DEFAULT 0 COMMENT '是否报错',
    `error_msg` String COMMENT '报错信息' CODEC(ZSTD(1)),
    `pod_ip` String COMMENT '处理节点ip',
    `ability_id` Int32 COMMENT '能力id',
    `storage_id` Int32 COMMENT '存储点id'
)
    ENGINE = MergeTree
    PARTITION BY (toYYYYMMDD(storage_time), is_error)
    ORDER BY (data_model_id,storage_time,record_id)
SETTINGS index_granularity = 8192;


-- 创建kafka引擎表
DROP TABLE IF EXISTS data_process_trace_kafka_engine;
CREATE TABLE data_process_trace_kafka_engine
(

    `recordId` Int64,

    `executeId` Int64,

    `processId` Int64,

    `msgTitle` String,

    `dataModelId` Int64,

    `dataModelName` String,

    `operatorId` Int64,

    `processingName` String,

    `processingTime` Int64,

    `condition` String,

    `conditionResult` Int8,

    `input` String,

    `output` String,

    `storageTime` DateTime,

    `startTime` DateTime,

    `endTime` DateTime,

    `processingOrder` Int64,

    `isError` Int8,

    `errorMsg` String,

    `podIp` String,

    `abilityId` Int32,

    `storageId` Int32
)
    ENGINE = Kafka
    SETTINGS kafka_broker_list = 'kafka-svc:9092',
 kafka_topic_list = 'data_process_trace_topic',
 kafka_group_name = 'data_process_trace_group',
 kafka_format = 'JSONStringsEachRow',
 kafka_skip_broken_messages = 20000,
 kafka_num_consumers = 4;


-- 创建物化视图
drop view if exists v_data_process_trace;
CREATE MATERIALIZED VIEW data_process_trace_view TO data_process_trace
(

    `record_id` Int64,

    `execute_id` Int64,

    `process_id` Int64,

    `msg_title` String,

    `data_model_id` Int64,

    `data_model_name` String,

    `operator_id` Int64,

    `processing_name` String,

    `processing_time` Int64,

    `condition` String,

    `condition_result` Int8,

    `input` String,

    `output` String,

    `storage_time` DateTime,

    `start_time` DateTime,

    `end_time` DateTime,

    `processing_order` Int64,

    `is_error` Int8,

    `error_msg` String,

    `pod_ip` String,

    `ability_id` Int32,

    `storage_id` Int32
) AS
SELECT
    recordId AS record_id,

    processId AS process_id,

    executeId AS execute_id,

    msgTitle AS msg_title,

    dataModelId AS data_model_id,

    dataModelName AS data_model_name,

    input AS input,

    output AS output,

    storageTime AS storage_time,

    startTime AS start_time,

    endTime AS end_time,

    processingTime AS processing_time,

    processingName AS processing_name,

    operatorId AS operator_id,

    processingOrder AS processing_order,

    isError AS is_error,

    errorMsg AS error_msg,

    podIp AS pod_ip,

    `condition` AS `condition`,

    conditionResult AS condition_result,

    abilityId AS ability_id,

    storageId AS storage_id
FROM data_process_trace_kafka_engine;