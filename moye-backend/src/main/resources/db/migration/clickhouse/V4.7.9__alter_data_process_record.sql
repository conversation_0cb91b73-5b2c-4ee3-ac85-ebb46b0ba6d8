DROP TABLE IF EXISTS data_process_record;
CREATE TABLE data_process_record
(
    `record_id` Int64 COMMENT '记录id',
    `execute_id` Int64 COMMENT '执行id',
    `data_model_id` Int64 COMMENT '建模id',
    `data_model_name` String COMMENT '建模名称',
    `msg_title` String COMMENT '消息标题',
    `total_operator_count` Int64 COMMENT '算子总数',
    `executed_operator_count` Int64 COMMENT '执行算子数',
    `msg_content` String COMMENT '消息内容' CODEC(ZSTD(1)) TTL storage_time + toIntervalDay(15),
    `storage_time` DateTime COMMENT '入库时间',
    `start_time`   DateTime COMMENT '开始时间',
    `end_time`     DateTime COMMENT '结束时间',
    `processing_time` Int64 COMMENT '执行时长ms',
    `is_error` Int8 DEFAULT 0 COMMENT '是否报错',
    `is_rerun` Int8 DEFAULT 0 COMMENT '是否重跑',
    `error_msg_read_flag` Int8 DEFAULT 0 COMMENT '是否已读',
    `pod_ip` String COMMENT '节点ip'
)
    ENGINE = MergeTree
    PARTITION BY (toYYYYMMDD(storage_time), is_error)
    ORDER BY (data_model_id, storage_time, record_id)
SETTINGS index_granularity = 8192;


-- moye_v4.data_process_record_kafka_engine_new definition
DROP TABLE IF EXISTS data_process_record_kafka_engine;
CREATE TABLE data_process_record_kafka_engine
(

    `recordId` Int64,

    `executeId` Int64,

    `msgTitle` String,

    `totalOperatorCount` Int64,

    `dataModelId` Int64,

    `dataModelName` String,

    `storageTime` DateTime,

    `startTime` DateTime,

    `endTime` DateTime,

    `processingTime` Int64,

    `isError` Int8,

    `executedOperatorCount` Int64,

    `msgContent` String,

    `isRerun` Int8,

    `podIp` String
)
    ENGINE = Kafka
    SETTINGS kafka_broker_list = 'kafka-svc:9092',
 kafka_topic_list = 'data_process_record_topic',
 kafka_group_name = 'data_process_record_group',
 kafka_format = 'JSONStringsEachRow',
 kafka_skip_broken_messages = 20000,
 kafka_num_consumers = 4;

-- moye_v4.data_process_record_view_new source
drop view if exists data_process_record_view;
CREATE MATERIALIZED VIEW data_process_record_view TO data_process_record
(

    `record_id` Int64,

    `execute_id` Int64,

    `data_model_id` Int64,

    `data_model_name` String,

    `msg_title` String,

    `total_operator_count` Int64,

    `executed_operator_count` Int64,

    `msg_content` String,

    `storage_time` DateTime,

    `start_time` DateTime,

    `end_time` DateTime,

    `processing_time` Int64,

    `is_error` Int8,

    `is_rerun` Int8,

    `pod_ip` String
) AS
SELECT
    recordId AS record_id,

    executeId AS execute_id,

    msgTitle AS msg_title,

    dataModelId AS data_model_id,

    dataModelName AS data_model_name,

    msgContent AS msg_content,

    storageTime AS storage_time,

    startTime AS start_time,

    endTime AS end_time,

    processingTime AS processing_time,

    isError AS is_error,

    podIp AS pod_ip,

    totalOperatorCount AS total_operator_count,

    executedOperatorCount AS executed_operator_count,

    isRerun AS is_rerun
FROM data_process_record_kafka_engine;