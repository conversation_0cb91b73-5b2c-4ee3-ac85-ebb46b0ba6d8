

CREATE TABLE IF NOT EXISTS stream_access_trend_metrics
(
    `id` Int64,
    `data_model_id` Nullable(Int32) COMMENT '要素库ID',
    `source_model_id` Nullable(Int32) COMMENT '来源建模ID',
    `source_storage_id` Nullable(Int32) COMMENT '来源存储点id',
    `topic` Nullable(String) COMMENT '消息主题',
    `group` Nullable(String) COMMENT '消费者分组',
    `monitor_time` DateTime64(3) COMMENT '监控时间',
    `last_monitor_time` Nullable(DateTime64(3)) COMMENT '上一次监控时间',
    `storage_time` DateTime64(3) COMMENT '入库时间',
    `total_count` Nullable(Int64) COMMENT '总量',
    `lag_count` Nullable(Int64) COMMENT '积压量',
    `period_count` Nullable(Int64) COMMENT '周期量',
    `tps` Nullable(Decimal(20,10)) COMMENT 'TPS',
    `is_error` Nullable(Int8) COMMENT '是否异常',
    `remark` Nullable(String) COMMENT '备注'
)
ENGINE = MergeTree
PARTITION BY toYYYYMMDD(monitor_time)
ORDER BY (id, monitor_time)
SETTINGS index_granularity = 8192
COMMENT '流处理接入趋势图指标表';



CREATE TABLE IF NOT EXISTS stream_process_trend_metrics
(
    `id` Int64,
    `data_model_id` Nullable(Int32) COMMENT '建模ID',
    `topic` Nullable(String) COMMENT '消息主题',
    `group` Nullable(String) COMMENT '消费者分组',
    `monitor_time` DateTime64(3) COMMENT '监控时间',
    `last_monitor_time` Nullable(DateTime64(3)) COMMENT '上一次监控时间',
    `storage_time` DateTime64(3) COMMENT '入库时间',
    `total_count` Nullable(Int64) COMMENT '总量',
    `lag_count` Nullable(Int64) COMMENT '积压量',
    `period_count` Nullable(Int64) COMMENT '周期量',
    `tps` Nullable(Decimal(20,10)) COMMENT 'TPS',
    `is_error` Nullable(Int8) COMMENT '是否异常',
    `remark` Nullable(String) COMMENT '备注'
)
ENGINE = MergeTree
PARTITION BY toYYYYMMDD(monitor_time)
ORDER BY (id, monitor_time)
SETTINGS index_granularity = 8192
COMMENT '流处理处理趋势图指标表';



CREATE TABLE IF NOT EXISTS stream_storage_trend_metrics
(
    `id` Int64,
    `data_model_id` Nullable(Int32) COMMENT '建模ID',
    `storage_id` Nullable(Int32) COMMENT '存储点id',
    `topic` Nullable(String) COMMENT '消息主题',
    `group` Nullable(String) COMMENT '消费者分组',
    `monitor_time` DateTime64(3) COMMENT '监控时间',
    `last_monitor_time` Nullable(DateTime64(3)) COMMENT '上一次监控时间',
    `storage_time` DateTime64(3) COMMENT '入库时间',
    `total_count` Nullable(Int64) COMMENT '总量',
    `lag_count` Nullable(Int64) COMMENT '积压量',
    `period_count` Nullable(Int64) COMMENT '周期量',
    `tps` Nullable(Decimal(20,10)) COMMENT 'TPS',
    `is_error` Nullable(Int8) COMMENT '是否异常',
    `remark` Nullable(String) COMMENT '备注'
)
ENGINE = MergeTree
PARTITION BY toYYYYMMDD(monitor_time)
ORDER BY (id, monitor_time)
SETTINGS index_granularity = 8192
COMMENT '流处理存储趋势图指标表';


CREATE TABLE IF NOT EXISTS stream_access_realtime_metrics
(
    `data_model_id` Nullable(Int32) COMMENT '建模ID',
    `time` Date COMMENT '时间',
    `access_read_count` Nullable(Int64) COMMENT '接入读取量',
    `access_read_error_count` Nullable(Int64) COMMENT '接入读取异常量',
    `access_write_count` Nullable(Int64) COMMENT '接入写入量',
    `access_write_error_count` Nullable(Int64) COMMENT '接入写入异常量'
)
ENGINE = MergeTree
PARTITION BY toYYYYMM(time)
ORDER BY (time)
SETTINGS index_granularity = 8192
COMMENT '流处理数据接入实时指标表';


CREATE TABLE IF NOT EXISTS stream_process_realtime_metrics
(
    `data_model_id` Nullable(Int32) COMMENT '建模ID',
    `time` Date COMMENT '时间',
    `process_read_count` Nullable(Int64) COMMENT '处理读取量',
    `process_read_error_count` Nullable(Int64) COMMENT '处理读取异常量',
    `process_drop_count` Nullable(Int64) COMMENT '处理丢弃量'
)
ENGINE = MergeTree
PARTITION BY toYYYYMM(time)
ORDER BY (time)
SETTINGS index_granularity = 8192
COMMENT '流处理数据处理实时指标表';


CREATE TABLE IF NOT EXISTS stream_storage_realtime_metrics
(
    `data_model_id` Nullable(Int32) COMMENT '建模ID',
    `time` Date COMMENT '时间',
    `storage_id` Nullable(Int64) COMMENT '存储点id',
    `process_write_drop_count` Nullable(Int64) COMMENT '处理写入丢弃量',
    `process_write_split_count` Nullable(Int64) COMMENT '处理写入拆分量',
    `process_write_count` Nullable(Int64) COMMENT '处理写入量',
    `process_write_error_count` Nullable(Int64) COMMENT '处理写入异常量',
    `storage_read_count` Nullable(Int64) COMMENT '存储读取量',
    `storage_read_error_count` Nullable(Int64) COMMENT '存储读取异常量',
    `storage_write_count` Nullable(Int64) COMMENT '存储写入量',
    `storage_write_error_count` Nullable(Int64) COMMENT '存储写入异常量'
)
ENGINE = MergeTree
PARTITION BY toYYYYMM(time)
ORDER BY (time)
SETTINGS index_granularity = 8192
COMMENT '流处理数据存储实时指标表';