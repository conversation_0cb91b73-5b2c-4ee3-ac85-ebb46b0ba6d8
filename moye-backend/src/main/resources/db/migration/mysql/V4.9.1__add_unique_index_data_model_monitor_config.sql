-- 为data_model_monitor_config表添加唯一索引，防止重复创建监控配置

-- 创建通用的索引添加存储过程（如果不存在）
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS addUniqueIndexIfNotExists(
    IN tableName VARCHAR(255),
    IN indexName VARCHAR(255),
    IN indexColumns VARCHAR(500)
)
BEGIN
    DECLARE indexExists INT DEFAULT 0;
    DECLARE sqlStatement TEXT;

    -- 检查索引是否已存在
    SELECT COUNT(1) INTO indexExists
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
      AND table_name = tableName
      AND index_name = indexName;

    -- 如果索引不存在，则创建索引
    IF indexExists = 0 THEN
        SET @sqlStatement = CONCAT('ALTER TABLE ', tableName, ' ADD UNIQUE INDEX ', indexName, ' (', indexColumns, ')');
        PREPARE stmt FROM @sqlStatement;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END //

DELIMITER ;

-- 清理重复数据：对于相同data_model_id和type的记录，保留id最大的记录，删除其他重复记录
-- 使用EXISTS子查询确保只在有重复数据时执行删除操作
DELETE t1 FROM data_model_monitor_config t1
INNER JOIN data_model_monitor_config t2
WHERE t1.data_model_id = t2.data_model_id
  AND t1.type = t2.type
  AND t1.id < t2.id
  AND EXISTS (
    SELECT 1 FROM (
        SELECT data_model_id, type, COUNT(1) as cnt
        FROM data_model_monitor_config
        GROUP BY data_model_id, type
        HAVING cnt > 1
    ) as duplicates
    WHERE duplicates.data_model_id = t1.data_model_id
      AND duplicates.type = t1.type
  );

-- 调用存储过程添加唯一索引
CALL addUniqueIndexIfNotExists('data_model_monitor_config', 'uk_data_model_id_type', 'data_model_id, type');
