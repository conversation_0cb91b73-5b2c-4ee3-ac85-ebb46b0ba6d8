CALL addColumnIfNotExists('data_model', 'is_mcp_published',
                          'ALTER TABLE `data_model` ADD COLUMN `is_mcp_published` tinyint(1) default 0 COMMENT ''是否发布到数据智能体'';');
-- 将有描述的数据模型标记为已发布
update data_model
set is_mcp_published = 1
where description is not null
  and description != ''
  and layer != 'INDICATOR'
  and exists(select *
             from data_storage ds
             where ds.data_model_id = data_model.id
               and ds.create_table_status = 'SUCCESS');
