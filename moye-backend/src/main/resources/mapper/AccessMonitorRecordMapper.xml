<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trs.ai.moye.monitor.mapper.AccessMonitorRecordMapper">

    <!-- 分页查询接入记录 -->
    <select id="selectPageWithConditions" resultType="com.trs.ai.moye.monitor.entity.AccessMonitorRecord">
        SELECT * FROM access_monitor_record
        <where>
            <if test="dataModelId != null">
                AND data_model_id = #{dataModelId}
            </if>
            <if test="startTime != null and endTime != null">
                AND access_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="isError != null">
                AND is_error = #{isError}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    source_name LIKE CONCAT('%', #{keyword}, '%')
                    OR execute_id LIKE CONCAT('%', #{keyword}, '%')
                    OR record_id LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        <choose>
            <when test="sortField != null and sortField != '' and sortOrder != null and sortOrder != ''">
                ORDER BY ${sortField} ${sortOrder}
            </when>
            <otherwise>
                ORDER BY access_time DESC
            </otherwise>
        </choose>
    </select>

</mapper>
