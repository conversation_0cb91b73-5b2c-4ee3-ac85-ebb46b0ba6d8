<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.DataModelDisplayMapper">
    <resultMap id="ModelExecuteStatusGroupCountMap" type="com.trs.ai.moye.common.dao.GroupCount">
        <result column="value" property="value" javaType="com.trs.moye.base.data.model.enums.ModelExecuteStatus"/>
        <result column="count" property="count"/>
    </resultMap>

    <select id="selectExecuteStatusCounts" resultMap="ModelExecuteStatusGroupCountMap">
        select count(1) as count,
        execute_status as value
        from data_model
        where layer = #{layer}
        group by execute_status
    </select>

    <select id="selectBusinessCategoryCounts" resultType="com.trs.ai.moye.common.dao.GroupCount">
        SELECT COUNT(1) AS count,
        bc.zh_name AS value
        FROM data_model dm
            JOIN business_category bc
        ON dm.business_category_id = bc.id
        WHERE dm.layer = #{layer}
        GROUP BY bc.zh_name;
    </select>

    <select id="getArrangementInfoById" resultType="com.trs.ai.moye.data.model.dto.ArrangementInfo">
        select (select if(c.execute_mode = 'REALTIME', 'STREAM', 'BATCH')
                from data_model_schedule_config c
                where c.data_model_id = d.id)                                                as processType,
               (select b.display_type from batch_arrangement b where b.data_model_id = d.id) as arrangeType
        from data_model d
        where d.id = #{id}
    </select>

    <select id="selectAllDataModelRelation" resultType="com.trs.ai.moye.data.model.entity.MetadataRelation">
        SELECT
        dm.id AS dataModelId,
        dm.layer AS modelLayer,
        dm.business_category_id AS categoryId,
        dm.zh_name AS dataModelName,
        dm.en_name AS dataModelEnName,
        dm.update_time AS recentUpdateTime,
        dm.create_mode AS dataModelType,
        dm.is_mcp_published as isMcpPublished,
        if(dm.layer = 'ODS',
        (SELECT ds.connection_id FROM data_source_config ds WHERE ds.data_model_id = dm.id LIMIT 1),
        NULL) AS connectionId,
        (SELECT dmsc.execute_mode FROM data_model_schedule_config dmsc WHERE dmsc.data_model_id = dm.id LIMIT 1) AS
        executeMode,
        if(dm.execute_status IS NULL, 'STOP', dm.execute_status) AS status
        FROM
        data_model dm
        <where>
            <if test="request.enableStatus != null">
                <choose>
                    <when test="request.enableStatus == @com.trs.moye.base.data.model.enums.ModelExecuteStatus@STOP">
                        AND (dm.execute_status = 'STOP' OR dm.execute_status IS NULL)
                    </when>
                    <otherwise>
                        AND dm.execute_status = #{request.enableStatus}
                    </otherwise>
                </choose>
            </if>
            <if test="request.modelLayerType != null">
                AND dm.layer = #{request.modelLayerType}
            </if>
            <if test="request.createTableStatus != null">
                AND dm.id IN (SELECT data_model_id FROM data_storage WHERE create_table_status =
                #{request.createTableStatus})
            </if>
            <if test="request.arrangeStatus != null">
                AND dm.is_arranged = #{request.arrangeStatus}
            </if>
            <if test="request.connectionType != null">
                AND dm.layer = 'ODS' AND EXISTS (
                SELECT 1 FROM data_source_config ds
                JOIN data_connection dc ON dc.id = ds.connection_id
                WHERE ds.data_model_id = dm.id AND dc.connection_type = #{request.connectionType}
                )
            </if>
            <if test="request.dataSourceCategory != null and request.connectionTypesByCategory != null and !request.connectionTypesByCategory.isEmpty()">
                AND dm.layer = 'ODS' AND EXISTS (
                SELECT 1 FROM data_source_config ds
                JOIN data_connection dc ON dc.id = ds.connection_id
                WHERE ds.data_model_id = dm.id
                AND dc.connection_type IN
                <foreach collection="request.connectionTypesByCategory" item="connectionType" open="(" close=")" separator=",">
                    #{connectionType}
                </foreach>
                )
            </if>
            <if test="request.connectionId != null">
                AND dm.layer = 'ODS' AND EXISTS (
                SELECT 1 FROM data_source_config ds
                WHERE dm.id = ds.data_model_id AND ds.connection_id = #{request.connectionId}
                )
            </if>
            <if test="request.isMcpPublished != null">
                AND dm.is_mcp_published = #{request.isMcpPublished}
            </if>
        </where>
    </select>

    <select id="selectSourceDataModelList" resultType="com.trs.ai.moye.data.model.entity.SourceDataMode">
        SELECT
        dm.id,
        dm.en_name,
        dm.zh_name,
        dm.business_category_id AS categoryId,
        dm.layer AS modelLayer,
        dm.is_arranged,
        dm.description,
        dmsc.execute_mode,
        MAX(dmf.is_increment) AS has_increment_field,
        dm.create_mode AS dataModelType,
        if(dm.layer = 'ODS',
        (SELECT ds.connection_id FROM data_source_config ds WHERE ds.data_model_id = dm.id LIMIT 1),
        NULL) AS connectionId
        FROM data_model dm
        LEFT JOIN data_model_schedule_config dmsc ON dmsc.data_model_id = dm.id
        LEFT JOIN data_model_field dmf ON dm.id = dmf.data_model_id
        <where>
            <if test="request.modelLayer != null">
                <choose>
                    <when test="request.modelLayer.name == 'DWD'">
                        AND layer = 'ODS'
                    </when>
                    <when test="request.modelLayer.name == 'THEME'">
                        AND layer in ('ODS','DWD')
                    </when>
                    <when test="request.modelLayer.name == 'SUBJECT'">
                        AND layer in ('ODS','DWD','THEME')
                    </when>
                    <when test="request.modelLayer.name == 'INDICATOR'">
                        AND layer in ('ODS','DWD','THEME','SUBJECT')
                    </when>
                </choose>
            </if>
            <if test="request.searchLayer != null">
                AND layer = #{request.searchLayer}
            </if>

            <if test="request.categoryId != null">
                AND dm.business_category_id = #{request.categoryId}
            </if>

            <if test="@org.apache.commons.lang3.StringUtils@isNotBlank(request.keyword)">
                AND (dm.zh_name LIKE concat('%', #{request.keyword}, '%')
                OR dm.en_name LIKE concat('%', #{request.keyword}, '%'))
            </if>

            <if test="request.arrangeStatus != null">
                AND dm.is_arranged = #{request.arrangeStatus}
            </if>
        </where>
        GROUP BY dm.id
        ORDER BY dm.update_time DESC
    </select>

    <select id="selectAllModelStorageDictList" resultType="com.trs.ai.moye.data.model.dto.ModelConnectionTypeDTO">
        SELECT ds.data_model_id AS dataModelId, dc.connection_type AS connectionType
        FROM (SELECT data_model_id, connection_id FROM data_storage) AS ds
                 LEFT JOIN (SELECT id, `name`, connection_type FROM data_connection) AS dc ON (ds.connection_id = dc.id)
    </select>

    <select id="selectDataModelDict" resultType="com.trs.moye.base.common.response.IdNameResponse">
        select id, zh_name as name
        from data_model
    </select>

    <select id="selectAllDataModelWithExecuteMode" resultType="com.trs.ai.moye.data.model.entity.MetadataRelation">
        select dm.id                   AS dataModelId,
               dm.layer                AS modelLayer,
               dm.business_category_id AS categoryId,
               dm.zh_name              AS dataModelName,
               dm.en_name              AS dataModelEnName,
               dm.update_time          AS recentUpdateTime,
               dm.create_mode          AS dataModelType,
               dmsc.execute_mode       AS executeMode
        from data_model dm
                 left join data_model_schedule_config dmsc on (dm.id = dmsc.data_model_id)
    </select>

    <select id="selectDataServiceCounts" resultType="com.trs.ai.moye.common.dao.GroupCount">
        SELECT COUNT(DISTINCT m.id) AS count,
            CASE
                WHEN s.id IS NOT NULL THEN '已提供服务'
                ELSE '未提供服务'
        END
        AS value
        FROM
            data_model m
                LEFT JOIN
            data_storage st ON st.data_model_id = m.id
                LEFT JOIN
            data_service s ON s.storage_id = st.id
        WHERE
            m.layer =
        #{layer}
        GROUP
        BY
        CASE
        WHEN
        s
        .
        id
        IS
        NOT
        NULL
        THEN
        'provided'
        ELSE
        'not provided'
        END;
    </select>

    <resultMap id="ArrangedGroupCountMap" type="com.trs.ai.moye.common.dao.GroupCount">
        <result column="value" property="value" javaType="com.trs.moye.base.data.model.enums.ArrangedStatus"/>
        <result column="count" property="count"/>
    </resultMap>

    <select id="selectArrangedCounts" resultMap="ArrangedGroupCountMap">
        SELECT COUNT(*) AS count,
            CASE
                WHEN dm.is_arranged = 1 THEN 'ARRANGED'
                ELSE 'NOT_ARRANGED'
        END
        AS value
        FROM
            data_model dm
        WHERE
            dm.layer =
        #{layer}
        GROUP
        BY
        dm
        .
        is_arranged;
    </select>
    <select id="selectDataModelList" resultMap="com.trs.moye.base.data.model.dao.DataModelMapper.DataModelMap">
        select *
        from data_model dm
        <where>
            <if test="request.categoryId != null">
                AND dm.business_category_id = #{request.categoryId}
            </if>
            <if test="request.modelLayer != null">
                AND dm.layer = #{request.modelLayer}
            </if>
        </where>
    </select>
</mapper>