<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.data.model.dao.batch.BatchArrangementDraftMapper">
    <resultMap id="BaseResultMap" type="com.trs.ai.moye.data.model.dto.arrangement.batch.BatchOperatorPipelineDTO" >
        <result column="id" property="id" />
        <result column="data_model_id" property="dataModelId" />
        <result column="operators" property="operators" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
        <result column="canvas" property="canvas" typeHandler="com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler"/>
    </resultMap>

    <select id="selectByDataModelId" resultMap="BaseResultMap">
        SELECT *
        FROM batch_arrangement_draft
        WHERE data_model_id = #{dataModelId}
    </select>

    <insert id="insert">
        insert into batch_arrangement_draft(data_model_id,
                                            canvas,
                                            operators)
        values (#{dataModelId},
                #{dto.canvas, typeHandler=com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler},
                #{dto.operators, typeHandler=com.trs.moye.base.common.typehandler.CustomJacksonTypeHandler})
    </insert>

    <delete id="deleteByDataModelId">
        DELETE
        FROM batch_arrangement_draft
        WHERE data_model_id = #{dataModelId}
    </delete>
</mapper>