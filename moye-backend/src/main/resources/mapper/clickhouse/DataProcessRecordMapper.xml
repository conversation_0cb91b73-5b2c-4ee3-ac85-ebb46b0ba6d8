<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.monitor.dao.DataProcessRecordMapper">
    <select id="selectNodeProcessCountList" resultType="com.trs.ai.moye.monitor.entity.NodeProcessCountInfo">
        SELECT data_model_id          as dataModelId,
               pod_ip                 as node,
               COUNT(*)               AS processCount,
               sumIf(1, is_error = 1) AS processErrorCount,
               sum(processing_time)   AS processingTimeMillis
        FROM data_process_record
        WHERE storage_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        GROUP BY data_model_id, pod_ip
    </select>

    <select id="selectLastDayCountByModelId" resultType="com.trs.ai.moye.homepage.entity.HomePageDwdStatistics">
        select data_model_id,
        data_model_name as data_model_name,
        toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
        'STREAM' as type,
        SUM(CASE WHEN is_error = 1 THEN 1 ELSE 0 END) AS fail_count,
        SUM(CASE WHEN is_error = 0 THEN 1 ELSE 0 END) AS success_count,
        COUNT(*) AS total_count,
        sum(executed_operator_count) as operator_count,
        toTimezone(now(), 'Asia/Shanghai') as syncTime
        from data_process_record
        <where>
            <if test="startTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
            </if>
            <if test="endTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
            </if>
            <if test="dataModelIds !=null">
                and data_model_id in
                <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                    #{dataModelId}
                </foreach>
            </if>
        </where>
        GROUP BY data_model_id,
        data_model_name,
        time,
        type;
    </select>

    <select id="selectLastDayCountByModelIdTheme" resultType="com.trs.ai.moye.homepage.entity.HomePageThemeStatistics">
        select data_model_id,
        data_model_name as data_model_name,
        toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
        'STREAM' as type,
        SUM(CASE WHEN is_error = 1 THEN 1 ELSE 0 END) AS fail_count,
        SUM(CASE WHEN is_error = 0 THEN 1 ELSE 0 END) AS success_count,
        COUNT(*) AS total_count,
        sum(executed_operator_count) as operator_count,
        toTimezone(now(), 'Asia/Shanghai') as syncTime
        from data_process_record
        <where>
            <if test="startTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
            </if>
            <if test="endTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
            </if>
            <if test="dataModelIds !=null">
                and data_model_id in
                <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                    #{dataModelId}
                </foreach>
            </if>
        </where>
        GROUP BY data_model_id,
        data_model_name,
        time,
        type;
    </select>

    <select id="selectLastDayCountByModelIdSubject"
        resultType="com.trs.ai.moye.homepage.entity.HomePageSubjectStatistics">
        select data_model_id,
        data_model_name as data_model_name,
        toDate(toTimezone(end_time, 'Asia/Shanghai')) as time,
        'STREAM' as type,
        SUM(CASE WHEN is_error = 1 THEN 1 ELSE 0 END) AS fail_count,
        SUM(CASE WHEN is_error = 0 THEN 1 ELSE 0 END) AS success_count,
        COUNT(*) AS total_count,
        sum(executed_operator_count) as operator_count,
        toTimezone(now(), 'Asia/Shanghai') as syncTime
        from data_process_record
        <where>
            <if test="startTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
            </if>
            <if test="endTime!=null">
                and toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
            </if>
            <if test="dataModelIds !=null">
                and data_model_id in
                <foreach collection="dataModelIds" item="dataModelId" open="(" separator="," close=")">
                    #{dataModelId}
                </foreach>
            </if>
        </where>
        GROUP BY data_model_id,
        data_model_name,
        time,
        type;
    </select>

    <select id="countErrorMsgByTime" resultType="java.lang.Long">
        SELECT COUNT(record_id) FROM data_process_record
        <where>
            is_error = 1
            <if test="startTime != null">
                AND toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &gt;= toDateTime(#{startTime})
            </if>
            <if test="endTime != null">
                AND toDateTime(toTimezone(end_time, 'Asia/Shanghai')) &lt;= toDateTime(#{endTime})
            </if>
            <if test="dataModelId != null">
                AND data_model_id = #{dataModelId}
            </if>
        </where>
    </select>

    <select id="distinctSelectErrorMsgByTime"
        resultType="com.trs.ai.moye.data.model.response.DataProcessRecordMsgResponse">
        SELECT record_id as recordId, msg_content as msg
        FROM (
                 SELECT record_id,
                        msg_content,
                        storage_time,
                        is_error,
                        ROW_NUMBER() OVER (PARTITION BY record_id ORDER BY storage_time DESC) AS rn
                 FROM data_process_record
                 WHERE data_model_id = #{dataModelId}
                   and storage_time >= toDateTime(#{startTime})
                   and storage_time &lt;= toDateTime(#{endTime})
             ) t
        WHERE rn = 1 AND is_error = 1
    </select>

    <select id="selectStreamTaskMonitorList" resultType="com.trs.ai.moye.data.model.response.DataProcessRecordResponse">
        SELECT *
        FROM data_process_record
        <where>
            data_model_id = #{modelId} and storage_time >= #{beginTime} and storage_time &lt;= #{endTime}
            <if test="isError != null">
                and is_error = #{isError}
            </if>
            <if test="conditionKey != null and conditionKey != '' and operator != '' and operator != null and conditionValue != null">
                <choose>
                    <when test="conditionKey == 'EXECUTED_OPERATOR_COUNT'">
                        and executed_operator_count ${operator} #{conditionValue}
                    </when>
                    <when test="conditionKey == 'OPERATOR_COUNT'">
                        and operator_count ${operator} #{conditionValue}
                    </when>
                    <when test="conditionKey == 'PROCESSING_TIME'">
                        and processing_time ${operator} #{conditionValue}
                    </when>
                </choose>
            </if>
            <!-- 修改后的动态搜索条件 -->
            <if test="searchParams != null and searchParams.fields != null and searchParams.keyword != null and searchParams.keyword != ''">
                AND
                <trim prefix="(" suffix=")" prefixOverrides="OR | AND">
                    <foreach collection="searchParams.fields" item="field">
                        <choose>
                            <when test="field == 'msg_title'">
                                OR msg_title LIKE CONCAT('%', #{searchParams.keyword}, '%')
                            </when>
                            <when test="field == 'record_id'">
                                OR record_id = #{searchParams.keyword}
                            </when>
                        </choose>
                    </foreach>
                </trim>
            </if>
        </where>
        ORDER BY storage_time DESC
    </select>

    <select id="selectTracerDataListByRecordId" resultType="com.trs.moye.ability.domain.DataProcessTrace">
        SELECT *
        FROM data_process_trace
        <where>
            record_id = #{recordId}
            AND data_model_id = #{dataModelId}
        </where>
        ORDER BY processing_order
    </select>

    <select id="selectByRecordId" resultType="com.trs.moye.ability.domain.DataProcessRecord">
        select *
        from data_process_record
        where record_id = #{recordId}
          and data_model_id = #{dataModelId}
    </select>

</mapper>