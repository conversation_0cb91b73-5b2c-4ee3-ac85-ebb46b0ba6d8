<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.ai.moye.tasks.dao.CkScheduledTaskMapper">


    <delete id="deletePartitionOfData">
        ALTER TABLE ${tableName} DROP PARTITION #{partition}
    </delete>

    <select id="selectPartitionOfProcessTrace" resultType="java.lang.Integer">
        SELECT DISTINCT (partition) AS partition
        FROM system.parts
        WHERE
            table = #{tableName}
          AND
            database = currentDatabase()
    </select>

    <select id="selectPartitions" resultType="com.trs.ai.moye.monitor.entity.PartitionInfo">
        SELECT
            toString(extract(partition, '\\((\\d+),')) AS datePartition,
            toString(extract(partition, ',(\\d+)\\)')) AS isError
        FROM system.parts
        WHERE table = #{tableName} AND database = currentDatabase()

    </select>

    <delete id="deletePartition">
        ALTER TABLE ${tableName} DROP PARTITION (#{datePartition}, #{isError})
    </delete>
</mapper>