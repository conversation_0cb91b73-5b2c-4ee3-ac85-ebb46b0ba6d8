package com.trs.ai.moye.permission.service;

import com.trs.ai.moye.backstage.dao.UserMapper;
import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.common.exception.AuthenticationException;
import com.trs.ai.moye.permission.request.UserLoginRequest;
import com.trs.moye.base.common.utils.AesUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;

/**
 * 验证service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AuthenticationService {

    private final UserMapper userMapper;

    private final AuthenticationManager authenticationManager;

    public AuthenticationService(UserMapper userMapper, AuthenticationManager authenticationManager) {
        this.authenticationManager = authenticationManager;
        this.userMapper = userMapper;
    }

    /**
     * 验证当前登录用户；若通过则返回对应用户信息
     *
     * @param loginRequest 登录用户账户密码
     * @return 用户信息
     */
    public User authenticate(UserLoginRequest loginRequest) {
        try {
            authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getAccount(),
                    AesUtils.decrypt(loginRequest.getPassword()))
            );
        } catch (Exception e) {
            log.error("登录错误，可能是用户名或密码错误", e);
            throw new AuthenticationException("用户名或密码错误！");
        }
        return userMapper.findByAccount(loginRequest.getAccount());
    }

}