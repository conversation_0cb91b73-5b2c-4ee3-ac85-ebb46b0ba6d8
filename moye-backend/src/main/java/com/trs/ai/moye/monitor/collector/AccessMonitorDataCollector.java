package com.trs.ai.moye.monitor.collector;

import com.trs.ai.moye.monitor.entity.AccessMonitorRecord;
import com.trs.ai.moye.monitor.mapper.AccessMonitorRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.UUID;

/**
 * 数据接入监控数据采集器
 * 在Seatunnel数据接入过程中记录监控数据
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Component
public class AccessMonitorDataCollector {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private AccessMonitorRecordMapper accessMonitorRecordMapper;

    /**
     * 记录数据接入开始
     *
     * @param executeId 执行ID
     * @param dataModelId 数据模型ID
     * @param sourceName 数据源名称
     * @param sourceType 数据源类型
     */
    public void recordAccessStart(String executeId, Integer dataModelId,
                                 String sourceName, String sourceType) {
        try {
            // 1. 创建接入记录
            AccessMonitorRecord record = AccessMonitorRecord.builder()
                .recordId(generateRecordId())
                .executeId(executeId)
                .dataModelId(dataModelId)
                .sourceName(sourceName)
                .sourceType(sourceType)
                .accessTime(System.currentTimeMillis())
                .startTime(System.currentTimeMillis())
                .dataCount(0L)
                .accessDuration(0L)
                .isError(false)
                .errorMsgReadFlag(0)
                .build();

            // 2. 保存到数据库
            accessMonitorRecordMapper.insert(record);

            // 3. 缓存开始时间，用于计算耗时
            String cacheKey = "access:start:" + executeId;
            redisTemplate.opsForValue().set(cacheKey, System.currentTimeMillis(), Duration.ofHours(1));

            // 4. 更新实时计数器
            updateRealTimeCounters(dataModelId, 1, 0);

            log.info("记录数据接入开始: executeId={}, dataModelId={}, sourceName={}", 
                    executeId, dataModelId, sourceName);
        } catch (Exception e) {
            log.error("记录数据接入开始失败: executeId={}, dataModelId={}", executeId, dataModelId, e);
        }
    }

    /**
     * 记录数据接入完成
     *
     * @param executeId 执行ID
     * @param dataCount 接入数据量
     * @param isError 是否发生错误
     * @param errorMsg 错误信息，如果发生错误则不为null
     */
    public void recordAccessComplete(String executeId, long dataCount, boolean isError, String errorMsg) {
        try {
            // 1. 获取开始时间
            String cacheKey = "access:start:" + executeId;
            Long startTime = (Long) redisTemplate.opsForValue().get(cacheKey);

            // 2. 计算接入耗时
            long accessDuration = startTime != null ? System.currentTimeMillis() - startTime : 0;

            // 3. 更新接入记录
            AccessMonitorRecord record = accessMonitorRecordMapper.selectByExecuteId(executeId);
            if (record != null) {
                record.setDataCount(dataCount);
                record.setAccessDuration(accessDuration);
                record.setIsError(isError);
                record.setErrorMsg(errorMsg);
                record.setEndTime(System.currentTimeMillis());
                accessMonitorRecordMapper.updateById(record);
            }

            // 4. 更新实时计数器
            updateRealTimeCounters(record.getDataModelId(), 0, isError ? 1 : 0);

            // 5. 清理缓存
            redisTemplate.delete(cacheKey);

            log.info("记录数据接入完成: executeId={}, dataCount={}, isError={}, duration={}ms", 
                    executeId, dataCount, isError, accessDuration);
        } catch (Exception e) {
            log.error("记录数据接入完成失败: executeId={}", executeId, e);
        }
    }

    /**
     * 记录执行日志
     *
     * @param executeId 执行ID
     * @param logLevel 日志级别，例如 INFO, ERROR 等
     * @param logMessage 日志内容
     */
    public void recordExecutionLog(String executeId, String logLevel, String logMessage) {
        try {
            // 这里可以调用ExecutionLogMapper来保存日志
            // 为了简化，这里只记录到Redis缓存
            String cacheKey = "execution:log:" + executeId;
            String logEntry = String.format("[%s] %s: %s", 
                    System.currentTimeMillis(), logLevel, logMessage);
            
            redisTemplate.opsForList().rightPush(cacheKey, logEntry);
            redisTemplate.expire(cacheKey, Duration.ofHours(24));
        } catch (Exception e) {
            log.error("记录执行日志失败: executeId={}, logLevel={}", executeId, logLevel, e);
        }
    }

    /**
     * 更新实时计数器
     *
     * @param dataModelId 数据模型ID
     * @param increment 增量计数
     * @param errorIncrement 错误增量计数
     */
    private void updateRealTimeCounters(Integer dataModelId, int increment, int errorIncrement) {
        try {
            String totalKey = "access:counter:total:" + dataModelId;
            String todayKey = "access:counter:today:" + dataModelId;
            String errorKey = "access:counter:errors:" + dataModelId;

            if (increment > 0) {
                redisTemplate.opsForValue().increment(totalKey, increment);
                redisTemplate.opsForValue().increment(todayKey, increment);
            }

            if (errorIncrement > 0) {
                redisTemplate.opsForValue().increment(errorKey, errorIncrement);
            }

            // 设置过期时间
            redisTemplate.expire(totalKey, Duration.ofDays(30));
            redisTemplate.expire(todayKey, Duration.ofDays(1));
            redisTemplate.expire(errorKey, Duration.ofDays(1));
        } catch (Exception e) {
            log.error("更新实时计数器失败: dataModelId={}", dataModelId, e);
        }
    }

    /**
     * 生成记录ID
     *
     * @return {@link String }
     */
    private String generateRecordId() {
        return "R" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 获取实时计数器
     *
     * @param dataModelId 数据模型ID
     * @param counterType 计数器类型，例如 "total", "today", "errors"
     * @return {@link Long }
     */
    public Long getRealTimeCounter(Integer dataModelId, String counterType) {
        try {
            String key = "access:counter:" + counterType + ":" + dataModelId;
            Object value = redisTemplate.opsForValue().get(key);
            return value != null ? Long.valueOf(value.toString()) : 0L;
        } catch (Exception e) {
            log.error("获取实时计数器失败: dataModelId={}, counterType={}", dataModelId, counterType, e);
            return 0L;
        }
    }
}
