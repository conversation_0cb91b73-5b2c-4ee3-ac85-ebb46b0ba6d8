package com.trs.ai.moye.monitor.service;

import com.trs.ai.moye.data.model.dao.StorageTaskMapper;
import com.trs.ai.moye.data.model.entity.StorageTask;
import com.trs.moye.base.storage.seatunnel.SeaTunnelJobStatus;
import com.trs.ai.moye.data.model.request.ScheduleRecordRequest;
import com.trs.ai.moye.data.model.service.DataModelService;
import com.trs.ai.moye.monitor.dto.AccessMonitorDataDTO;
import com.trs.moye.base.common.enums.TimeRange;
import com.trs.moye.base.common.request.TimeRangeParams;
import com.trs.moye.base.common.response.PageResponse;
// 🔧 新增：导入MonitorFeign和MonitorDTO
import com.trs.ai.moye.storageengine.feign.MonitorFeign;
import com.trs.ai.moye.storageengine.dto.MonitorDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * 基于调度记录的指标计算服务（优化版本）
 * 使用读取成功量（read_success_count）和读取失败量（read_fail_count）计算监控指标
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Service
public class ScheduleBasedMetricsService {

    @Resource
    private DataModelService dataModelService;

    @Resource
    private StorageTaskMapper storageTaskMapper;

    // 🔧 新增：注入MonitorFeign，用于获取真实积压量
    @Resource
    private MonitorFeign monitorFeign;

    // ==================== 核心业务方法 ====================

    /**
     * 计算基于读取成功量的接入指标（优化版本）
     * 
     * 接入速率计算逻辑：
     * 1. 统计最近2分钟内的任务执行情况
     * 2. 根据实际任务执行时间计算持续时间
     * 3. 速率单位：条/秒（records per second）
     * 4. 智能调整统计窗口，确保速率计算的准确性
     *
     * @param dataModelId 数据模型ID
     * @return 接入指标
     */
    public AccessMonitorDataDTO.AccessMetrics calculateAccessMetrics(Integer dataModelId) {
        try {
            log.info("开始基于读取成功量计算接入指标, dataModelId: {}", dataModelId);

            // 使用优化的单个SQL查询获取所有统计数据
            java.util.Map<String, Object> statistics = storageTaskMapper.getAccessStatisticsByScheduleRecord(dataModelId);
            
            // 从结果中提取各项指标
            Long todayAccessCount = getLongValue(statistics, "todaySuccessCount");
            Long totalAccessCount = getLongValue(statistics, "totalSuccessCount");
            Long todayErrorCount = getLongValue(statistics, "todayErrorCount");
            Long recentMinutesSuccessCount = getLongValue(statistics, "recentMinutesSuccessCount");
            Long recentMinutesDuration = getLongValue(statistics, "recentMinutesDuration");

            // 计算接入速率（条/秒）
            BigDecimal accessSpeed = calculateSmartAccessSpeed(recentMinutesSuccessCount, recentMinutesDuration);

            // 🔧 修改：直接调用MonitorFeign获取真实积压量，确保与接入卡片数据一致
            Long sourceLagCount = getRealTimeLagCount(dataModelId);
            boolean hasLag = sourceLagCount > 0;
            String lagStatus = generateLagStatus(sourceLagCount, hasLag);

            log.info("基于读取成功量的指标获取完成: dataModelId={}, todayAccessCount={}, totalAccessCount={}, todayErrorCount={}, recentMinutesSuccessCount={}, recentMinutesDuration={}, accessSpeed={}, sourceLagCount={}, hasLag={}",
                dataModelId, todayAccessCount, totalAccessCount, todayErrorCount, recentMinutesSuccessCount, recentMinutesDuration, accessSpeed, sourceLagCount, hasLag);

            AccessMonitorDataDTO.AccessMetrics metrics = AccessMonitorDataDTO.AccessMetrics.builder()
                .accessTotalCount(totalAccessCount)
                .todayAccessCount(todayAccessCount)
                .accessSpeed(accessSpeed)
                .speedUnit("SECOND")
                .todayErrorCount(todayErrorCount)
                .lagCount(sourceLagCount)  // 🔧 修改：使用真实积压量
                .sourceLagCount(sourceLagCount)  // 🔧 修改：使用真实积压量
                .hasLag(hasLag)  // 🔧 修改：基于真实积压量判断
                .lagStatus(lagStatus)  // 🔧 修改：基于真实积压量生成状态
                .remark("基于读取成功量计算的接入监控指标（优化版本，实时速率，包含积压监控，积压量通过MonitorFeign实时获取）")
                .build();

            log.info("基于读取成功量的接入指标计算完成: dataModelId={}, todayAccessCount={}, totalAccessCount={}, accessSpeed={}, todayErrorCount={}, sourceLagCount={}, hasLag={}",
                dataModelId, todayAccessCount, totalAccessCount, accessSpeed, todayErrorCount, sourceLagCount, hasLag);

            return metrics;

        } catch (Exception e) {
            log.error("基于读取成功量计算接入指标失败, dataModelId: {}", dataModelId, e);
            return getDefaultAccessMetrics();
        }
    }

    // ==================== 积压量计算相关方法 ====================

    /**
     * 🔧 新增：获取实时积压量，直接调用MonitorFeign从数据源获取
     * 确保与接入卡片显示的积压量数据一致
     *
     * @param dataModelId 数据模型ID
     * @return 实时积压量
     */
    private Long getRealTimeLagCount(Integer dataModelId) {
        try {
            log.info("开始获取实时积压量, dataModelId: {}", dataModelId);
            
            // 直接调用MonitorFeign获取实时积压量，与接入卡片使用相同的数据源
            MonitorDTO monitorDTO = new MonitorDTO(dataModelId);
            Long lagCount = monitorFeign.getDataSourceLag(monitorDTO);
            
            log.info("实时积压量获取成功: dataModelId={}, lagCount={}", dataModelId, lagCount);
            return lagCount != null ? lagCount : 0L;
            
        } catch (Exception e) {
            log.error("获取实时积压量失败, dataModelId: {}, 使用默认值0", dataModelId, e);
            return 0L;
        }
    }

    /**
     * 🔧 修改：计算数据源积压量 - 现在直接调用MonitorFeign获取
     * 保持方法名不变，确保向后兼容
     *
     * @param dataModelId 数据模型ID
     * @return 积压量
     */
    private Long calculateSourceLagCount(Integer dataModelId) {
        try {
            log.info("开始计算数据源积压量, dataModelId: {}", dataModelId);
            
            // 🔧 修改：直接调用MonitorFeign获取实时积压量，不再使用调度记录计算
            Long lagCount = getRealTimeLagCount(dataModelId);
            
            log.info("数据源积压量计算完成: dataModelId={}, lagCount={}", dataModelId, lagCount);
            return lagCount;
            
        } catch (Exception e) {
            log.error("计算数据源积压量失败, dataModelId: {}", dataModelId, e);
            return 0L;
        }
    }

    /**
     * 🔧 修改：基于调度记录计算积压量 - 现在直接调用MonitorFeign获取
     * 保持方法名不变，确保向后兼容
     *
     * @param dataModelId 数据模型ID
     * @return 积压量
     */
    private Long calculateLagFromScheduleRecords(Integer dataModelId) {
        try {
            log.info("基于调度记录计算积压量（已改为调用MonitorFeign）, dataModelId: {}", dataModelId);
            
            // 🔧 修改：直接调用MonitorFeign获取实时积压量，确保与接入卡片数据一致
            Long lagCount = getRealTimeLagCount(dataModelId);
            
            log.info("积压量获取完成: dataModelId={}, lagCount={}", dataModelId, lagCount);
            return lagCount;
            
        } catch (Exception e) {
            log.error("基于调度记录计算积压量失败, dataModelId: {}", dataModelId, e);
            return 0L;
        }
    }

    /**
     * 🔧 修改：获取最近一次成功的读取量 - 现在返回0，因为不再使用调度记录计算积压量
     * 保持方法名不变，确保向后兼容
     *
     * @param dataModelId 数据模型ID
     * @return 读取量
     */
    private Long getLastSuccessfulReadCount(Integer dataModelId) {
        try {
            log.debug("获取最近一次成功的读取量（已改为调用MonitorFeign获取积压量）, dataModelId: {}", dataModelId);
            // 🔧 修改：不再使用调度记录计算积压量，返回0
            return 0L;
        } catch (Exception e) {
            log.error("获取最近一次成功的读取量失败, dataModelId: {}", dataModelId, e);
            return 0L;
        }
    }

    /**
     * 🔧 修改：获取最近一次成功的存储量 - 现在返回0，因为不再使用调度记录计算积压量
     * 保持方法名不变，确保向后兼容
     *
     * @param dataModelId 数据模型ID
     * @return 存储量
     */
    private Long getLastSuccessfulStorageCount(Integer dataModelId) {
        try {
            log.debug("获取最近一次成功的存储量（已改为调用MonitorFeign获取积压量）, dataModelId: {}", dataModelId);
            // 🔧 修改：不再使用调度记录计算积压量，返回0
            return 0L;
        } catch (Exception e) {
            log.error("获取最近一次成功的存储量失败, dataModelId: {}", dataModelId, e);
            return 0L;
        }
    }

    /**
     * 生成积压状态描述
     *
     * @param sourceLagCount 积压量
     * @param hasLag 是否存在积压
     * @return 积压状态描述
     */
    private String generateLagStatus(Long sourceLagCount, boolean hasLag) {
        if (!hasLag) {
            return "无积压";
        }
        
        String baseStatus = "存在积压";
        if (sourceLagCount != null) {
            if (sourceLagCount > 10000) {
                return baseStatus + "（积压量较大：" + formatLagCount(sourceLagCount) + "）";
            } else if (sourceLagCount > 1000) {
                return baseStatus + "（积压量中等：" + formatLagCount(sourceLagCount) + "）";
            } else {
                return baseStatus + "（积压量较小：" + formatLagCount(sourceLagCount) + "）";
            }
        }
        
        return baseStatus;
    }
    
    /**
     * 格式化积压数量
     *
     * @param count 数量
     * @return 格式化后的字符串
     */
    private String formatLagCount(Long count) {
        if (count >= 100000000) {
            return String.format("%.1f亿", count / 100000000.0);
        } else if (count >= 10000) {
            return String.format("%.1f万", count / 10000.0);
        } else if (count >= 1000) {
            return String.format("%.1fk", count / 1000.0);
        } else {
            return count.toString();
        }
    }

    // ==================== 优化版本的辅助方法 ====================

    /**
     * 从Map中安全获取Long值
     *
     * @param map 数据Map
     * @param key 键名
     * @return Long值，如果不存在则返回0L
     */
    private Long getLongValue(java.util.Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return 0L;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法解析为Long值: key={}, value={}", key, value);
            return 0L;
        }
    }

    /**
     * 根据最近一小时的成功量计算接入速率
     *
     * @param recentHourSuccessCount 最近一小时成功量
     * @return 接入速率（条/分钟）
     */
    private BigDecimal calculateAccessSpeedFromRecentHour(Long recentHourSuccessCount) {
        if (recentHourSuccessCount == null || recentHourSuccessCount <= 0) {
            return BigDecimal.ZERO;
        }
        
        // 一小时 = 60分钟
        long durationMinutes = 60L;
        return new BigDecimal(recentHourSuccessCount).divide(new BigDecimal(durationMinutes), 2, RoundingMode.HALF_UP);
    }

    /**
     * 根据最近两分钟的成功量和持续时间计算接入速率
     *
     * @param recentMinutesSuccessCount 最近两分钟成功量
     * @param recentMinutesDuration 最近两分钟持续时间（秒）
     * @return 接入速率（条/秒）
     */
    private BigDecimal calculateAccessSpeedFromRecentMinutes(Long recentMinutesSuccessCount, Long recentMinutesDuration) {
        if (recentMinutesSuccessCount == null || recentMinutesSuccessCount <= 0) {
            return BigDecimal.ZERO;
        }
        
        // 如果持续时间有效，使用实际持续时间；否则使用默认的2分钟
        long durationSeconds = (recentMinutesDuration != null && recentMinutesDuration > 0) ? recentMinutesDuration : 120L;
        
        // 计算接入速率：成功量 ÷ 持续时间（秒）
        BigDecimal speed = new BigDecimal(recentMinutesSuccessCount).divide(new BigDecimal(durationSeconds), 2, RoundingMode.HALF_UP);
        
        log.debug("接入速率计算: recentMinutesSuccessCount={}, recentMinutesDuration={}, speed={}", 
            recentMinutesSuccessCount, recentMinutesDuration, speed);
        
        return speed;
    }

    /**
     * 智能计算接入速率
     *
     * @param recentMinutesSuccessCount 最近两分钟成功量
     * @param recentMinutesDuration 最近两分钟持续时间（秒）
     * @return 接入速率（条/秒）
     */
    private BigDecimal calculateSmartAccessSpeed(Long recentMinutesSuccessCount, Long recentMinutesDuration) {
        if (recentMinutesSuccessCount == null || recentMinutesSuccessCount <= 0) {
            return BigDecimal.ZERO;
        }
        
        // 优先使用最近两分钟的数据计算实时速率
        if (recentMinutesDuration != null && recentMinutesDuration > 0) {
            return calculateAccessSpeedFromRecentMinutes(recentMinutesSuccessCount, recentMinutesDuration);
        }
        
        // 如果没有最近两分钟的数据，使用最近一小时的数据
        return calculateAccessSpeedFromRecentHour(recentMinutesSuccessCount);
    }

    /**
     * 获取默认接入指标
     *
     * @return {@link AccessMonitorDataDTO.AccessMetrics }
     */
    private AccessMonitorDataDTO.AccessMetrics getDefaultAccessMetrics() {
        return AccessMonitorDataDTO.AccessMetrics.builder()
            .accessTotalCount(0L)
            .todayAccessCount(0L)
            .accessSpeed(BigDecimal.ZERO)
            .speedUnit("SECOND")
            .todayErrorCount(0L)
            .lagCount(null)
            .remark("基于读取成功量的默认接入指标（实时速率）")
            .build();
    }

    /**
     * 获取不同存储点的今日存储统计信息
     * 包含：
     * 1. 成功任务的写入成功量和写入失败量
     * 2. 执行失败任务的预期写入量（作为异常统计）
     *
     * @param dataModelId 数据模型ID
     * @return 不同存储点的今日存储统计信息
     */
    public java.util.Map<String, Object> getStorageStatsByStoragePoints(Integer dataModelId) {
        try {
            log.info("开始获取不同存储点的今日存储统计信息, dataModelId: {}", dataModelId);

            // 构建今日时间范围
            LocalDateTime todayStart = LocalDateTime.now().toLocalDate().atStartOfDay();
            LocalDateTime todayEnd = LocalDateTime.now();
            
            log.info("查询时间范围: todayStart={}, todayEnd={}", todayStart, todayEnd);

            // 构建查询参数 - 移除过于严格的限制
            ScheduleRecordRequest request = createInitializedScheduleRecordRequest();
            
            TimeRangeParams timeRangeParams = new TimeRangeParams(TimeRange.CUSTOM, todayStart, todayEnd);
            request.setTimeRangeParams(timeRangeParams);
            // 移除 showOnlyHasData 限制，获取所有任务记录
            request.setShowOnlyHasData(false);
            request.getPageParams().setPageSize(10000);
            
            log.info("查询参数构建完成: request={}", request);

            PageResponse<StorageTask> response = dataModelService.getScheduleRecordPageList(dataModelId, request);
            
            log.info("调度记录查询结果: dataModelId={}, totalCount={}, itemsCount={}", 
                dataModelId, response.getTotal(), response.getItems().size());

            // 按存储点分组统计
            java.util.Map<Integer, StoragePointStats> storagePointStatsMap = new java.util.HashMap<>();
            int processedTaskCount = 0;
            int skippedTaskCount = 0;
            int totalWriteCountInfo = 0;

            for (StorageTask task : response.getItems()) {
                log.debug("处理存储任务: taskId={}, executionStatus={}, writeCountInfoSize={}", 
                    task.getId(), task.getExecutionStatus(), 
                    task.getWriteCountInfo() != null ? task.getWriteCountInfo().length : 0);
                
                // 检查任务是否有写入计数信息
                if (task.getWriteCountInfo() != null && task.getWriteCountInfo().length > 0) {
                    totalWriteCountInfo += task.getWriteCountInfo().length;
                    
                    // 统计所有任务，包括成功和失败的
                    if (task.getExecutionStatus() == SeaTunnelJobStatus.FINISHED) {
                        processedTaskCount++;
                        for (StorageTask.TaskWriteCount writeCount : task.getWriteCountInfo()) {
                            Integer storageId = writeCount.getStorageId();
                            if (storageId != null) {
                                StoragePointStats stats = storagePointStatsMap.computeIfAbsent(storageId, 
                                    k -> new StoragePointStats(storageId));
                                
                                // 累加成功量
                                if (writeCount.getSuccessCount() != null) {
                                    stats.addTodayStorage(writeCount.getSuccessCount());
                                    log.debug("存储点{}累加成功量: +{} = {}", storageId, writeCount.getSuccessCount(), stats.getTodayStorage());
                                }
                                
                                // 累加写入失败量
                                if (writeCount.getFailCount() != null) {
                                    stats.addTodayError(writeCount.getFailCount());
                                    log.debug("存储点{}累加写入失败量: +{} = {}", storageId, writeCount.getFailCount(), stats.getTodayErrorCount());
                                }
                            } else {
                                log.warn("存储任务{}的写入计数信息中storageId为null: writeCount={}", task.getId(), writeCount);
                            }
                        }
                    } else {
                        // 处理执行失败的任务
                        skippedTaskCount++;
                        log.debug("处理执行失败任务: taskId={}, executionStatus={}", 
                            task.getId(), task.getExecutionStatus());
                        
                        // 为执行失败的任务分配异常数到相关存储点
                        for (StorageTask.TaskWriteCount writeCount : task.getWriteCountInfo()) {
                            Integer storageId = writeCount.getStorageId();
                            if (storageId != null) {
                                StoragePointStats stats = storagePointStatsMap.computeIfAbsent(storageId, 
                                    k -> new StoragePointStats(storageId));
                                
                                // 执行失败的任务，将预期写入量作为异常数统计
                                // 这里假设任务执行失败时，所有预期写入的数据都变成了异常
                                Long expectedWriteCount = 0L;
                                if (writeCount.getSuccessCount() != null) {
                                    expectedWriteCount += writeCount.getSuccessCount();
                                }
                                if (writeCount.getFailCount() != null) {
                                    expectedWriteCount += writeCount.getFailCount();
                                }
                                
                                if (expectedWriteCount > 0) {
                                    stats.addTodayError(expectedWriteCount);
                                    log.debug("存储点{}累加执行失败异常量: +{} = {}", storageId, expectedWriteCount, stats.getTodayErrorCount());
                                }
                            }
                        }
                    }
                } else {
                    skippedTaskCount++;
                    log.debug("跳过没有写入计数信息的存储任务: taskId={}, hasWriteCountInfo={}", 
                        task.getId(), task.getWriteCountInfo() != null);
                    
                    // 对于没有写入计数信息的失败任务，也需要统计异常
                    if (task.getExecutionStatus() != SeaTunnelJobStatus.FINISHED) {
                        log.debug("任务{}执行失败且无写入计数信息，需要统计异常", task.getId());
                        // 这里可以根据任务类型或历史数据估算异常量
                        // 暂时跳过，避免统计不准确
                    }
                }
            }
            
            log.info("任务处理统计: dataModelId={}, processedTaskCount={}, skippedTaskCount={}, totalWriteCountInfo={}, storagePointCount={}", 
                dataModelId, processedTaskCount, skippedTaskCount, totalWriteCountInfo, storagePointStatsMap.size());

            // 计算总和
            Long todayStorageSum = storagePointStatsMap.values().stream()
                .mapToLong(StoragePointStats::getTodayStorage)
                .sum();
            
            Long todayErrorSum = storagePointStatsMap.values().stream()
                .mapToLong(StoragePointStats::getTodayErrorCount)
                .sum();
            
            log.info("存储统计汇总: dataModelId={}, todayStorageSum={}, todayErrorSum={}", 
                dataModelId, todayStorageSum, todayErrorSum);

            // 构建返回结果
            java.util.Map<String, Object> result = new java.util.HashMap<>();
            result.put("todayStorageSum", todayStorageSum);
            result.put("todayErrorSum", todayErrorSum);
            
            // 转换存储点统计信息
            java.util.List<java.util.Map<String, Object>> storagePoints = storagePointStatsMap.values().stream()
                .map(this::convertToMap)
                .collect(java.util.stream.Collectors.toList());
            
            result.put("storagePoints", storagePoints);
            result.put("remark", "包含成功任务写入统计和执行失败任务异常统计的存储点统计信息");
            
            // 添加调试信息
            result.put("debugInfo", java.util.Map.of(
                "queryTimeRange", java.util.Map.of("start", todayStart.toString(), "end", todayEnd.toString()),
                "totalTasks", response.getTotal(),
                "processedTasks", processedTaskCount,
                "skippedTasks", skippedTaskCount,
                "totalWriteCountInfo", totalWriteCountInfo,
                "storagePointCount", storagePointStatsMap.size()
            ));

            log.info("不同存储点的今日存储统计信息获取完成: dataModelId={}, storagePointCount={}, todayStorageSum={}, todayErrorSum={}",
                dataModelId, storagePointStatsMap.size(), todayStorageSum, todayErrorSum);

            return result;

        } catch (Exception e) {
            log.error("获取不同存储点的今日存储统计信息失败, dataModelId: {}", dataModelId, e);
            return getDefaultStorageStatsByStoragePoints();
        }
    }

    /**
     * 获取指定存储点的今日存储统计信息
     * 包含：
     * 1. 成功任务的写入成功量和写入失败量
     * 2. 执行失败任务的预期写入量（作为异常统计）
     *
     * @param dataModelId 数据模型ID
     * @param storageId 存储点ID
     * @return 指定存储点的今日存储统计信息
     */
    public java.util.Map<String, Object> getStorageStatsByStorageId(Integer dataModelId, Integer storageId) {
        try {
            log.info("开始获取指定存储点的今日存储统计信息, dataModelId: {}, storageId: {}", dataModelId, storageId);

            // 构建今日时间范围
            LocalDateTime todayStart = LocalDateTime.now().toLocalDate().atStartOfDay();
            LocalDateTime todayEnd = LocalDateTime.now();

            // 构建查询参数
            ScheduleRecordRequest request = createInitializedScheduleRecordRequest();
            
            TimeRangeParams timeRangeParams = new TimeRangeParams(TimeRange.CUSTOM, todayStart, todayEnd);
            request.setTimeRangeParams(timeRangeParams);
            request.setShowOnlyHasData(false);
            request.getPageParams().setPageSize(10000);

            PageResponse<StorageTask> response = dataModelService.getScheduleRecordPageList(dataModelId, request);

            StoragePointStats stats = new StoragePointStats(storageId);

            // 统计指定存储点的数据
            for (StorageTask task : response.getItems()) {
                if (task.getWriteCountInfo() != null) {
                    for (StorageTask.TaskWriteCount writeCount : task.getWriteCountInfo()) {
                        if (storageId.equals(writeCount.getStorageId())) {
                            if (task.getExecutionStatus() == SeaTunnelJobStatus.FINISHED) {
                                // 成功任务：累加成功量和写入失败量
                                if (writeCount.getSuccessCount() != null) {
                                    stats.addTodayStorage(writeCount.getSuccessCount());
                                }
                                
                                if (writeCount.getFailCount() != null) {
                                    stats.addTodayError(writeCount.getFailCount());
                                }
                            } else {
                                // 执行失败的任务：将预期写入量作为异常数统计
                                Long expectedWriteCount = 0L;
                                if (writeCount.getSuccessCount() != null) {
                                    expectedWriteCount += writeCount.getSuccessCount();
                                }
                                if (writeCount.getFailCount() != null) {
                                    expectedWriteCount += writeCount.getFailCount();
                                }
                                
                                if (expectedWriteCount > 0) {
                                    stats.addTodayError(expectedWriteCount);
                                    log.debug("存储点{}累加执行失败异常量: +{} = {}", storageId, expectedWriteCount, stats.getTodayErrorCount());
                                }
                            }
                        }
                    }
                }
            }

            // 获取历史总存储量
            Long totalStorage = calculateTotalStorageByStorageId(dataModelId, storageId);

            // 构建返回结果
            java.util.Map<String, Object> result = convertToMap(stats);
            result.put("totalStorage", totalStorage);
            result.put("remark", "包含成功任务写入统计和执行失败任务异常统计的存储点统计信息");

            log.info("指定存储点的今日存储统计信息获取完成: dataModelId={}, storageId={}, todayStorage={}, todayErrorCount={}, totalStorage={}",
                dataModelId, storageId, stats.getTodayStorage(), stats.getTodayErrorCount(), totalStorage);

            return result;

        } catch (Exception e) {
            log.error("获取指定存储点的今日存储统计信息失败, dataModelId: {}, storageId: {}", dataModelId, storageId, e);
            return getDefaultStorageStatsByStorageId(storageId);
        }
    }

    /**
     * 计算指定存储点的历史总存储量
     *
     * @param dataModelId 数据模型ID
     * @param storageId 存储点ID
     * @return 历史总存储量
     */
    private Long calculateTotalStorageByStorageId(Integer dataModelId, Integer storageId) {
        try {
            log.debug("开始计算指定存储点的历史总存储量, dataModelId: {}, storageId: {}", dataModelId, storageId);

            // 构建查询参数（不限制时间范围，获取所有历史数据）
            ScheduleRecordRequest request = createInitializedScheduleRecordRequest();
            request.setShowOnlyHasData(true); // 只获取有数据的记录
            request.getPageParams().setPageSize(50000); // 设置足够大的页面大小获取所有数据

            PageResponse<StorageTask> response = dataModelService.getScheduleRecordPageList(dataModelId, request);
            
            log.debug("历史数据查询结果: dataModelId={}, storageId={}, totalTasks={}, itemsCount={}", 
                dataModelId, storageId, response.getTotal(), response.getItems().size());

            // 计算指定存储点的历史成功量总和
            Long totalStorage = response.getItems().stream()
                .filter(task -> task.getExecutionStatus() == SeaTunnelJobStatus.FINISHED) // 只统计成功的任务
                .flatMap(task -> java.util.Arrays.stream(task.getWriteCountInfo()))
                .filter(writeCount -> storageId.equals(writeCount.getStorageId()))
                .mapToLong(writeCount -> writeCount.getSuccessCount() != null ? writeCount.getSuccessCount() : 0L)
                .sum();

            log.debug("指定存储点历史总存储量计算完成: dataModelId={}, storageId={}, totalStorage={}", 
                dataModelId, storageId, totalStorage);

            return totalStorage;

        } catch (Exception e) {
            log.error("计算指定存储点的历史总存储量失败, dataModelId: {}, storageId: {}", dataModelId, storageId, e);
            return 0L;
        }
    }

    /**
     * 获取默认存储点统计信息
     *
     * @return 默认存储点统计信息
     */
    private java.util.Map<String, Object> getDefaultStorageStatsByStoragePoints() {
        java.util.Map<String, Object> defaultStats = new java.util.HashMap<>();
        defaultStats.put("todayStorageSum", 0L);
        defaultStats.put("todayErrorSum", 0L);
        defaultStats.put("storagePoints", java.util.List.of());
        defaultStats.put("remark", "获取存储点统计信息失败");
        return defaultStats;
    }

    /**
     * 获取指定存储点的默认统计信息
     *
     * @param storageId 存储点ID
     * @return 默认统计信息
     */
    private java.util.Map<String, Object> getDefaultStorageStatsByStorageId(Integer storageId) {
        java.util.Map<String, Object> defaultStats = new java.util.HashMap<>();
        defaultStats.put("storageId", storageId);
        defaultStats.put("todayStorage", 0L);
        defaultStats.put("totalStorage", 0L);
        defaultStats.put("todayErrorCount", 0L);
        defaultStats.put("remark", "获取存储点统计信息失败");
        return defaultStats;
    }

    /**
     * 将存储点统计信息转换为Map
     *
     * @param stats 存储点统计信息
     * @return Map格式的统计信息
     */
    private java.util.Map<String, Object> convertToMap(StoragePointStats stats) {
        java.util.Map<String, Object> map = new java.util.HashMap<>();
        map.put("storageId", stats.getStorageId());
        map.put("todayStorage", stats.getTodayStorage());
        map.put("todayErrorCount", stats.getTodayErrorCount());
        return map;
    }

    /**
     * 存储点统计信息内部类
     */
    private static class StoragePointStats {
        private final Integer storageId;
        private Long todayStorage = 0L;
        private Long todayErrorCount = 0L;

        StoragePointStats(Integer storageId) {
            this.storageId = storageId;
        }

        public void addTodayStorage(Long count) {
            this.todayStorage += count != null ? count : 0L;
        }

        public void addTodayError(Long count) {
            this.todayErrorCount += count != null ? count : 0L;
        }

        public Integer getStorageId() {
            return storageId;
        }

        public Long getTodayStorage() {
            return todayStorage;
        }

        public Long getTodayErrorCount() {
            return todayErrorCount;
        }
    }

    // ==================== 私有工具方法 ====================

    /**
     * 确保ScheduleRecordRequest的pageParams被正确初始化
     *
     * @param request ScheduleRecordRequest对象
     * @return 初始化后的ScheduleRecordRequest对象
     */
    private ScheduleRecordRequest ensurePageParamsInitialized(ScheduleRecordRequest request) {
        if (request.getPageParams() == null) {
            request.setPageParams(new com.trs.moye.base.common.request.PageParams());
        }
        return request;
    }

    /**
     * 创建并初始化一个ScheduleRecordRequest对象
     *
     * @return 初始化后的ScheduleRecordRequest对象
     */
    private ScheduleRecordRequest createInitializedScheduleRecordRequest() {
        ScheduleRecordRequest request = new ScheduleRecordRequest();
        
        // 确保pageParams被正确初始化
        request = ensurePageParamsInitialized(request);
        
        // 🔧 修复：确保timeRangeParams被正确初始化，避免空指针异常
        if (request.getTimeRangeParams() == null) {
            // 使用TimeRange.ALL来获取所有历史数据
            TimeRangeParams timeRangeParams = new TimeRangeParams(TimeRange.ALL);
            request.setTimeRangeParams(timeRangeParams);
        }
        
        return request;
    }
}
