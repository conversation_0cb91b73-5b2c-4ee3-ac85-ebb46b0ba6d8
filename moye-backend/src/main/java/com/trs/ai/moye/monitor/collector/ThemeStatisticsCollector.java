package com.trs.ai.moye.monitor.collector;

import com.trs.ai.moye.data.model.dao.BatchTaskRecordMapper;
import com.trs.ai.moye.data.model.dao.StorageTaskMapper;
import com.trs.ai.moye.homepage.dao.HomePageThemeStatisticsMapper;
import com.trs.ai.moye.homepage.entity.HomePageThemeStatistics;
import com.trs.ai.moye.monitor.dao.DataProcessRecordMapper;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 主题库收集器
 *
 * <AUTHOR>
 * @since 2025/6/24
 */
@Service
public class ThemeStatisticsCollector extends AbstractStatisticsCollector<HomePageThemeStatistics> {

    private final HomePageThemeStatisticsMapper homePageThemeStatisticsMapper;

    @Autowired
    public ThemeStatisticsCollector(DataModelMapper dataModelMapper,
        BatchTaskRecordMapper batchTaskRecordMapper,
        DataProcessRecordMapper dataProcessRecordMapper,
        StorageTaskMapper storageTaskMapper,
        HomePageThemeStatisticsMapper homePageThemeStatisticsMapper) {
        super(dataModelMapper, batchTaskRecordMapper, dataProcessRecordMapper, storageTaskMapper,
            ModelLayer.THEME, 3);
        this.homePageThemeStatisticsMapper = homePageThemeStatisticsMapper;
    }

    @Override
    public List<HomePageThemeStatistics> selectAllStatistics() {
        return homePageThemeStatisticsMapper.selectAll(null, null, null, null);
    }

    @Override
    public List<HomePageThemeStatistics> selectBatchStatistics(LocalDateTime start, LocalDateTime end) {
        return batchTaskRecordMapper.selectLastDayCountByModelIdTheme(start, end);
    }

    @Override
    public List<HomePageThemeStatistics> selectBatchStorageStatistics(LocalDateTime start, LocalDateTime end) {
        return batchTaskRecordMapper.selectBatchStorageTheme(start, end);
    }

    @Override
    public List<HomePageThemeStatistics> selectStreamStatistics(LocalDateTime start, LocalDateTime end,
        List<Integer> modelIds) {
        return dataProcessRecordMapper.selectLastDayCountByModelIdTheme(start, end, modelIds);
    }

    @Override
    public List<HomePageThemeStatistics> selectStorageStatistics(LocalDateTime start, LocalDateTime end,
        int layerType) {
        return storageTaskMapper.selectLastDayCountByModelIdTheme(start, end, layerType);
    }

    @Override
    public void updateStatistics(List<HomePageThemeStatistics> statistics) {
        statistics.forEach(homePageThemeStatisticsMapper::updateData);
    }

    @Override
    public void insertNewStatistics(List<HomePageThemeStatistics> newStatistics) {
        homePageThemeStatisticsMapper.insert(newStatistics);
    }

    @Override
    public String getStatisticsTypeName() {
        return "主题库";
    }
}
