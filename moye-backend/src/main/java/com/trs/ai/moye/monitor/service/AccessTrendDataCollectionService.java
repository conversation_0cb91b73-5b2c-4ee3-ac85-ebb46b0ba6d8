package com.trs.ai.moye.monitor.service;

import com.trs.ai.moye.redis.starter.service.RedisService;
import com.trs.ai.moye.monitor.dto.AccessMonitorDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;

/**
 * 接入趋势数据采集服务
 * 负责定时采集接入监控数据并存储到Redis时间序列
 * 使用基于调度记录的计算方式，与接入监控保持一致
 * 
 * 更新说明：
 * - 已移除复杂的智能采样算法，直接返回Redis中存在的所有数据点
 * - 确保数据的完整性和准确性，避免因采样导致的数据丢失
 * - 支持"今日"、"昨日"、"近一周"等时间范围的数据获取
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@Slf4j
@Service
public class AccessTrendDataCollectionService {

    @Resource
    private RedisService redisService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    @Resource
    private ScheduleBasedMetricsService scheduleBasedMetricsService;

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final int TREND_DATA_EXPIRE_DAYS = 30; // 趋势数据保留30天
    
    // 🔧 新增：统一时区设置，确保存储和获取时使用相同的时区
    // 使用系统默认时区，避免UTC时区导致的8小时偏差
    private static final java.time.ZoneId SYSTEM_ZONE_ID = java.time.ZoneId.systemDefault();

    /**
     * 采集并存储接入趋势数据点
     * 建议每分钟调用一次
     *
     * @param dataModelId 数据模型ID
     */
    public void collectAccessTrendPoint(Integer dataModelId) {
        try {
            LocalDateTime now = LocalDateTime.now();
            String timePoint = now.format(TIME_FORMATTER);
            
            log.info("🔍 开始采集接入趋势数据点, dataModelId: {}, timePoint: {}", dataModelId, timePoint);

            // 获取当前时刻的接入指标
            AccessMetrics currentMetrics = getCurrentAccessMetrics(dataModelId);
            log.info("📊 获取到接入指标, dataModelId: {}, accessSpeed: {}, accessCount: {}", 
                dataModelId, currentMetrics.getAccessSpeed(), currentMetrics.getAccessCount());
            
            // 存储接入速度趋势点
            log.info("💾 开始存储接入速度趋势点, dataModelId: {}, speed: {}", dataModelId, currentMetrics.getAccessSpeed());
            storeAccessSpeedTrendPoint(dataModelId, timePoint, currentMetrics.getAccessSpeed());
            
            // 存储接入数量趋势点
            log.info("💾 开始存储接入数量趋势点, dataModelId: {}, count: {}", dataModelId, currentMetrics.getAccessCount());
            storeAccessCountTrendPoint(dataModelId, timePoint, currentMetrics.getAccessCount());
            
            // 🔧 新增：存储积压量趋势点
            log.info("💾 开始存储积压量趋势点, dataModelId: {}, lagCount: {}", dataModelId, currentMetrics.getLagCount());
            storeLagCountTrendPoint(dataModelId, timePoint, currentMetrics.getLagCount());
            
            log.info("✅ 接入趋势数据点采集完成, dataModelId: {}, timePoint: {}", dataModelId, timePoint);
            
        } catch (Exception e) {
            log.error("❌ 采集接入趋势数据点失败, dataModelId: {}", dataModelId, e);
        }
    }

    /**
     * 获取接入速度趋势数据
     *
     * @param dataModelId 数据模型ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 趋势数据点列表
     */
    public List<TrendPoint> getAccessSpeedTrend(Integer dataModelId, LocalDateTime startTime, LocalDateTime endTime) {
        String key = buildTrendKey(dataModelId, "speed");
        return getTrendDataInRange(key, startTime, endTime);
    }

    /**
     * 获取接入数量趋势数据
     *
     * @param dataModelId 数据模型ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 趋势数据点列表
     */
    public List<TrendPoint> getAccessCountTrend(Integer dataModelId, LocalDateTime startTime, LocalDateTime endTime) {
        String key = buildTrendKey(dataModelId, "count");
        return getTrendDataInRange(key, startTime, endTime);
    }

    /**
     * 🔧 新增：获取积压量趋势数据
     *
     * @param dataModelId 数据模型ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 趋势数据点列表
     */
    public List<TrendPoint> getLagCountTrend(Integer dataModelId, LocalDateTime startTime, LocalDateTime endTime) {
        String key = buildTrendKey(dataModelId, "lag");
        return getTrendDataInRange(key, startTime, endTime);
    }

    // ==================== 智能采样方法 ====================

    /**
     * 智能获取接入速度趋势数据 - 直接返回Redis原始数据，不进行采样
     *
     * @param dataModelId 数据模型ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param targetPoints 目标数据点数量（已废弃，保留参数兼容性）
     * @return 趋势数据点列表
     */
    public List<TrendPoint> getSmartAccessSpeedTrend(Integer dataModelId, LocalDateTime startTime, LocalDateTime endTime, int targetPoints) {
        try {
            log.info("🔍 开始获取接入速度趋势数据（直接返回Redis原始数据）, dataModelId: {}, startTime: {}, endTime: {}", 
                dataModelId, startTime, endTime);
            
            // 直接从Redis获取原始数据，不进行采样
            List<TrendPoint> rawTrendPoints = getAccessSpeedTrend(dataModelId, startTime, endTime);
            log.info("📊 从Redis获取到原始数据点数量: {}", rawTrendPoints.size());
            
            // 按时间排序
            rawTrendPoints.sort((a, b) -> {
                try {
                    LocalDateTime timeA = LocalDateTime.parse(a.getTime(), TIME_FORMATTER);
                    LocalDateTime timeB = LocalDateTime.parse(b.getTime(), TIME_FORMATTER);
                    return timeA.compareTo(timeB);
                } catch (Exception e) {
                    return 0;
                }
            });
            
            log.info("✅ 接入速度趋势数据获取完成, 数据点数量: {}", rawTrendPoints.size());
            return rawTrendPoints;
            
        } catch (Exception e) {
            log.error("❌ 获取接入速度趋势数据失败, dataModelId: {}", dataModelId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 智能获取接入数量趋势数据 - 直接返回Redis原始数据，不进行采样
     *
     * @param dataModelId 数据模型ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param targetPoints 目标数据点数量（已废弃，保留参数兼容性）
     * @return 趋势数据点列表
     */
    public List<TrendPoint> getSmartAccessCountTrend(Integer dataModelId, LocalDateTime startTime, LocalDateTime endTime, int targetPoints) {
        try {
            log.info("🔍 开始获取接入数量趋势数据（直接返回Redis原始数据）, dataModelId: {}, startTime: {}, endTime: {}", 
                dataModelId, startTime, endTime);
            
            // 直接从Redis获取原始数据，不进行采样
            List<TrendPoint> rawTrendPoints = getAccessCountTrend(dataModelId, startTime, endTime);
            log.info("📊 从Redis获取到原始数据点数量: {}", rawTrendPoints.size());
            
            // 按时间排序
            rawTrendPoints.sort((a, b) -> {
                try {
                    LocalDateTime timeA = LocalDateTime.parse(a.getTime(), TIME_FORMATTER);
                    LocalDateTime timeB = LocalDateTime.parse(b.getTime(), TIME_FORMATTER);
                    return timeA.compareTo(timeB);
                } catch (Exception e) {
                    return 0;
                }
            });
            
            log.info("✅ 接入数量趋势数据获取完成, 数据点数量: {}", rawTrendPoints.size());
            return rawTrendPoints;
            
        } catch (Exception e) {
            log.error("❌ 获取接入数量趋势数据失败, dataModelId: {}", dataModelId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 智能获取积压量趋势数据 - 直接返回Redis原始数据，不进行采样
     *
     * @param dataModelId 数据模型ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param targetPoints 目标数据点数量（已废弃，保留参数兼容性）
     * @return 趋势数据点列表
     */
    public List<TrendPoint> getSmartLagCountTrend(Integer dataModelId, LocalDateTime startTime, LocalDateTime endTime, int targetPoints) {
        try {
            log.info("🔍 开始获取积压量趋势数据（直接返回Redis原始数据）, dataModelId: {}, startTime: {}, endTime: {}", 
                dataModelId, startTime, endTime);
            
            // 直接从Redis获取原始数据，不进行采样
            List<TrendPoint> rawTrendPoints = getLagCountTrend(dataModelId, startTime, endTime);
            log.info("📊 从Redis获取到原始数据点数量: {}", rawTrendPoints.size());
            
            // 按时间排序
            rawTrendPoints.sort((a, b) -> {
                try {
                    LocalDateTime timeA = LocalDateTime.parse(a.getTime(), TIME_FORMATTER);
                    LocalDateTime timeB = LocalDateTime.parse(b.getTime(), TIME_FORMATTER);
                    return timeA.compareTo(timeB);
                } catch (Exception e) {
                    return 0;
                }
            });
            
            log.info("✅ 积压量趋势数据获取完成, 数据点数量: {}", rawTrendPoints.size());
            return rawTrendPoints;
            
        } catch (Exception e) {
            log.error("❌ 获取积压量趋势数据失败, dataModelId: {}", dataModelId, e);
            return new ArrayList<>();
        }
    }

    // ==================== 移除智能采样方法 ====================
    // 以下方法已被移除，直接返回Redis原始数据
    // 
    // 移除原因：
    // 1. 智能采样算法复杂，容易产生数据匹配问题
    // 2. 搜索窗口限制（15分钟）导致"昨日"和"近一周"数据无法匹配
    // 3. 时间点生成逻辑与Redis实际数据时间点不一致
    // 4. 采样后的数据可能丢失重要的趋势信息
    // 
    // 解决方案：
    // 直接返回Redis中存在的所有数据点，确保数据完整性
    // 前端可以根据需要自行进行数据展示优化
    // 
    // private List<TrendPoint> smartSampleAndFill(...)
    // private Double findBestMatchingRealValue(...)
    // private List<TrendPoint> generateUniformZeroData(...)

    /**
     * 数据质量报告
     *
     * @param dataModelId 数据模型ID
     * @param startTime  开始时间
     * @param endTime  结束时间
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    public Map<String, Object> getDataQualityReport(Integer dataModelId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> report = new HashMap<>();
        
        try {
            // 获取原始数据
            List<TrendPoint> rawPoints = getAccessSpeedTrend(dataModelId, startTime, endTime);
            
            // 统计真实数据点数量
            long realDataCount = rawPoints.stream()
                .filter(point -> point.getValue() != null && point.getValue() > 0.0)
                .count();
            
            // 统计总数据点数量
            long totalDataCount = rawPoints.size();
            
            report.put("timeRange", Map.of(
                "startTime", startTime.format(TIME_FORMATTER),
                "endTime", endTime.format(TIME_FORMATTER)
            ));
            report.put("rawDataPoints", totalDataCount);
            report.put("realDataPoints", realDataCount);
            report.put("dataCoverage", totalDataCount > 0 ? String.format("%.2f%%", (double) realDataCount / totalDataCount * 100) : "0.00%");
            report.put("timestamp", LocalDateTime.now().format(TIME_FORMATTER));
            report.put("note", "已移除智能采样，直接返回Redis原始数据");
            
            log.info("📊 数据质量报告生成完成: dataModelId={}, 真实数据覆盖率={}%", 
                dataModelId, report.get("dataCoverage"));
            
        } catch (Exception e) {
            log.error("❌ 生成数据质量报告失败, dataModelId: {}", dataModelId, e);
            report.put("error", e.getMessage());
        }
        
        return report;
    }

    // ==================== 移除目标点数计算方法 ====================
    // 由于已移除智能采样，不再需要计算目标数据点数量
    // 直接返回Redis中存在的所有数据点
    // 
    // private int calculateTargetPoints(LocalDateTime startTime, LocalDateTime endTime) {...}

    // ==================== 私有方法 ====================

    /**
     * 获取当前时刻的接入指标 - 使用基于调度记录的计算方式
     *
     * @param dataModelId  数据模型ID
     * @return {@link AccessMetrics }
     */
    public AccessMetrics getCurrentAccessMetrics(Integer dataModelId) {
        log.info("🔍 开始获取当前接入指标（基于调度记录）, dataModelId: {}", dataModelId);
        
        try {
            // 使用ScheduleBasedMetricsService计算接入指标
            AccessMonitorDataDTO.AccessMetrics scheduleMetrics = scheduleBasedMetricsService.calculateAccessMetrics(dataModelId);
            
            // 提取接入速度（条/分）
            Double accessSpeed = scheduleMetrics.getAccessSpeed() != null
                    ?
                scheduleMetrics.getAccessSpeed().doubleValue() : 0.0;
            
            // 提取接入总量
            Long accessCount = scheduleMetrics.getAccessTotalCount() != null
                    ?
                scheduleMetrics.getAccessTotalCount().longValue() : 0L;
            
            // 🔧 新增：提取积压量
            Long lagCount = scheduleMetrics.getSourceLagCount() != null
                    ?
                scheduleMetrics.getSourceLagCount().longValue() : 0L;
            
            log.info("📋 基于调度记录获取数据成功, dataModelId: {}, accessSpeed: {}, accessCount: {}, lagCount: {}", 
                dataModelId, accessSpeed, accessCount, lagCount);
            
            AccessMetrics metrics = AccessMetrics.builder()
                    .accessSpeed(accessSpeed)
                    .accessCount(accessCount)
                    .lagCount(lagCount)
                    .build();
            
            log.info("✅ 接入指标获取完成, dataModelId: {}, metrics: speed={}, count={}, lagCount={}", 
                dataModelId, metrics.getAccessSpeed(), metrics.getAccessCount(), metrics.getLagCount());
            return metrics;
            
        } catch (Exception e) {
            log.error("❌ 获取基于调度记录的接入指标失败, dataModelId: {}, 使用默认值", dataModelId, e);
            
            // 异常情况下返回默认值
            return AccessMetrics.builder()
                    .accessSpeed(0.0)
                    .accessCount(0L)
                    .lagCount(0L)
                    .build();
        }
    }





    /**
     * 存储接入速度趋势点（基于调度记录计算）
     *
     * @param dataModelId 数据模型ID
     * @param timePoint 时间点（格式：yyyy-MM-dd HH:mm:ss）
     * @param speed 接入速度（条/分，基于调度记录计算）
     */
    private void storeAccessSpeedTrendPoint(Integer dataModelId, String timePoint, Double speed) {
        String key = buildTrendKey(dataModelId, "speed");
        log.info("🚀 存储接入速度趋势点（基于调度记录）, dataModelId: {}, key: {}, timePoint: {}, speed: {} 条/分", 
            dataModelId, key, timePoint, speed);
        storeTrendPoint(key, timePoint, speed);
    }

    /**
     * 存储接入数量趋势点（使用接入总量）
     *
     * @param dataModelId 数据模型ID
     * @param timePoint 时间点（格式：yyyy-MM-dd HH:mm:ss）
     * @param count 接入总量（基于调度记录计算）
     */
    private void storeAccessCountTrendPoint(Integer dataModelId, String timePoint, Long count) {
        String key = buildTrendKey(dataModelId, "count");
        log.info("📊 存储接入数量趋势点（基于调度记录）, dataModelId: {}, key: {}, timePoint: {}, count: {}", 
            dataModelId, key, timePoint, count);
        storeTrendPoint(key, timePoint, count.doubleValue());
    }

    /**
     * 存储积压量趋势点（使用接入总量）
     *
     * @param dataModelId 数据模型ID
     * @param timePoint 时间点（格式：yyyy-MM-dd HH:mm:ss）
     * @param count 积压量（基于调度记录计算）
     */
    private void storeLagCountTrendPoint(Integer dataModelId, String timePoint, Long count) {
        String key = buildTrendKey(dataModelId, "lag");
        log.info("📊 存储积压量趋势点（基于调度记录）, dataModelId: {}, key: {}, timePoint: {}, count: {}", 
            dataModelId, key, timePoint, count);
        storeTrendPoint(key, timePoint, count.doubleValue());
    }

    /**
     * 存储趋势数据点到Redis
     *
     * @param key Redis键
     * @param timePoint 时间点（格式：yyyy-MM-dd HH:mm:ss）
     * @param value 趋势数据点的值
     */
    private void storeTrendPoint(String key, String timePoint, Double value) {
        try {
            log.info("💾 开始存储趋势数据点到Redis, key: {}, timePoint: {}, value: {}", key, timePoint, value);
            
            // 使用Redis的ZSet存储时间序列数据
            // score为时间戳，value为"timePoint:value"格式
            LocalDateTime time = LocalDateTime.parse(timePoint, TIME_FORMATTER);
            // 🔧 修复：使用系统默认时区，确保与获取数据时的时区一致
            // 避免UTC时区导致的8小时偏差
            long timestamp = time.atZone(SYSTEM_ZONE_ID).toEpochSecond();
            
            String member = timePoint + ":" + value;
            log.debug("🔧 构建ZSet数据, key: {}, member: {}, timestamp: {}", key, member, timestamp);
            
            // 检查Redis连接
            if (redisTemplate == null) {
                log.error("❌ RedisTemplate为null, 无法存储数据, key: {}", key);
                return;
            }
            
            // 🔧 修复：使用RedisTemplate直接操作ZSet
            Boolean addResult = redisTemplate.opsForZSet().add(key, member, timestamp);
            log.info("📝 ZSet添加结果, key: {}, member: {}, timestamp: {}, addResult: {}", 
                key, member, timestamp, addResult);
            
            // 验证数据是否成功写入
            Long zsetSize = redisTemplate.opsForZSet().zCard(key);
            log.info("📊 ZSet当前大小, key: {}, size: {}", key, zsetSize);
            
            // 设置过期时间（转换为秒）
            long expireSeconds = TREND_DATA_EXPIRE_DAYS * 24 * 60 * 60;
            Boolean expireResult = redisService.expire(key, expireSeconds);
            log.info("⏰ 设置过期时间结果, key: {}, expireSeconds: {}, expireResult: {}", 
                key, expireSeconds, expireResult);
            
            // 清理过期数据（保留最近30天）
            long expireTimestamp = timestamp - (TREND_DATA_EXPIRE_DAYS * 24 * 60 * 60);
            Long removedCount = redisTemplate.opsForZSet().removeRangeByScore(key, 0, expireTimestamp);
            log.info("🗑️ 清理过期数据结果, key: {}, expireTimestamp: {}, removedCount: {}", 
                key, expireTimestamp, removedCount);
            
            // 最终验证
            Long finalSize = redisTemplate.opsForZSet().zCard(key);
            log.info("✅ 趋势数据点存储完成, key: {}, finalSize: {}", key, finalSize);
            
        } catch (Exception e) {
            log.error("❌ 存储趋势数据点失败, key: {}, timePoint: {}, value: {}", key, timePoint, value, e);
        }
    }

    /**
     * 获取指定时间范围内的趋势数据
     *
     * @param key Redis键
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link List }<{@link TrendPoint }>
     */
    private List<TrendPoint> getTrendDataInRange(String key, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 🔧 修复：对于"今日"范围，确保开始时间是从0:00开始
            LocalDateTime actualStartTime = startTime;
            LocalDateTime now = LocalDateTime.now();
            
            // 🔧 修复：简化"今日"范围判断，只要日期是今天，就强制设置为0:00:00开始
            if (startTime.toLocalDate().equals(now.toLocalDate())) {
                // 强制设置为当天的0:00:00，确保获取今天完整的数据
                actualStartTime = startTime.toLocalDate().atStartOfDay();
                log.info("🕐 今日范围：强制设置开始时间为0:00:00, 原开始时间: {}, 调整后: {}", 
                    startTime.format(TIME_FORMATTER), actualStartTime.format(TIME_FORMATTER));
            }
            
            // 🔧 修复：使用系统默认时区，确保与存储时的时区一致
            long startTimestamp = actualStartTime.atZone(SYSTEM_ZONE_ID).toEpochSecond();
            long endTimestamp = endTime.atZone(SYSTEM_ZONE_ID).toEpochSecond();
            
            log.info("🔍 从Redis获取趋势数据: key={}, 原开始时间={}, 调整后开始时间={}, 结束时间={}, startTimestamp={}, endTimestamp={}", 
                key, startTime, actualStartTime, endTime, startTimestamp, endTimestamp);
            
            // 🔧 新增：记录时区信息，帮助调试
            log.info("🕐 时区信息: 系统默认时区={}, 当前时间={}, 请求时间范围: {} 到 {}", 
                SYSTEM_ZONE_ID, now.format(TIME_FORMATTER), startTime.format(TIME_FORMATTER), endTime.format(TIME_FORMATTER));
            
            // 使用RedisTemplate的ZSet操作获取指定时间范围的数据
            Set<Object> members = redisTemplate.opsForZSet().rangeByScore(key, startTimestamp, endTimestamp);
            
            List<TrendPoint> trendPoints = new ArrayList<>();
            if (members != null) {
                log.info("📊 Redis返回原始数据数量: {}", members.size());
                
                // 🔧 新增：记录时间范围信息，帮助调试
                LocalDateTime minTime = null;
                LocalDateTime maxTime = null;
                
                for (Object member : members) {
                    String memberStr = member.toString();
                    log.debug("🔍 解析Redis数据: {}", memberStr);
                    
                    // 🔧 修复：正确解析Redis中存储的数据格式
                    // 存储格式：timePoint:value (例如: "2025-08-26 08:01:01:2975.0")
                    String[] parts = memberStr.split(":");
                    if (parts.length >= 4) {
                        // 重新组装时间字符串：parts[0]:parts[1]:parts[2] 是时间，parts[3]是数值
                        String timePoint = parts[0] + ":" + parts[1] + ":" + parts[2];
                        Double value = Double.parseDouble(parts[3]);
                        
                        // 🔧 新增：验证数据点时间是否在请求范围内，只添加范围内的数据点
                        try {
                            LocalDateTime pointTime = LocalDateTime.parse(timePoint, TIME_FORMATTER);
                            
                            // 只添加在请求时间范围内的数据点
                            if (!pointTime.isBefore(actualStartTime) && !pointTime.isAfter(endTime)) {
                                trendPoints.add(new TrendPoint(timePoint, value));
                                
                                // 记录时间范围
                                if (minTime == null || pointTime.isBefore(minTime)) {
                                    minTime = pointTime;
                                }
                                if (maxTime == null || pointTime.isAfter(maxTime)) {
                                    maxTime = pointTime;
                                }
                                
                                log.debug("✅ 成功解析并添加数据点: 时间={}, 值={}", timePoint, value);
                            } else {
                                log.debug("⏰ 跳过超出范围的数据点: 时间={}, 值={}, 请求范围: {} 到 {}", 
                                    timePoint, value, actualStartTime.format(TIME_FORMATTER), endTime.format(TIME_FORMATTER));
                            }
                        } catch (Exception e) {
                            log.warn("⚠️ 解析时间字符串失败: {}", timePoint, e);
                        }
                    } else {
                        log.warn("⚠️ 数据格式不正确，跳过: {}", memberStr);
                    }
                }
                
                // 🔧 新增：记录实际获取到的时间范围
                if (minTime != null && maxTime != null) {
                    log.info("📊 Redis数据时间范围: {} 到 {}, 数据点数量: {}", 
                        minTime.format(TIME_FORMATTER), maxTime.format(TIME_FORMATTER), trendPoints.size());
                    
                    // 🔧 新增：验证数据是否在请求的时间范围内
                    if (minTime.isBefore(actualStartTime)) {
                        log.warn("⚠️ 警告：Redis返回的数据包含早于请求开始时间的数据点！最早数据时间: {}, 请求开始时间: {}", 
                            minTime.format(TIME_FORMATTER), actualStartTime.format(TIME_FORMATTER));
                    }
                    if (maxTime.isAfter(endTime)) {
                        log.warn("⚠️ 警告：Redis返回的数据包含晚于请求结束时间的数据点！最晚数据时间: {}, 请求结束时间: {}", 
                            maxTime.format(TIME_FORMATTER), endTime.format(TIME_FORMATTER));
                    }
                }
            }
            
            log.info("📊 Redis数据获取完成: key={}, 数据点数量={}", key, trendPoints.size());
            return trendPoints;
            
        } catch (Exception e) {
            log.error("❌ 获取趋势数据失败, key: {}, startTime: {}, endTime: {}", key, startTime, endTime, e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建趋势数据Redis键
     *
     * @param dataModelId 数据模型ID
     * @param metricType 指标类型（如 "speed", "count", "connection"）
     * @return {@link String }
     */
    private String buildTrendKey(Integer dataModelId, String metricType) {
        String key = String.format("seatunnel:trend:ods:%s:%d", metricType, dataModelId);
        log.debug("🔑 构建Redis键, dataModelId: {}, metricType: {}, key: {}", dataModelId, metricType, key);
        return key;
    }


    // ==================== 内部类 ====================

    /**
     * 接入指标（基于调度记录计算）
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class AccessMetrics {
        private Double accessSpeed;    // 接入速度（条/分，基于调度记录计算）
        private Long accessCount;      // 接入总量（基于调度记录计算）
        private Long lagCount;        // 积压量（基于调度记录计算）
    }

    /**
     * 趋势数据点
     */
    @lombok.Data
    @lombok.AllArgsConstructor
    public static class TrendPoint {
        private String time;   // 时间点
        private Double value;  // 数值
    }
}
