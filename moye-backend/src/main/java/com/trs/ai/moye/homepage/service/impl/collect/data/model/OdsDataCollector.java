package com.trs.ai.moye.homepage.service.impl.collect.data.model;

import com.trs.ai.moye.homepage.entity.AggregatedStat;
import com.trs.ai.moye.homepage.entity.BaseStat;
import com.trs.ai.moye.homepage.entity.DashBoardData;
import com.trs.ai.moye.homepage.enums.HomePageShowData;
import com.trs.ai.moye.homepage.enums.StatType;
import com.trs.ai.moye.monitor.dao.DataAccessTraceMapper;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 贴源层 a. 已建贴源表：已建表的贴源表数量 b. 今日接入量：所有贴源表今日接入数量；
 *
 * <AUTHOR>
 * @since 2025/1/22
 **/

@Service
public class OdsDataCollector extends DataModelDataCollectService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataAccessTraceMapper dataAccessTraceMapper;

    @Override
    public List<DashBoardData> collectData() {
        Long count = dataModelMapper.selectCountCreateTableStatus(getLayer());
        Long accessCount = dataAccessTraceMapper.selectAccessTodayCount();
        return DashBoardData.buildData(HomePageShowData.ODS_LAYER, count, accessCount);
    }

    @Override
    public AggregatedStat getIncrementStat(LocalDateTime startTime, LocalDateTime endTime) {
        BaseStat streamAccessStat = streamAccessMapper.selectAccessStatsByLayerId(startTime, endTime,
            getModelLayerId());
        return AggregatedStat.from(streamAccessStat, getStatType(), endTime);
    }


    @Override
    public ModelLayer getLayer() {
        return ModelLayer.ODS;
    }

    @Override
    protected StatType getStatType() {
        return StatType.ODS_STORAGE;
    }
}
