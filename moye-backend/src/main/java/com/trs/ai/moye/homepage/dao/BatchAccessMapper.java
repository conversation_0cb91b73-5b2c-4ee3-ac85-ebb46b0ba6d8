package com.trs.ai.moye.homepage.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.ai.moye.homepage.entity.BaseStat;
import com.trs.moye.base.common.enums.ModelLayer;
import java.time.LocalDateTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2025/4/22
 **/

@DS("clickhouse")
@Mapper
public interface BatchAccessMapper {

    /**
     * 查询接入统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param layer     数据建模
     * @return 接入数据
     */
    BaseStat selectStorageStatsByLayerId(@Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime, @Param("layer") ModelLayer layer);


}
