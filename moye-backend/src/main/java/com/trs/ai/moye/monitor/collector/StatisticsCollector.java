package com.trs.ai.moye.monitor.collector;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 要素库/主题库/专题库统计信息收集器接口
 *
 * <AUTHOR>
 * @param <T> 统计信息类型
 * @since 2025/6/24
 */
public interface StatisticsCollector<T> {

    /**
     * 获取所有统计信息
     *
     * @return 统计信息列表
     */
    List<T> selectAllStatistics();

    /**
     * 根据层获取所有模型ID
     *
     * @return 模型ID列表
     */
    List<Integer> selectModelIdsByLayer();

    /**
     * 获取批处理批次统计信息
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 统计信息列表
     */
    List<T> selectBatchStatistics(LocalDateTime start, LocalDateTime end);

    /**
     * 获取批处理存储统计信息
     *
     * @param start 开始时间
     * @param end   结束时间
     * @return 统计信息列表
     */
    List<T> selectBatchStorageStatistics(LocalDateTime start, LocalDateTime end);

    /**
     * 获取流处理统计信息
     *
     * @param start    开始时间
     * @param end      结束时间
     * @param modelIds 模型ID列表
     * @return 统计信息列表
     */
    List<T> selectStreamStatistics(LocalDateTime start, LocalDateTime end, List<Integer> modelIds);

    /**
     * 获取流处理存储统计信息
     *
     * @param start     开始时间
     * @param end       结束时间
     * @param layerType 层类型
     * @return 统计信息列表
     */
    List<T> selectStorageStatistics(LocalDateTime start, LocalDateTime end, int layerType);

    /**
     * 插入所有统计信息
     *
     * @param modelIds 模型ID列表
     */
    void insertAllStatistics(List<Integer> modelIds);

    /**
     * 更新统计信息
     *
     * @param statistics 统计信息列表
     */
    void updateStatistics(List<T> statistics);

    /**
     * 插入新的统计信息
     *
     * @param newStatistics 新的统计信息列表
     */
    void insertNewStatistics(List<T> newStatistics);

    /**
     * 获取统计类型名称
     *
     * @return 统计类型名称
     */
    String getStatisticsTypeName();


}
