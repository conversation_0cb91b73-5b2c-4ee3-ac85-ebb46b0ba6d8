package com.trs.ai.moye.homepage.service.impl.collect.data.model;

import com.trs.ai.moye.homepage.dao.BatchAccessMapper;
import com.trs.ai.moye.homepage.dao.StorageStatsMapper;
import com.trs.ai.moye.homepage.dao.StreamAccessMapper;
import com.trs.ai.moye.homepage.entity.AggregatedStat;
import com.trs.ai.moye.homepage.entity.BaseStat;
import com.trs.ai.moye.homepage.enums.StatType;
import com.trs.ai.moye.homepage.service.DataCollectService;
import com.trs.moye.base.common.enums.ModelLayer;
import java.time.LocalDateTime;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2025/4/22
 **/
public abstract class DataModelDataCollectService implements DataCollectService {

    @Resource
    protected StreamAccessMapper streamAccessMapper;

    @Resource
    private BatchAccessMapper batchAccessMapper;

    @Resource
    private StorageStatsMapper storageStatsMapper;

    /**
     * 获取数据收集器对应的数据建模
     *
     * @return {@link ModelLayer}
     */
    public abstract ModelLayer getLayer();

    /**
     * 获取数据收集器对应的数据建模id
     *
     * @return 数据建模对应的id
     */
    public Integer getModelLayerId() {
        return getLayer().getId();
    }

    protected Long getTotal() {
        BaseStat baseStat = storageStatsMapper.selectStatsByType(getStatType());
        return baseStat.getTotal();
    }

    @Override
    public AggregatedStat getIncrementStat(LocalDateTime startTime, LocalDateTime endTime) {
        BaseStat streamAccessStat = streamAccessMapper.selectStorageStatsByLayerId(startTime, endTime,
            getModelLayerId());
        BaseStat batchAccessStat = batchAccessMapper.selectStorageStatsByLayerId(startTime, endTime, getLayer());
        BaseStat totalStat = streamAccessStat.add(batchAccessStat);
        return AggregatedStat.from(totalStat, getStatType(), endTime);
    }

    protected abstract StatType getStatType();
}
