package com.trs.ai.moye.monitor.controller;


import com.trs.ai.moye.monitor.dao.DataAccessTraceMapper;
import com.trs.ai.moye.monitor.dao.StorageTaskViewMapper;
import com.trs.ai.moye.monitor.entity.AccessTaskUnit;
import com.trs.ai.moye.monitor.request.ProcessDataTrendRequest;
import com.trs.ai.moye.monitor.request.StatisticRequest;
import com.trs.ai.moye.monitor.response.statistics.DataStatisticsResponse;
import com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics;
import com.trs.ai.moye.monitor.response.statistics.TaskStatisticsListResponse;
import com.trs.ai.moye.monitor.service.DataStatisticsService;
import com.trs.ai.moye.monitor.service.ProcessService;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.PageResponse;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据监控-统计页
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/8/27 16:16
 **/
@Validated
@RestController
@RequestMapping("/statistics")
public class DataStatisticsController {

    public static final String DAILY_STATISTICS = "day";

    public static final String ON_TIME_STATISTICS = "hour";

    @Resource
    private DataAccessTraceMapper dataAccessTraceMapper;
    @Resource
    private StorageTaskViewMapper storageTaskViewMapper;
    @Resource
    private ProcessService processService;
    @Resource
    private DataStatisticsService dataStatisticsService;

    /**
     * 接入数据量统计 数据总量，正常数，异常数 <a href="http://192.168.210.40:3001/project/4220/interface/api/163330">...</a>
     *
     * @return java.lang.Object
     * <AUTHOR>
     * @since 2024/8/27 17:02
     */

    @GetMapping("/access-volume")
    public DataStatisticsResponse accessStatistics() {
        return new DataStatisticsResponse(
            dataAccessTraceMapper.selectAccessAllCount());
    }

    /**
     * 任务统计
     *
     * @return 统计指标
     */
    @GetMapping("/task-statistics")
    public DataStatisticsResponse taskStatistics() {
        return new DataStatisticsResponse(
            storageTaskViewMapper.selectStatistics());
    }

    /**
     * 接入统计折线图
     *
     * @param request 前端请求
     * @return 折线图
     */
    @PostMapping("/access/line")
    public List<ProcessSegmentationStatistics> getAccessStatisticsLine(
        @RequestBody @Valid ProcessDataTrendRequest request) {
        if (request.getType().equals(DAILY_STATISTICS) || request.getType().equals(ON_TIME_STATISTICS)) {
            return processService.getProSegStatisticsLine(request);
        } else {
            throw new BizException("查询时间参数错误!");
        }
    }


    /**
     * 接入统计-查询数据接入详情列表 <a href="http://192.168.210.40:3001/project/4220/interface/api/163335">...</a>
     *
     * @param request 请求
     * @return 列表返回值
     * @throws BizException 业务异常类
     * <AUTHOR>
     * @since 2024/8/27 17:02
     */
    @PostMapping("/access/list")
    public PageResponse<AccessTaskUnit> getAccessDetailList(@RequestBody @Valid StatisticRequest request)
        throws BizException {
        return dataStatisticsService.getAccessDetailList(request);
    }


    /**
     * 任务统计列表
     *
     * @param filterRequest 请求参数
     * @return 分页信息
     */
    @PostMapping("/task/list")
    public PageResponse<TaskStatisticsListResponse> taskList(@RequestBody StatisticRequest filterRequest)
        throws BizException {
        return dataStatisticsService.taskList(filterRequest);
    }

}
