package com.trs.ai.moye.monitor.controller;

import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.monitor.entity.BasicComponentDetection;
import com.trs.ai.moye.monitor.enums.DetectionType;
import com.trs.ai.moye.monitor.request.BasicComponentAutoTestRequest;
import com.trs.ai.moye.monitor.request.BasicComponentDetailRequest;
import com.trs.ai.moye.monitor.request.BatchDetectionRequest;
import com.trs.ai.moye.monitor.response.BasicComponentDetectionStatisticsResponse;
import com.trs.ai.moye.monitor.response.BatchDetectionResponse;
import com.trs.ai.moye.monitor.service.BasicComponentService;
import com.trs.ai.moye.permission.service.CurrentUserService;
import com.trs.moye.base.common.response.PageResponse;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 基础组件监控controller
 *
 * <AUTHOR>
 * @since 2025/6/10
 */
@RestController
@RequestMapping("/monitor/basic-component")
public class BasicComponentController {

    @Resource
    private BasicComponentService basicComponentService;
    @Resource
    private CurrentUserService currentUserService;

    /**
     * 获取统计页面
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/168837">YApi</a>
     *
     * @return 统计页面
     */
    @GetMapping("/statistics-list")
    public List<BasicComponentDetectionStatisticsResponse> statisticsList() {
        return basicComponentService.statisticsList();
    }


    /**
     * 开关自动测试
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/168838">YApi</a>
     *
     * @param request 请求参数
     */
    @PostMapping("/auto-test")
    public void switchAutoTest(@RequestBody @Validated BasicComponentAutoTestRequest request) {
        basicComponentService.switchEnabled(request.getComponentName(), request.getAutoTestEnabled());
    }


    /**
     * 获取详情列表
     *
     * @param request 请求参数
     * @return 详情列表
     */
    @PostMapping("/detail-list")
    public PageResponse<BasicComponentDetection> detailList(@RequestBody BasicComponentDetailRequest request) {
        return basicComponentService.detailList(request);
    }

    /**
     * 单个检测
     *
     * @param componentName 组件名称
     */
    @GetMapping("/component-detection")
    public void componentDetection(@RequestParam String componentName) {
        User user = currentUserService.getUser();
        String userName = "-";
        if (Objects.nonNull(user)) {
            userName = user.getName();
        }
        basicComponentService.componentDetection(componentName, DetectionType.MANUAL, userName);
    }

    /**
     * 批量检测
     *
     * @param request 请求参数
     * @return 批量检测结果
     */
    @PostMapping("/batch-detection")
    public BatchDetectionResponse batchDetection(@RequestBody BatchDetectionRequest request) {
        User user = currentUserService.getUser();
        String userName = "-";
        if (Objects.nonNull(user)) {
            userName = user.getName();
        }
        return basicComponentService.batchDetection(request.getComponentNames(), DetectionType.MANUAL, userName);
    }

}
