package com.trs.ai.moye.homepage.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2025/4/14
 **/
@Getter
public enum StatType {
    ODS_STORAGE(true),
    TASK(true),
    LAG(true),
    FLUCTUATION(true),
    <PERSON>UTO<PERSON>(true),
    DWD_STORAGE(true),
    THEME_STORAGE(true),
    SUBJECT_STORAGE(true),
    OPERATOR_INVOKE(true),
    DATA_SERVICE_INVOKE(true);

    private final boolean needUpdate;

    StatType(boolean needUpdate) {
        this.needUpdate = needUpdate;
    }
}
