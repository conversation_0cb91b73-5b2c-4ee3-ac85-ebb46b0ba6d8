package com.trs.ai.moye.monitor.controller;

import com.trs.ai.moye.monitor.entity.BatchTaskListAllResponse;
import com.trs.ai.moye.monitor.entity.BatchTaskListRequest;
import com.trs.ai.moye.monitor.request.DataModelMonitorRequest;
import com.trs.ai.moye.monitor.request.HomePageDwdTrendRequest;
import com.trs.ai.moye.monitor.request.OdsMonitorTrendRequest;
import com.trs.ai.moye.monitor.response.HomePageResponse;
import com.trs.ai.moye.monitor.response.MonitorDetailTableResponse;
import com.trs.ai.moye.monitor.response.MonitorTrendResponse;
import com.trs.ai.moye.monitor.response.OdsMonitorConfigVersionResponse;
import com.trs.ai.moye.monitor.response.OdsMonitorStatisticsResponse;
import com.trs.ai.moye.monitor.response.SourceTypeResponse;
import com.trs.ai.moye.monitor.response.statistics.BatchTaskExecuteResponse;
import com.trs.ai.moye.monitor.response.statistics.StreamTaskBarChart;
import com.trs.ai.moye.monitor.response.statistics.StreamTaskHandleResponse;
import com.trs.ai.moye.monitor.service.DwdMonitorService;
import com.trs.ai.moye.monitor.service.OdsMonitorService;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.monitor.enums.MonitorConfigType;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025/3/3
 **/

@RestController
@RequestMapping("/monitor/dwd")
public class DwdMonitorController {

    @Resource
    private OdsMonitorService odsMonitorService;

    @Resource
    private DwdMonitorService dwdMonitorService;

    /**
     * Ods监控首页 <br>
     * <a href=http://192.168.210.40:3001/project/4220/interface/api/167533>YApi</a>
     *
     * @param request 请求参数
     * @return 首页数据
     */
    @PostMapping("/home-page")
    public HomePageResponse dwdMonitorHomePage(
        @RequestBody DataModelMonitorRequest request) {
        MonitorConfigType type = request.getMonitorType();
        return odsMonitorService.odsMonitorHomePage(type, request, ModelLayer.DWD);
    }

    /**
     * 贴源库监控统计列表
     * <p>
     * <a href=http://192.168.210.40:3001/project/4220/interface/api/163360>YApi</a>
     *
     * @param request 请求参数
     * @return 首页数据
     */
    @PostMapping("/list")
    public PageResponse<OdsMonitorStatisticsResponse> odsMonitorStatisticsList(
        @RequestBody @Validated DataModelMonitorRequest request) {
        return odsMonitorService.odsMonitorStatisticsList(request.getMonitorType(), request, ModelLayer.DWD);
    }


    /**
     * 贴源库监控详情列表
     * <p>
     * <a href=http://192.168.210.40:3001/project/4220/interface/api/163400>YApi</a>
     *
     * @param dataModelId 数据建模id
     * @param request     请求参数
     * @return 首页数据
     */
    @PostMapping("/detail/table/{dataModelId}")
    public PageResponse<MonitorDetailTableResponse> odsMonitorDetailTable(
        @PathVariable Integer dataModelId,
        @RequestBody DataModelMonitorRequest request) {
        return odsMonitorService.odsMonitorDetailTable(request.getMonitorType(), dataModelId, request);
    }

    /**
     * 获取贴源库监控配置版本信息
     * <p>
     * <a href=http://192.168.210.40:3001/project/4220/interface/api/163415>YApi</a>
     *
     * @param monitorType 监控类型
     * @param dataModelId 元数据id
     * @return 版本信息
     */
    @GetMapping("/{monitorType}/detail/versions/{dataModelId}")
    public List<OdsMonitorConfigVersionResponse> odsMonitorDetailVersions(@PathVariable String monitorType,
        @PathVariable Integer dataModelId) throws BizException {
        return odsMonitorService.odsMonitorDetailVersions(getMonitorType(monitorType), dataModelId);
    }


    private MonitorConfigType getMonitorType(String monitorType) throws BizException {
        return MonitorConfigType.valueOf(monitorType.toUpperCase());
    }

    /**
     * 贴源库 详情趋势
     * <br>
     * <a href=http://192.168.210.40:3001/project/4220/interface/api/163410>YApi</a>
     *
     * @param monitorType 监控类型
     * @param dataModelId 元数据id
     * @param request     其他筛选条件
     * @return 趋势
     */
    @PostMapping("/{monitorType}/detail/trend/{dataModelId}")
    public List<MonitorTrendResponse> odsMonitorTrend(@PathVariable("monitorType") String monitorType,
        @PathVariable("dataModelId") Integer dataModelId, @RequestBody @Valid OdsMonitorTrendRequest request) {
        MonitorConfigType typeEnum = getMonitorType(monitorType);
        if (Objects.isNull(typeEnum)) {
            throw new BizException("监控类型【%s】是非法值", monitorType);
        }
        if (MonitorConfigType.LAG.equals(typeEnum) || MonitorConfigType.FLUCTUATION.equals(typeEnum)
            || MonitorConfigType.TASK_EXECUTION_TIME.equals(typeEnum)) {
            return odsMonitorService.odsMonitorDetailTrend(typeEnum, dataModelId,
                request.getConfigId(), request.getCount());
        }
        throw new BizException("监控类型【%s】不提供趋势图", monitorType);
    }


    /**
     * 获取数据源类型
     *
     * @return 数据源类型
     */
    @GetMapping("/source-type")
    public List<SourceTypeResponse> getSourceType() {
        return Arrays.stream(DataSourceCategory.values()).map(SourceTypeResponse::new).toList();
    }

    /**
     * 获取数据源子类型
     *
     * @param sourceType 数据源类型
     * @return 数据源子类型
     */
    @GetMapping("/source-sub-type/{sourceType}")
    public List<SourceTypeResponse> getSourceSubType(@PathVariable String sourceType) {
        List<ConnectionType> enums = Arrays.stream(ConnectionType.values())
            .filter(connectionType -> connectionType.getCategory().name().equals(sourceType.toUpperCase())).toList();
        return enums.stream().map(SourceTypeResponse::new).toList();
    }

    /**
     * 获取要素库监执行情况
     *
     * @param request 请求参数
     * @return 要素库监执行情况
     */
    @PostMapping("/batch-task-execute")
    public BatchTaskExecuteResponse getBatchTaskExecuteResponse(@RequestBody HomePageDwdTrendRequest request) {
        return dwdMonitorService.getBatchTaskExecuteResponse(request);
    }

    /**
     * 获取批处理任务列表
     *
     * @param request 请求参数
     * @return 批处理任务列表
     */
    @PostMapping("/batch-task-list")
    public BatchTaskListAllResponse getBatchTaskListResponse(@RequestBody BatchTaskListRequest request) {
        return dwdMonitorService.getBatchTaskListResponse(request);
    }

    /**
     * 获取流处理处理量
     *
     * @param request 请求参数
     * @return 流处理处理量统计
     */
    @PostMapping("/stream-task-handle")
    public StreamTaskHandleResponse getStreamTaskHandleResponse(@RequestBody HomePageDwdTrendRequest request) {
        return dwdMonitorService.getStreamTaskHandleResponse(request);
    }

    /**
     * 获取流处理任务柱状图
     *
     * @param request 请求参数
     * @return 流处理任务柱状图
     */
    @PostMapping("/stream-task-bar-chart")
    public List<StreamTaskBarChart> getStreamTaskBarChart(@RequestBody HomePageDwdTrendRequest request) {
        return dwdMonitorService.getStreamTaskBarChart(request);
    }

    /**
     * 获取存储处理任务折线图
     *
     * @param request 请求参数
     * @return 流处理任务折线图
     */
    @PostMapping("/storage-line")
    public StreamTaskHandleResponse getStorageLine(@RequestBody HomePageDwdTrendRequest request) {
        return dwdMonitorService.getStorageLine(request);
    }

    /**
     * 获取算子执行总数
     *
     * @return 算子执行总数
     */
    @GetMapping("/operator-execute-count")
    public Long operatorExecuteCount() {
        return dwdMonitorService.operatorExecuteCount();
    }
}
