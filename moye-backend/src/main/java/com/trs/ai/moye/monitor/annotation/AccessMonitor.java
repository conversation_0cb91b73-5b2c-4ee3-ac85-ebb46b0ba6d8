package com.trs.ai.moye.monitor.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 接入监控注解
 * 用于标记需要监控的数据接入方法
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AccessMonitor {

    /**
     * 监控描述
     *
     * @return {@link String }
     */
    String description() default "";

    /**
     * 是否记录参数
     *
     * @return boolean
     */
    boolean recordParams() default true;

    /**
     * 是否记录返回值
     *
     * @return boolean
     */
    boolean recordResult() default true;

    /**
     * 是否记录异常
     *
     * @return boolean
     */
    boolean recordException() default true;
}
