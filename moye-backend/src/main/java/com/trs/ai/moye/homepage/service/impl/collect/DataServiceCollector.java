package com.trs.ai.moye.homepage.service.impl.collect;

import com.trs.ai.moye.data.service.dao.DataServiceLogMapper;
import com.trs.ai.moye.data.service.dao.DataServiceMapper;
import com.trs.ai.moye.homepage.dao.StorageStatsMapper;
import com.trs.ai.moye.homepage.entity.AggregatedStat;
import com.trs.ai.moye.homepage.entity.BaseStat;
import com.trs.ai.moye.homepage.entity.DashBoardData;
import com.trs.ai.moye.homepage.enums.HomePageShowData;
import com.trs.ai.moye.homepage.enums.StatType;
import com.trs.ai.moye.homepage.service.DataCollectService;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/1/23
 **/
@Service
public class DataServiceCollector implements DataCollectService {

    @Resource
    private DataServiceMapper dataServiceMapper;


    @Resource
    private DataServiceLogMapper dataServiceLogMapper;
    @Resource
    private StorageStatsMapper storageStatsMapper;


    @Override
    public List<DashBoardData> collectData() {
        Long count = dataServiceMapper.selectCount(null);
        BaseStat baseStat = storageStatsMapper.selectStatsByType(StatType.DATA_SERVICE_INVOKE);
        long total = 0;
        if (Objects.nonNull(baseStat)) {
            total = baseStat.getTotal();
        }
        return DashBoardData.buildData(HomePageShowData.DATA_SERVICE, count, total);
    }

    @Override
    public AggregatedStat getIncrementStat(LocalDateTime startTime, LocalDateTime endTime) {
        Long increment = dataServiceLogMapper.getIncrement(startTime, endTime);
        AggregatedStat aggregatedStat = new AggregatedStat();
        aggregatedStat.setStatType(StatType.DATA_SERVICE_INVOKE)
            .setLastStatTime(endTime)
            .setTotal(increment);
        return aggregatedStat;
    }
}
