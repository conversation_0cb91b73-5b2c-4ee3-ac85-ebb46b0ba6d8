package com.trs.ai.moye.homepage.service.impl;

import com.trs.ai.moye.homepage.dao.DashBoardMapper;
import com.trs.ai.moye.homepage.entity.DashBoardData;
import com.trs.ai.moye.homepage.service.DataCollectService;
import com.trs.ai.moye.homepage.service.impl.collect.AccessDurableService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/1/23
 **/
@Slf4j
@Component
public class CollectorSchedule {

    @Resource
    private List<DataCollectService> dataCollectServices;

    @Resource
    private DashBoardMapper dashBoardMapper;

    @Resource
    private AccessDurableService accessDurableService;

    /**
     * 定时任务，启动后固定间隔半个小时执行一次
     */
    @Scheduled(fixedRate = 30 * 60 * 1000)
    public void collectData() {
        // 收集为List<DashBoard>
        List<DashBoardData> insertData = dataCollectServices.stream().map(DataCollectService::collectData)
            .flatMap(List::stream).toList();
        // 插入数据
        dashBoardMapper.insertData(insertData);
    }

    /**
     * 定时任务，启动后固定间隔1小时执行一次
     */
    @Scheduled(fixedRate = 60 * 60 * 1000)
    public void collectAccessStats() {
        try {
            accessDurableService.durableAccessStat();
        } catch (Exception e) {
            log.error("定时任务收集接入统计数据失败", e);
        }
    }

}
