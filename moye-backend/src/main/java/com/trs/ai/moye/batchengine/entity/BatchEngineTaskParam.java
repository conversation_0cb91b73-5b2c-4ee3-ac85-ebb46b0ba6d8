package com.trs.ai.moye.batchengine.entity;

import com.trs.ai.moye.backstage.dao.AuthCertificateKerberosMapper;
import com.trs.ai.moye.backstage.entity.AuthCertificateKerberos;
import com.trs.ai.moye.common.utils.BeanUtil;
import com.trs.ai.moye.data.model.enums.CodeType;
import com.trs.moye.ability.entity.operator.BatchOperator;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.entity.BatchProcessSparkConfig;
import com.trs.moye.base.data.model.entity.CodeParameterItem;
import com.trs.moye.base.data.model.entity.SparkConfigItem;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;


/**
 * batch engine 批处理任务 数据传输类
 * <br>
 * 批处理的 立即执行 和 定时调度 使用的数据对象
 *
 * <AUTHOR>
 */
@Data
public class BatchEngineTaskParam {

    /**
     * 主任务id
     */
    private final String taskId;

    /**
     * 分层
     */
    private final ModelLayer layer;

    /**
     * 用于 spark 应用名称
     */
    private final String taskName;

    /**
     * 代码
     */
    private String code;

    /**
     * 算子 DAG测试用
     */
    private List<BatchOperator> operators;

    /**
     * kerberos认证 用户名
     */
    private String principal;

    /**
     * kerberos认证 keytab文件
     */
    private String keytabPath;

    /**
     * 高级参数
     */
    private Map<String, String> configParameters;
    /**
     * 自定义 代码可替换参数
     */
    private Map<String, String> customCodeParameters = new HashMap<>();
    /**
     * 子任务名称
     */
    private String subTaskName;
    /**
     * 代码类型
     */
    private CodeType codeType;
    /**
     * 启动任务用户id
     */
    private Integer userId;

    /**
     * 执行id
     */
    private String executeId;

    /**
     * 遇到异常, 是否继续子任务
     * true: 继续执行后续子任务
     * false: 停止执行后续子任务
     */
    private Boolean continueOnException = false;

    private LocalDateTime beginTime;

    private LocalDateTime endTime;


    /**
     * 设置spark参数
     *
     * @param config {@link BatchProcessSparkConfig}
     */
    public void setSparkConfig(BatchProcessSparkConfig config) {
        if (config == null) {
            return;
        }
        setKerberosConfig(config.getCertificateId());
        setSparkConfigParameters(config.getSparkConfigItemList());
        this.customCodeParameters = CodeParameterItem.convertToMap(config.getCustomCodeParameters());
        this.continueOnException = config.getContinueOnException();
    }

    private void setKerberosConfig(Integer certificateId) {
        if (certificateId == null) {
            return;
        }
        AuthCertificateKerberosMapper kerberosMapper = BeanUtil.getBean(AuthCertificateKerberosMapper.class);
        AuthCertificateKerberos authCertificateKerberos = kerberosMapper.selectById(certificateId);
        this.setKeytabPath(authCertificateKerberos.getKeytabPath());
        this.setPrincipal(authCertificateKerberos.getPrincipal());
    }

    private void setSparkConfigParameters(List<SparkConfigItem> sparkConfigItemList) {
        if (sparkConfigItemList == null || sparkConfigItemList.isEmpty()) {
            return;
        }
        Map<String, String> sparkConfig = new HashMap<>();
        for (SparkConfigItem item : sparkConfigItemList) {
            String key = item.getKey();
            String value = item.getValue();
            if (!key.isEmpty() && !value.isEmpty()) {
                sparkConfig.put(key, value);
            }
        }
        this.setConfigParameters(sparkConfig);
    }
}
