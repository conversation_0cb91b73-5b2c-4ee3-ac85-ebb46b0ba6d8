package com.trs.ai.moye.homepage.entity;

import java.time.LocalDateTime;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 基础统计结果实体
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseStat {

    private Long total = 0L;
    private Long normal = 0L;
    private Long abnormal = 0L;
    private LocalDateTime lastStatTime;

    /**
     * 相加
     *
     * @param parma 加数
     * @return 和
     */
    public BaseStat add(BaseStat parma) {
        if (Objects.isNull(parma)) {
            parma = new BaseStat();
        }
        BaseStat result = new BaseStat();
        result.setTotal(this.getTotal() + parma.getTotal());
        result.setNormal(this.getNormal() + parma.getNormal());
        result.setAbnormal(this.getAbnormal() + parma.getAbnormal());
        return result;
    }
}

