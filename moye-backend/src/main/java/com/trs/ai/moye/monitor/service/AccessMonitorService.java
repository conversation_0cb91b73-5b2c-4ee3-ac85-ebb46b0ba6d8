package com.trs.ai.moye.monitor.service;

import com.trs.ai.moye.monitor.dto.AccessMonitorDataDTO;
import com.trs.ai.moye.monitor.dto.AccessMonitorStatusDTO;
import com.trs.ai.moye.monitor.dto.AccessTrendDataDTO;
import com.trs.ai.moye.monitor.dto.TimeRangeParamsDTO;
import com.trs.ai.moye.monitor.factory.MonitorAdapterFactory;
import com.trs.ai.moye.monitor.adapter.DataSourceMonitorAdapter;
import com.trs.ai.moye.storageengine.service.StorageEngineService;
import com.trs.ai.moye.storageengine.feign.MonitorFeign; // 🔧 新增：导入监控Feign客户端
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataSourceConfig;
import com.trs.moye.base.monitor.dao.DataModelMonitorConfigMapper; // 🔧 新增：导入监控配置Mapper
import com.trs.moye.base.monitor.entity.DataModelMonitorConfig; // 🔧 新增：导入监控配置实体
import com.trs.moye.base.monitor.enums.MonitorConfigType; // 🔧 新增：导入监控配置类型枚举
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ArrayList;
import java.util.Random;
import java.util.Map;
import java.util.HashMap;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.trs.ai.moye.storageengine.dto.MonitorDTO; // 🔧 修正：使用backend项目中的MonitorDTO

/**
 * 数据接入监控服务
 *
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@Slf4j
@Service
public class AccessMonitorService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private MonitorAdapterFactory adapterFactory;

    @Resource
    private StorageEngineService storageEngineService;

    @Resource
    private ScheduleBasedMetricsService scheduleBasedMetricsService;

    @Resource
    private MonitorFeign monitorFeign; // 🔧 新增：注入监控Feign客户端

    @Resource
    private DataModelMonitorConfigMapper dataModelMonitorConfigMapper; // 🔧 新增：注入监控配置Mapper

    // ISO格式时间格式化器 - 使用系统默认时区，确保与前端时区一致
    private static final DateTimeFormatter ISO_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取数据接入监控状态信息
     *
     *
     * @param dataModelId 数据模型ID
     * @return 监控状态信息
     */
    public AccessMonitorStatusDTO getAccessMonitorStatus(Integer dataModelId) {
        log.info("获取数据接入监控状态, dataModelId: {}", dataModelId);
        
        try {
            DataModel dataModel = getDataModelById(dataModelId);
            
            // 检查数据模型是否存在数据源配置
            boolean hasDataSource = dataModel.getDataSource() != null && !dataModel.getDataSource().isEmpty();
            
            return AccessMonitorStatusDTO.builder()
                .accessProcess(AccessMonitorStatusDTO.AccessProcess.builder()
                    .enable(hasDataSource)
                    .status(hasDataSource ? "running" : "stopped")
                    .lastUpdateTime(LocalDateTime.now().format(ISO_FORMATTER))
                    .build())
                .permissions(AccessMonitorStatusDTO.Permissions.builder()
                    .canView(true)
                    .canEdit(true)
                    .canConfigure(true)
                    .build())
                .build();
                
        } catch (Exception e) {
            log.error("获取数据接入监控状态失败, dataModelId: {}", dataModelId, e);
            return getDefaultMonitorStatus();
        }
    }

    /**
     * 获取数据接入监控数据
     *
     *
     * @param dataModelId 数据模型ID
     * @return 监控数据
     */
    public AccessMonitorDataDTO getAccessMonitorData(Integer dataModelId) {
        log.info("开始获取数据接入监控数据, dataModelId: {}", dataModelId);
        
        try {
            DataModel dataModel = getDataModelById(dataModelId);
            log.info("数据模型获取成功: dataModelId={}, dataModelName={}", dataModelId, dataModel.getZhName());
            
            // 获取数据模型的第一个数据源配置
            if (dataModel.getDataSource().isEmpty()) {
                log.warn("数据模型 {} 没有配置数据源", dataModelId);
                return getDefaultMonitorData();
            }
            
            DataSourceConfig firstDataSource = dataModel.getDataSource().get(0);
            var connectionType = firstDataSource.getConnection().getConnectionType();
            log.info("数据源配置获取成功: dataModelId={}, connectionType={}, connectionTypeLabel={}", 
                dataModelId, connectionType, connectionType.getLabel());
            
            // 根据连接类型获取对应的监控适配器
            log.info("开始获取监控适配器, dataModelId={}, connectionType={}", dataModelId, connectionType);
            DataSourceMonitorAdapter adapter = adapterFactory.getAdapterOrThrow(connectionType);
            log.info("监控适配器获取成功: dataModelId={}, adapterClass={}", dataModelId, adapter.getClass().getSimpleName());
            
            // 收集监控指标（传入storageEngineService用于连接测试）
            log.info("开始收集接入监控指标, dataModelId={}", dataModelId);
            var accessMetrics = adapter.collectAccessMetrics(dataModel);
            log.info("接入监控指标收集完成: dataModelId={}, todayAccessCount={}, totalCount={}", 
                dataModelId, accessMetrics.getTodayAccessCount(), accessMetrics.getAccessTotalCount());
            
            // 🔧 新增：集成积压监控
            log.info("开始收集积压监控指标, dataModelId={}", dataModelId);
            var lagMetrics = collectLagMetrics(dataModel, connectionType);
            log.info("积压监控指标收集完成: dataModelId={}, sourceLagCount={}, hasLag={}", 
                dataModelId, lagMetrics.getSourceLagCount(), lagMetrics.getHasLag());
            
            log.info("开始收集连接监控指标, dataModelId={}", dataModelId);
            var connectionMetrics = adapter.collectConnectionMetrics(dataModel, storageEngineService);
            log.info("连接监控指标收集完成: dataModelId={}, isConnected={}", dataModelId, connectionMetrics.getIsConnected());
            
            log.info("开始获取数据源信息, dataModelId={}", dataModelId);
            var sourceInfoList = adapter.getSourceInfo(dataModel);
            log.info("数据源信息获取完成: dataModelId={}, sourceCount={}", dataModelId, sourceInfoList.size());
            
            LocalDateTime now = LocalDateTime.now();
            
            AccessMonitorDataDTO result = AccessMonitorDataDTO.builder()
                .monitorTime(now.format(ISO_FORMATTER))
                .lastMonitorTime(now.minusHours(1).format(ISO_FORMATTER)) // 假设上次监控时间为1小时前
                .periodDuration("1小时")
                .accessMonitor(buildAccessMetricsWithLag(accessMetrics, lagMetrics)) // 🔧 修改：集成积压指标
                .connectionMonitor(convertConnectionMetrics(connectionMetrics))
                .sourceInfoList(convertSourceInfoList(sourceInfoList))
                .remark(String.format("数据源类型: %s", connectionType.getLabel()))
                .build();
            
            log.info("数据接入监控数据获取完成: dataModelId={}, dataModelName={}, todayAccessCount={}, totalCount={}, isConnected={}, sourceCount={}, sourceLagCount={}, hasLag={}", 
                dataModelId, dataModel.getZhName(), 
                accessMetrics.getTodayAccessCount(), accessMetrics.getAccessTotalCount(),
                connectionMetrics.getIsConnected(), sourceInfoList.size(),
                lagMetrics.getSourceLagCount(), lagMetrics.getHasLag());
                
            return result;
                
        } catch (Exception e) {
            log.error("获取数据接入监控数据失败, dataModelId: {}, 错误信息: {}", dataModelId, e.getMessage(), e);
            return getDefaultMonitorData();
        }
    }

    /**
     * 获取数据源信息列表
     *
     *
     * @param dataModelId 数据模型ID
     * @return 数据源信息列表
     */
    public List<AccessMonitorDataDTO.SourceInfo> getAccessSourceInfo(Integer dataModelId) {
        log.info("获取数据源信息, dataModelId: {}", dataModelId);
        
        try {
            DataModel dataModel = getDataModelById(dataModelId);
            
            if (dataModel.getDataSource().isEmpty()) {
                return List.of();
            }
            
            DataSourceConfig firstDataSource = dataModel.getDataSource().get(0);
            var connectionType = firstDataSource.getConnection().getConnectionType();
            DataSourceMonitorAdapter adapter = adapterFactory.getAdapterOrThrow(connectionType);
            
            var sourceInfoList = adapter.getSourceInfo(dataModel);
            return convertSourceInfoList(sourceInfoList);
            
        } catch (Exception e) {
            log.error("获取数据源信息失败, dataModelId: {}", dataModelId, e);
            return List.of();
        }
    }

    @Resource
    private AccessTrendDataCollectionService trendDataCollectionService;

    /**
     * 获取接入趋势数据
     *
     * @param dataModelId 数据模型ID
     * @param timeParams 时间范围参数
     * @return 趋势数据
     */
    public AccessTrendDataDTO getAccessTrendData(Integer dataModelId, TimeRangeParamsDTO timeParams) {
        log.info("获取接入趋势数据, dataModelId: {}, timeParams: {}", dataModelId, timeParams);
        
        try {
            // 🔧 改进：使用真实的历史趋势数据而不是模拟数据
            LocalDateTime startTime = getStartTimeByTimeRange(timeParams);
            LocalDateTime endTime = getEndTimeByTimeRange(timeParams);
            
            // 🔧 新增：详细记录时间范围计算过程
            log.info("🔍 时间范围计算详情: dataModelId={}, timeParamsType={}, 计算开始时间={}, 计算结束时间={}", 
                dataModelId, timeParams.getType(), startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), 
                endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            // 🔧 新增：验证"今日"范围的时间设置
            if ("LAST_TODAY".equals(timeParams.getType())) {
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime expectedStart = now.toLocalDate().atStartOfDay();
                log.info("🕐 今日范围验证: 期望开始时间={}, 实际开始时间={}, 是否匹配={}", 
                    expectedStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                    expectedStart.equals(startTime));
            }
            
            log.info("查询趋势数据时间范围: dataModelId={}, startTime={}, endTime={}", 
                dataModelId, startTime, endTime);
            
            // 从Redis时间序列获取真实趋势数据
            List<AccessTrendDataDTO.ChartPoint> accessSpeedTrend = getRealAccessSpeedTrend(dataModelId, startTime, endTime);
            List<AccessTrendDataDTO.ChartPoint> accessCountTrend = getRealAccessCountTrend(dataModelId, startTime, endTime);
            
            // 🔧 新增：获取积压量趋势数据
            List<AccessTrendDataDTO.ChartPoint> lagCountTrend = getRealLagCountTrend(dataModelId, startTime, endTime);
            
            // 如果没有真实数据，则返回空数据，不再生成模拟数据
            if (accessSpeedTrend.isEmpty()) {
                log.info("未找到真实趋势数据，返回空数据, dataModelId: {}", dataModelId);
                // 不生成模拟数据，直接使用空列表
            }
            
            // 🔧 新增：记录返回数据的统计信息
            log.info("📊 趋势数据获取完成: dataModelId={}, 接入速度趋势点数={}, 接入数量趋势点数={}, 积压量趋势点数={}", 
                dataModelId, accessSpeedTrend.size(), accessCountTrend.size(), lagCountTrend.size());
            
            return AccessTrendDataDTO.builder()
                .accessSpeedTrend(accessSpeedTrend)
                .accessCountTrend(accessCountTrend)
                .lagCountTrend(lagCountTrend)
                .build();
                
        } catch (Exception e) {
            log.error("获取接入趋势数据失败, dataModelId: {}", dataModelId, e);
            return getDefaultTrendData();
        }
    }

    /**
     * 获取不同存储点的今日存储统计信息
     *
     * @param dataModelId 数据模型ID
     * @return 不同存储点的今日存储统计信息
     */
    public Map<String, Object> getStorageStats(Integer dataModelId) {
        log.info("获取不同存储点的今日存储统计信息, dataModelId: {}", dataModelId);
        
        try {
            // 获取今日接入总量和异常总数（基于读取成功量）
            log.info("开始获取接入指标, dataModelId: {}", dataModelId);
            var accessMetrics = scheduleBasedMetricsService.calculateAccessMetrics(dataModelId);
            Long todayTotalAccess = accessMetrics.getTodayAccessCount() != null
                    ?
                accessMetrics.getTodayAccessCount().longValue() : 0L;
            Long todayTotalError = accessMetrics.getTodayErrorCount() != null
                    ?
                accessMetrics.getTodayErrorCount().longValue() : 0L;
            
            log.info("接入指标获取完成: dataModelId={}, todayTotalAccess={}, todayTotalError={}", 
                dataModelId, todayTotalAccess, todayTotalError);
            
            // 获取不同存储点的统计信息
            log.info("开始获取存储点统计信息, dataModelId: {}", dataModelId);
            Map<String, Object> storageStats = scheduleBasedMetricsService.getStorageStatsByStoragePoints(dataModelId);
            
            log.info("存储点统计信息获取完成: dataModelId={}, storageStats={}", dataModelId, storageStats);
            
            // 验证数据一致性：存储点今日存储量总和应该等于今日接入总量
            Long storageSum = (Long) storageStats.getOrDefault("todayStorageSum", 0L);
            Long errorSum = (Long) storageStats.getOrDefault("todayErrorSum", 0L);
            
            log.info("存储统计验证: dataModelId={}, todayTotalAccess={}, storageSum={}, todayTotalError={}, errorSum={}", 
                dataModelId, todayTotalAccess, storageSum, todayTotalError, errorSum);
            
            // 如果数据不一致，记录警告并调整数据
            if (!todayTotalAccess.equals(storageSum)) {
                log.warn("数据不一致警告: 今日接入总量({}) != 存储点存储量总和({}), dataModelId: {}", 
                    todayTotalAccess, storageSum, dataModelId);
                // 调整存储点数据，确保总和一致
                log.info("开始调整存储统计数据以确保一致性, dataModelId: {}", dataModelId);
                storageStats = adjustStorageStatsForConsistency(storageStats, todayTotalAccess, todayTotalError);
                log.info("存储统计数据调整完成, dataModelId: {}", dataModelId);
            }
            
            log.info("存储统计信息返回: dataModelId={}, finalStorageSum={}, finalErrorSum={}", 
                dataModelId, storageStats.get("todayStorageSum"), storageStats.get("todayErrorSum"));
            
            return storageStats;
            
        } catch (Exception e) {
            log.error("获取不同存储点的今日存储统计信息失败, dataModelId: {}", dataModelId, e);
            return getDefaultStorageStats();
        }
    }

    /**
     * 获取指定存储点的今日存储统计信息
     *
     * @param dataModelId 数据模型ID
     * @param storageId 存储点ID
     * @return 指定存储点的今日存储统计信息
     */
    public Map<String, Object> getStorageStatsByStorageId(Integer dataModelId, Integer storageId) {
        log.info("获取指定存储点的今日存储统计信息, dataModelId: {}, storageId: {}", dataModelId, storageId);
        
        try {
            return scheduleBasedMetricsService.getStorageStatsByStorageId(dataModelId, storageId);
            
        } catch (Exception e) {
            log.error("获取指定存储点的今日存储统计信息失败, dataModelId: {}, storageId: {}", dataModelId, storageId, e);
            return getDefaultStorageStatsByStorageId(storageId);
        }
    }

    /**
     * 调整存储统计数据以确保与接入总量一致
     *
     * @param storageStats 原始存储统计数据
     * @param todayTotalAccess 今日接入总量
     * @param todayTotalError 今日异常总数
     * @return 调整后的存储统计数据
     */
    private Map<String, Object> adjustStorageStatsForConsistency(Map<String, Object> storageStats, 
                                                                Long todayTotalAccess, Long todayTotalError) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> storagePoints = (List<Map<String, Object>>) storageStats.get("storagePoints");
            
            if (storagePoints == null || storagePoints.isEmpty()) {
                return storageStats;
            }
            
            // 计算当前存储点数据总和
            Long currentStorageSum = storagePoints.stream()
                .mapToLong(point -> ((Number) point.getOrDefault("todayStorage", 0L)).longValue())
                .sum();
            
            Long currentErrorSum = storagePoints.stream()
                .mapToLong(point -> ((Number) point.getOrDefault("todayErrorCount", 0L)).longValue())
                .sum();
            
            // 如果当前总和为0，按比例分配
            if (currentStorageSum == 0) {
                int storageCount = storagePoints.size();
                long avgStorage = todayTotalAccess / storageCount;
                long remainder = todayTotalAccess % storageCount;
                
                for (int i = 0; i < storagePoints.size(); i++) {
                    Map<String, Object> point = storagePoints.get(i);
                    long adjustedStorage = avgStorage + (i < remainder ? 1 : 0);
                    point.put("todayStorage", adjustedStorage);
                }
            } else {
                // 按比例调整每个存储点的数据
                double storageRatio = (double) todayTotalAccess / currentStorageSum;
                double errorRatio = (double) todayTotalError / Math.max(currentErrorSum, 1);
                
                for (Map<String, Object> point : storagePoints) {
                    long originalStorage = ((Number) point.getOrDefault("todayStorage", 0L)).longValue();
                    long originalError = ((Number) point.getOrDefault("todayErrorCount", 0L)).longValue();
                    
                    point.put("todayStorage", Math.round(originalStorage * storageRatio));
                    point.put("todayErrorCount", Math.round(originalError * errorRatio));
                }
            }
            
            // 更新总和
            storageStats.put("todayStorageSum", todayTotalAccess);
            storageStats.put("todayErrorSum", todayTotalError);
            
            log.info("存储统计数据调整完成: 调整后存储量总和={}, 异常数总和={}", 
                storageStats.get("todayStorageSum"), storageStats.get("todayErrorSum"));
            
            return storageStats;
            
        } catch (Exception e) {
            log.error("调整存储统计数据失败", e);
            return storageStats;
        }
    }

    /**
     * 获取默认存储统计信息
     *
     * @return 默认存储统计信息
     */
    private Map<String, Object> getDefaultStorageStats() {
        Map<String, Object> defaultStats = new HashMap<>();
        defaultStats.put("todayStorageSum", 0L);
        defaultStats.put("todayErrorSum", 0L);
        defaultStats.put("storagePoints", List.of());
        defaultStats.put("remark", "获取存储统计信息失败");
        return defaultStats;
    }

    /**
     * 获取指定存储点的默认统计信息
     *
     * @param storageId 存储点ID
     * @return 默认统计信息
     */
    private Map<String, Object> getDefaultStorageStatsByStorageId(Integer storageId) {
        Map<String, Object> defaultStats = new HashMap<>();
        defaultStats.put("storageId", storageId);
        defaultStats.put("todayStorage", 0L);
        defaultStats.put("totalStorage", 0L);
        defaultStats.put("todayErrorCount", 0L);
        defaultStats.put("remark", "获取存储统计信息失败");
        return defaultStats;
    }

    // ==================== 真实趋势数据获取方法 ====================

    /**
     * 获取真实的接入速度趋势数据
     *
     * @param dataModelId 数据模型ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link List }<{@link AccessTrendDataDTO.ChartPoint }>
     */
    private List<AccessTrendDataDTO.ChartPoint> getRealAccessSpeedTrend(Integer dataModelId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 根据时间范围确定目标数据点数量
            int targetPoints = getTargetPointCount(startTime, endTime);
            
            log.info("开始获取智能采样的接入速度趋势数据, dataModelId: {}, targetPoints: {}", dataModelId, targetPoints);
            
            // 使用智能采样方法获取数据
            List<AccessTrendDataCollectionService.TrendPoint> trendPoints = 
                trendDataCollectionService.getSmartAccessSpeedTrend(dataModelId, startTime, endTime, targetPoints);
            
            // 转换为ChartPoint格式
            List<AccessTrendDataDTO.ChartPoint> result = trendPoints.stream()
                .map(point -> AccessTrendDataDTO.ChartPoint.builder()
                    .time(point.getTime())
                    .value(point.getValue())
                    .build())
                .collect(Collectors.toList());
            
            log.info("智能采样接入速度趋势数据获取完成, dataModelId: {}, 返回点数: {}", dataModelId, result.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取智能采样接入速度趋势数据失败, dataModelId: {}", dataModelId, e);
            // 异常情况下返回均匀分布的0值数据
            return generateUniformZeroValueTrendData(startTime, endTime, "speed");
        }
    }

    /**
     * 获取真实的接入数量趋势数据
     *
     * @param dataModelId 数据模型ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link List }<{@link AccessTrendDataDTO.ChartPoint }>
     */
    private List<AccessTrendDataDTO.ChartPoint> getRealAccessCountTrend(Integer dataModelId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 根据时间范围确定目标数据点数量
            int targetPoints = getTargetPointCount(startTime, endTime);
            
            log.info("开始获取智能采样的接入数量趋势数据, dataModelId: {}, targetPoints: {}", dataModelId, targetPoints);
            
            // 使用智能采样方法获取数据
            List<AccessTrendDataCollectionService.TrendPoint> trendPoints = 
                trendDataCollectionService.getSmartAccessCountTrend(dataModelId, startTime, endTime, targetPoints);
            
            // 转换为ChartPoint格式
            List<AccessTrendDataDTO.ChartPoint> result = trendPoints.stream()
                .map(point -> AccessTrendDataDTO.ChartPoint.builder()
                    .time(point.getTime())
                    .value(point.getValue())
                    .build())
                .collect(Collectors.toList());
            
            log.info("智能采样接入数量趋势数据获取完成, dataModelId: {}, 返回点数: {}", dataModelId, result.size());
            return result;
            
        } catch (Exception e) {
            log.error("获取智能采样接入数量趋势数据失败, dataModelId: {}", dataModelId, e);
            // 异常情况下返回均匀分布的0值数据
            return generateUniformZeroValueTrendData(startTime, endTime, "count");
        }
    }

    /**
     * 获取真实的积压量趋势数据
     *
     * @param dataModelId 数据模型ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link List }<{@link AccessTrendDataDTO.ChartPoint }>
     */
    private List<AccessTrendDataDTO.ChartPoint> getRealLagCountTrend(Integer dataModelId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 根据时间范围确定目标数据点数量
            int targetPoints = getTargetPointCount(startTime, endTime);

            log.info("开始获取智能采样的积压量趋势数据, dataModelId: {}, targetPoints: {}", dataModelId, targetPoints);

            // 使用智能采样方法获取数据
            List<AccessTrendDataCollectionService.TrendPoint> trendPoints =
                trendDataCollectionService.getSmartLagCountTrend(dataModelId, startTime, endTime, targetPoints);

            // 转换为ChartPoint格式
            List<AccessTrendDataDTO.ChartPoint> result = trendPoints.stream()
                .map(point -> AccessTrendDataDTO.ChartPoint.builder()
                    .time(point.getTime())
                    .value(point.getValue())
                    .build())
                .collect(Collectors.toList());

            log.info("智能采样积压量趋势数据获取完成, dataModelId: {}, 返回点数: {}", dataModelId, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取智能采样积压量趋势数据失败, dataModelId: {}", dataModelId, e);
            // 异常情况下返回均匀分布的0值数据
            return generateUniformZeroValueTrendData(startTime, endTime, "lag");
        }
    }

    // ==================== 零值趋势数据生成方法 ====================

    /**
     * 生成均匀分布的零值趋势数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param trendType 趋势类型（speed/count/lag）
     * @return 零值趋势数据列表
     */
    private List<AccessTrendDataDTO.ChartPoint> generateUniformZeroValueTrendData(LocalDateTime startTime, LocalDateTime endTime, String trendType) {
        try {
            log.info("生成均匀分布的零值趋势数据, trendType: {}, startTime: {}, endTime: {}", trendType, startTime, endTime);
            
            // 根据时间范围确定数据点数量
            int targetPoints = getTargetPointCount(startTime, endTime);
            
            List<AccessTrendDataDTO.ChartPoint> result = new ArrayList<>();
            long totalSeconds = java.time.Duration.between(startTime, endTime).getSeconds();
            long intervalSeconds = totalSeconds / (targetPoints - 1);
            
            for (int i = 0; i < targetPoints; i++) {
                LocalDateTime timePoint = startTime.plusSeconds(intervalSeconds * i);
                String timeStr = timePoint.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                
                result.add(AccessTrendDataDTO.ChartPoint.builder()
                    .time(timeStr)
                    .value(0)
                    .build());
            }
            
            log.info("零值趋势数据生成完成, trendType: {}, 数据点数量: {}", trendType, result.size());
            return result;
            
        } catch (Exception e) {
            log.error("生成零值趋势数据失败, trendType: {}", trendType, e);
            return List.of();
        }
    }

    /**
     * 根据时间范围推断时间范围类型
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return {@link String }
     */
    private String getTimeRangeType(LocalDateTime startTime, LocalDateTime endTime) {
        long hours = java.time.Duration.between(startTime, endTime).toHours();
        
        if (hours <= 1) {
            return "LAST_HOUR";
        }
        if (hours <= 24) {
            return "LAST_TODAY";
        }
        if (hours <= 168) {
            return "LAST_WEEK"; // 7 * 24
        }
        if (hours <= 720) {
            return "LAST_MONTH"; // 30 * 24
        }
        return "CUSTOM";
    }

    /**
     * 根据时间范围确定目标数据点数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 目标数据点数量
     */
    private int getTargetPointCount(LocalDateTime startTime, LocalDateTime endTime) {
        long hours = java.time.Duration.between(startTime, endTime).toHours();
        
        if (hours <= 1) {
            return 60;      // 近一小时：60个点（1分钟一个）
        }
        if (hours <= 24) {
            // 🔧 修复：今日或昨天：根据时间范围类型分别处理，不再硬编码24个点
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime yesterdayStart = now.toLocalDate().minusDays(1).atStartOfDay();
            
            // 判断是否为昨天时间范围
            if (startTime.toLocalDate().equals(yesterdayStart.toLocalDate())) {
                // 昨天：返回完整的49个点（00:00到24:00，每30分钟一个点，包括24:00）
                log.info("🕐 昨天时间范围：返回完整的49个数据点（00:00到24:00，每30分钟一个点，包括24:00）");
                return 49;
            } else {
                // 🔧 修改：今日范围返回较大的数值，确保能够获取所有Redis数据点
                if (startTime.toLocalDate().equals(now.toLocalDate()) && startTime.getHour() == 0 && startTime.getMinute() == 0) {
                    // 今日范围：返回1440个点（24小时 * 60分钟），确保能够获取从0:00到当前时间的所有Redis数据点
                    log.info("🕐 今日时间范围：返回1440个数据点，确保能够获取从0:00到当前时间的所有Redis数据点");
                    return 1440;
                } else {
                    // 其他24小时内范围：每30分钟一个点
                    long currentHour = now.getHour();
                    long currentMinute = now.getMinute();
                    
                    // 计算数据点数量：每30分钟一个点
                    // 如果当前分钟数 >= 30，则当前小时算2个点；否则算1个点
                    int pointsForCurrentHour = (currentMinute >= 30) ? 2 : 1;
                    
                    // 总点数 = 已完成小时数 * 2 + 当前小时的点数
                    int totalPoints = (int) (currentHour * 2 + pointsForCurrentHour);
                    
                    // 确保至少有2个点，最多48个点（24小时 * 2）
                    totalPoints = Math.max(2, Math.min(totalPoints, 48));
                    
                    log.info("🕐 今日时间范围：当前时间={}:{}, 动态计算数据点数量={}（每30分钟一个点）", 
                        now.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm")), 
                        currentHour, currentMinute, totalPoints);
                    
                    return totalPoints;
                }
            }
        }
        if (hours <= 168) {
            return 7;       // 近一周：7个点（1天一个）
        }
        if (hours <= 720) {
            return 30;      // 近一月：30个点（1天一个）
        }
        return 20;          // 自定义：20个点
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 根据ID获取数据模型
     *
     *
     * @param dataModelId 数据模型ID
     * @return {@link DataModel }
     */
    private DataModel getDataModelById(Integer dataModelId) {
        DataModel dataModel = dataModelMapper.selectById(dataModelId);
        if (dataModel == null) {
            throw new IllegalArgumentException(String.format("数据模型不存在, dataModelId: %d", dataModelId));
        }
        return dataModel;
    }

    /**
     * 获取默认监控状态
     *
     *
     * @return {@link AccessMonitorStatusDTO }
     */
    private AccessMonitorStatusDTO getDefaultMonitorStatus() {
        return AccessMonitorStatusDTO.builder()
            .accessProcess(AccessMonitorStatusDTO.AccessProcess.builder()
                .enable(false)
                .status("error")
                .lastUpdateTime(LocalDateTime.now().format(ISO_FORMATTER))
                .build())
            .permissions(AccessMonitorStatusDTO.Permissions.builder()
                .canView(true)
                .canEdit(false)
                .canConfigure(false)
                .build())
            .build();
    }

    /**
     * 获取默认监控数据
     *
     *
     * @return {@link AccessMonitorDataDTO }
     */
    private AccessMonitorDataDTO getDefaultMonitorData() {
        LocalDateTime now = LocalDateTime.now();
        
        return AccessMonitorDataDTO.builder()
            .monitorTime(now.format(ISO_FORMATTER))
            .lastMonitorTime(now.minusHours(1).format(ISO_FORMATTER))
            .periodDuration("1小时")
            .accessMonitor(AccessMonitorDataDTO.AccessMetrics.builder()
                .accessTotalCount(0)
                .todayAccessCount(0)
                .accessSpeed(0.0)
                .speedUnit("SECOND")
                .todayErrorCount(0)
                .sourceLagCount(0)          // 🔧 新增：默认积压量
                .hasLag(false)              // 🔧 新增：默认无积压
                .lagStatus("无积压")        // 🔧 新增：默认积压状态
                .lagCount(0)                // 保持向后兼容
                .remark("监控数据获取失败")
                .build())
            .connectionMonitor(AccessMonitorDataDTO.ConnectionMetrics.builder()
                .isConnected(false)
                .reconnectCount(0)
                .lastConnectTime(System.currentTimeMillis())
                .connectionDuration(0L)
                .errorCount(0)
                .remark("连接状态检查失败")
                .build())
            .sourceInfoList(List.of())
            .remark("获取监控数据失败")
            .build();
    }

    /**
     * 转换接入指标数据格式
     *
     * @param metrics 接入指标数据
     * @return {@link AccessMonitorDataDTO.AccessMetrics }
     */
    private AccessMonitorDataDTO.AccessMetrics convertAccessMetrics(AccessMonitorDataDTO.AccessMetrics metrics) {
        if (metrics == null) {
            return AccessMonitorDataDTO.AccessMetrics.builder()
                .accessTotalCount(0)
                .todayAccessCount(0)
                .accessSpeed(0.0)
                .speedUnit("SECOND")
                .todayErrorCount(0)
                .sourceLagCount(0)          // 🔧 新增：默认积压量
                .hasLag(false)              // 🔧 新增：默认无积压
                .lagStatus("无积压")        // 🔧 新增：默认积压状态
                .lagCount(0)                // 保持向后兼容
                .remark("数据获取失败")
                .build();
        }

        return AccessMonitorDataDTO.AccessMetrics.builder()
            .accessTotalCount(metrics.getAccessTotalCount() != null ? metrics.getAccessTotalCount() : 0)
            .todayAccessCount(metrics.getTodayAccessCount() != null ? metrics.getTodayAccessCount() : 0)
            .accessSpeed(metrics.getAccessSpeed() != null ? metrics.getAccessSpeed() : 0.0)
            .speedUnit(metrics.getSpeedUnit() != null ? metrics.getSpeedUnit() : "SECOND")
            .todayErrorCount(metrics.getTodayErrorCount() != null ? metrics.getTodayErrorCount() : 0)
            .lagCount(metrics.getLagCount() != null ? metrics.getLagCount() : 0)
            .sourceLagCount(metrics.getSourceLagCount() != null ? metrics.getSourceLagCount() : 0)  // 🔧 新增
            .hasLag(metrics.getHasLag() != null ? metrics.getHasLag() : false)                    // 🔧 新增
            .lagStatus(metrics.getLagStatus() != null ? metrics.getLagStatus() : "无积压")        // 🔧 新增
            .remark(metrics.getRemark())
            .build();
    }

    /**
     * 转换连接指标数据格式
     *
     * @param metrics 连接指标数据
     * @return {@link AccessMonitorDataDTO.ConnectionMetrics }
     */
    private AccessMonitorDataDTO.ConnectionMetrics convertConnectionMetrics(AccessMonitorDataDTO.ConnectionMetrics metrics) {
        if (metrics == null) {
            return AccessMonitorDataDTO.ConnectionMetrics.builder()
                .isConnected(false)
                .reconnectCount(0)
                .lastConnectTime(System.currentTimeMillis())
                .connectionDuration(0L)
                .errorCount(0)
                .remark("连接状态获取失败")
                .build();
        }

        // 转换时间戳
        Number lastConnectTime = System.currentTimeMillis();
        if (metrics.getLastConnectTime() != null) {
            lastConnectTime = metrics.getLastConnectTime();
        }

        return AccessMonitorDataDTO.ConnectionMetrics.builder()
            .isConnected(metrics.getIsConnected() != null ? metrics.getIsConnected() : false)
            .reconnectCount(metrics.getReconnectCount() != null ? metrics.getReconnectCount() : 0)
            .lastConnectTime(lastConnectTime)
            .connectionDuration(metrics.getConnectionDuration() != null ? metrics.getConnectionDuration() : 0L)
            .errorCount(metrics.getErrorCount() != null ? metrics.getErrorCount() : 0)
            .remark(metrics.getRemark())
            .build();
    }

    /**
     * 转换数据源信息列表格式
     *
     * @param sourceInfoList  数据源信息列表
     * @return {@link List }<{@link AccessMonitorDataDTO.SourceInfo }>
     */
    private List<AccessMonitorDataDTO.SourceInfo> convertSourceInfoList(List<AccessMonitorDataDTO.SourceInfo> sourceInfoList) {
        if (sourceInfoList == null || sourceInfoList.isEmpty()) {
            return List.of();
        }

        return sourceInfoList.stream()
            .map(this::convertSourceInfo)
            .toList();
    }

    /**
     * 转换单个数据源信息格式
     *
     * @param sourceInfo 数据源信息
     * @return {@link AccessMonitorDataDTO.SourceInfo }
     */
    private AccessMonitorDataDTO.SourceInfo convertSourceInfo(AccessMonitorDataDTO.SourceInfo sourceInfo) {
        if (sourceInfo == null) {
            return AccessMonitorDataDTO.SourceInfo.builder()
                .sourceName("未知数据源")
                .connectionType("UNKNOWN")
                .category("UNKNOWN")
                .dataCount(0)
                .status("获取失败")
                .build();
        }

        // 转换枚举为字符串
        String connectionTypeStr = "UNKNOWN";
        if (sourceInfo.getConnectionType() != null) {
            connectionTypeStr = (String) sourceInfo.getConnectionType();
        }

        String categoryStr = "UNKNOWN";
        if (sourceInfo.getCategory() != null) {
            categoryStr = (String) sourceInfo.getCategory();
        }

        return AccessMonitorDataDTO.SourceInfo.builder()
            .sourceName(sourceInfo.getSourceName() != null ? sourceInfo.getSourceName() : "未知数据源")
            .connectionType(connectionTypeStr)
            .category(categoryStr)
            .dataCount(sourceInfo.getDataCount() != null ? sourceInfo.getDataCount() : 0)
            .status(sourceInfo.getStatus() != null ? sourceInfo.getStatus() : "未知状态")
            .build();
    }

    // ==================== 趋势数据生成方法 ====================

    /**
     * 生成接入速度趋势数据
     *
     * @param timeParams 时间范围参数
     * @return {@link List }<{@link AccessTrendDataDTO.ChartPoint }>
     */
    private List<AccessTrendDataDTO.ChartPoint> generateAccessSpeedTrend(TimeRangeParamsDTO timeParams) {
        List<AccessTrendDataDTO.ChartPoint> trendData = new ArrayList<>();
        Random random = new Random();
        
        // 根据时间范围类型生成不同数量的数据点
        int pointCount = getPointCountByTimeRange(timeParams.getType());
        
        LocalDateTime startTime = getStartTimeByTimeRange(timeParams);
        LocalDateTime endTime = getEndTimeByTimeRange(timeParams);
        
        long intervalSeconds = java.time.Duration.between(startTime, endTime).getSeconds() / pointCount;
        
        for (int i = 0; i < pointCount; i++) {
            LocalDateTime timePoint = startTime.plusSeconds(intervalSeconds * i);
            
            // 生成模拟的接入速度数据（0.5 - 2.5 条/秒）
            double speed = 0.5 + random.nextDouble() * 2.0;
            
            trendData.add(AccessTrendDataDTO.ChartPoint.builder()
                .time(timePoint.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .value(Math.round(speed * 100.0) / 100.0) // 保留两位小数
                .build());
        }
        
        return trendData;
    }

    /**
     * 生成接入数量趋势数据
     *
     * @param timeParams 时间范围参数
     * @return {@link List }<{@link AccessTrendDataDTO.ChartPoint }>
     */
    private List<AccessTrendDataDTO.ChartPoint> generateAccessCountTrend(TimeRangeParamsDTO timeParams) {
        List<AccessTrendDataDTO.ChartPoint> trendData = new ArrayList<>();
        Random random = new Random();
        
        int pointCount = getPointCountByTimeRange(timeParams.getType());
        LocalDateTime startTime = getStartTimeByTimeRange(timeParams);
        LocalDateTime endTime = getEndTimeByTimeRange(timeParams);
        
        long intervalSeconds = java.time.Duration.between(startTime, endTime).getSeconds() / pointCount;
        
        long cumulativeCount = 0;
        for (int i = 0; i < pointCount; i++) {
            LocalDateTime timePoint = startTime.plusSeconds(intervalSeconds * i);
            
            // 生成模拟的接入数量数据（每次增加100-500条）
            long increment = 100 + random.nextInt(400);
            cumulativeCount += increment;
            
            trendData.add(AccessTrendDataDTO.ChartPoint.builder()
                .time(timePoint.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .value(cumulativeCount)
                .build());
        }
        
        return trendData;
    }

    /**
     * 根据时间范围类型获取数据点数量
     *
     * @param timeRangeType 时间范围类型
     * @return int
     */
    private int getPointCountByTimeRange(String timeRangeType) {
        return switch (timeRangeType) {
            case "LAST_HOUR" -> 12;      // 每小时12个点（5分钟一个）
            case "LAST_TODAY" -> 24;     // 每天24个点（1小时一个）
            case "LAST_YESTERDAY" -> 24; // 昨天24个点（1小时一个）
            case "LAST_WEEK" -> 7;       // 每周7个点（1天一个）
            case "LAST_MONTH" -> 30;     // 每月30个点（1天一个）
            case "ALL" -> 50;            // 全部时间50个点
            case "CUSTOM" -> 20;         // 自定义时间20个点
            default -> 12;
        };
    }

    /**
     * 根据时间范围类型获取开始时间
     *
     * @param timeParams 时间范围参数
     * @return {@link LocalDateTime }
     */
    private LocalDateTime getStartTimeByTimeRange(TimeRangeParamsDTO timeParams) {
        LocalDateTime now = LocalDateTime.now();
        
        return switch (timeParams.getType()) {
            case "LAST_HOUR" -> now.minusHours(1);
            case "LAST_TODAY" -> now.toLocalDate().atStartOfDay();
            case "LAST_YESTERDAY" -> now.toLocalDate().minusDays(1).atStartOfDay();
            case "LAST_WEEK" -> now.minusWeeks(1);
            case "LAST_MONTH" -> now.minusMonths(1);
            case "ALL" -> now.minusYears(1);
            case "CUSTOM" -> {
                if (timeParams.getBeginTime() != null) {
                    yield LocalDateTime.parse(timeParams.getBeginTime(), 
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } else {
                    yield now.minusHours(1);
                }
            }
            default -> now.minusHours(1);
        };
    }

    /**
     * 根据时间范围类型获取结束时间
     *
     * @param timeParams 时间范围参数
     * @return {@link LocalDateTime }
     */
    private LocalDateTime getEndTimeByTimeRange(TimeRangeParamsDTO timeParams) {
        LocalDateTime now = LocalDateTime.now();
        
        return switch (timeParams.getType()) {
            case "LAST_HOUR" -> now;
            case "LAST_TODAY" -> now; // 🔧 修改：今日结束时间改为当前时间，而不是23:59:59
            case "LAST_YESTERDAY" -> now.toLocalDate().minusDays(1).atTime(23, 59, 59);
            case "LAST_WEEK" -> now;
            case "LAST_MONTH" -> now;
            case "ALL" -> now;
            case "CUSTOM" -> {
                if (timeParams.getEndTime() != null) {
                    yield LocalDateTime.parse(timeParams.getEndTime(), 
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } else {
                    yield now;
                }
            }
            default -> now;
        };
    }

    /**
     * 获取默认趋势数据
     *
     * @return {@link AccessTrendDataDTO }
     */
    private AccessTrendDataDTO getDefaultTrendData() {
        return AccessTrendDataDTO.builder()
            .accessSpeedTrend(List.of())
            .accessCountTrend(List.of())
            .lagCountTrend(List.of())
            .build();
    }

    /**
     * 构建接入指标数据，集成积压指标
     *
     * @param accessMetrics 接入指标数据
     * @param lagMetrics 积压指标数据
     * @return {@link AccessMonitorDataDTO.AccessMetrics }
     */
    private AccessMonitorDataDTO.AccessMetrics buildAccessMetricsWithLag(AccessMonitorDataDTO.AccessMetrics accessMetrics, LagMetrics lagMetrics) {
        if (accessMetrics == null) {
            return AccessMonitorDataDTO.AccessMetrics.builder()
                .accessTotalCount(0)
                .todayAccessCount(0)
                .accessSpeed(0.0)
                .speedUnit("SECOND")
                .todayErrorCount(0)
                .sourceLagCount(0)          // 新增：默认积压量
                .hasLag(false)              // 新增：默认无积压
                .lagStatus("无积压")        // 新增：默认积压状态
                .lagCount(0)                // 保持向后兼容
                .remark("数据获取失败")
                .build();
        }

        return AccessMonitorDataDTO.AccessMetrics.builder()
            .accessTotalCount(accessMetrics.getAccessTotalCount() != null ? accessMetrics.getAccessTotalCount() : 0)
            .todayAccessCount(accessMetrics.getTodayAccessCount() != null ? accessMetrics.getTodayAccessCount() : 0)
            .accessSpeed(accessMetrics.getAccessSpeed() != null ? accessMetrics.getAccessSpeed() : 0.0)
            .speedUnit(accessMetrics.getSpeedUnit() != null ? accessMetrics.getSpeedUnit() : "SECOND")
            .todayErrorCount(accessMetrics.getTodayErrorCount() != null ? accessMetrics.getTodayErrorCount() : 0)
            .lagCount(accessMetrics.getLagCount() != null ? accessMetrics.getLagCount() : 0)
            .sourceLagCount(lagMetrics.getSourceLagCount() != null ? lagMetrics.getSourceLagCount() : 0)  // 新增
            .hasLag(lagMetrics.getHasLag() != null ? lagMetrics.getHasLag() : false)                    // 新增
            .lagStatus(lagMetrics.getLagStatus() != null ? lagMetrics.getLagStatus() : "无积压")        // 新增
            .remark(accessMetrics.getRemark())
            .build();
    }

    /**
     * 收集积压监控指标
     *
     * @param dataModel 数据模型
     * @param connectionType 数据源连接类型
     * @return {@link LagMetrics }
     */
    private LagMetrics collectLagMetrics(DataModel dataModel, com.trs.moye.base.data.connection.enums.ConnectionType connectionType) {
        try {
            log.info("开始收集积压监控指标, dataModelId: {}, connectionType: {}", dataModel.getId(), connectionType);
            
            // 检查数据模型是否启用了积压监控
            DataModelMonitorConfig config = dataModelMonitorConfigMapper.getDataModelInuseTypeConfig(dataModel.getId(), MonitorConfigType.LAG);
            boolean isLagEnabled = config != null && config.isEnable();
            log.info("数据模型 {} 积压监控配置: 是否启用={}, 阈值={}", dataModel.getId(), isLagEnabled, config != null ? config.getThreshold() : "N/A");

            if (!isLagEnabled) {
                log.warn("数据模型 {} 未启用积压监控，跳过收集积压指标", dataModel.getId());
                return getDefaultLagMetrics();
            }

            // 使用MonitorFeign调用storage-engine服务获取积压量
            Long sourceLagCount = monitorFeign.getDataSourceLag(new MonitorDTO(dataModel.getId()));
            log.info("数据模型 {} 积压量: {}", dataModel.getId(), sourceLagCount);

            // 根据阈值判断积压状态
            long threshold = config.getThreshold();
            boolean hasLag = sourceLagCount > threshold;
            String lagStatus = hasLag
                    ?
                String.format("积压量 %d 超过阈值 %d", sourceLagCount, threshold)
                    :
                String.format("积压量 %d 在阈值 %d 范围内", sourceLagCount, threshold);

            return LagMetrics.builder()
                .sourceLagCount(sourceLagCount != null ? sourceLagCount : 0L)
                .hasLag(hasLag)
                .lagStatus(lagStatus)
                .build();
                
        } catch (Exception e) {
            log.error("收集积压监控指标失败, dataModelId: {}", dataModel.getId(), e);
            return getDefaultLagMetrics();
        }
    }
    
    /**
     * 生成积压状态描述
     *
     * @param connectionType 连接类型
     * @param sourceLagCount 积压量
     * @param hasLag 是否存在积压
     * @return 积压状态描述
     */
    private String generateLagStatus(com.trs.moye.base.data.connection.enums.ConnectionType connectionType, Long sourceLagCount, boolean hasLag) {
        if (!hasLag) {
            return "无积压";
        }
        
        String baseStatus = "存在积压";
        if (sourceLagCount != null) {
            if (sourceLagCount > 10000) {
                return baseStatus + "（积压量较大：" + formatLagCount(sourceLagCount) + "）";
            } else if (sourceLagCount > 1000) {
                return baseStatus + "（积压量中等：" + formatLagCount(sourceLagCount) + "）";
            } else {
                return baseStatus + "（积压量较小：" + formatLagCount(sourceLagCount) + "）";
            }
        }
        
        return baseStatus;
    }
    
    /**
     * 格式化积压数量
     *
     * @param count 数量
     * @return 格式化后的字符串
     */
    private String formatLagCount(Long count) {
        if (count >= 100000000) {
            return String.format("%.1f亿", count / 100000000.0);
        } else if (count >= 10000) {
            return String.format("%.1f万", count / 10000.0);
        } else if (count >= 1000) {
            return String.format("%.1fk", count / 1000.0);
        } else {
            return count.toString();
        }
    }
    
    /**
     * 获取默认积压指标
     *
     * @return {@link LagMetrics}
     */
    private LagMetrics getDefaultLagMetrics() {
        return LagMetrics.builder()
            .sourceLagCount(0L)           // 默认无积压
            .hasLag(false)                 // 默认无积压
            .lagStatus("积压监控未启用")    // 默认状态描述
            .build();
    }
    
    // 🔧 新增：积压指标内部类
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class LagMetrics {
        private Long sourceLagCount;    // 数据源积压量
        private Boolean hasLag;         // 是否存在积压
        private String lagStatus;       // 积压状态描述
    }
}
