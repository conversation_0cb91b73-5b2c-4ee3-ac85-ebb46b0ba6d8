package com.trs.ai.moye.monitor.controller;

import com.trs.ai.moye.monitor.dto.AccessMonitorDataDTO;
import com.trs.ai.moye.monitor.dto.AccessMonitorStatusDTO;
import com.trs.ai.moye.monitor.dto.AccessTrendDataDTO;
import com.trs.ai.moye.monitor.dto.TimeRangeParamsDTO;
import com.trs.ai.moye.monitor.service.AccessMonitorService;
import com.trs.ai.moye.monitor.service.AccessMonitorListService;
import com.trs.ai.moye.monitor.service.AccessDetailService;
import com.trs.ai.moye.monitor.service.ErrorManagementService;
import com.trs.ai.moye.monitor.service.MonitorConfigService;
import com.trs.ai.moye.monitor.service.AccessTrendDataCollectionService;
import com.trs.ai.moye.monitor.dto.IAccessParams;
import com.trs.ai.moye.monitor.dto.IPaginatedResult;
import com.trs.ai.moye.monitor.entity.AccessMonitorRecord;
import com.trs.ai.moye.monitor.entity.AccessMonitorConfig;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据接入监控控制器
 *
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@Slf4j
@RestController
@RequestMapping("/data-modeling/ods/access-monitor")
public class AccessMonitorController {

    @Resource
    private AccessMonitorService accessMonitorService;

    @Resource
    private AccessMonitorListService accessMonitorListService;

    @Resource
    private AccessDetailService accessDetailService;

    @Resource
    private ErrorManagementService errorManagementService;

    @Resource
    private MonitorConfigService monitorConfigService;

    @Resource
    private AccessTrendDataCollectionService trendDataCollectionService;

    /**
     * 获取接入监控状态信息
     *
     *
     * @param dataModelId 数据模型ID
     * @return 监控状态信息
     */
    @GetMapping("/status/{dataModelId}")
    public Map<String, Object> getAccessMonitorInfo(@PathVariable Integer dataModelId) {
        
        log.info("获取接入监控状态信息, dataModelId: {}", dataModelId);
        
        try {
            AccessMonitorStatusDTO status = accessMonitorService.getAccessMonitorStatus(dataModelId);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", status);
            return result;
        } catch (Exception e) {
            log.error("获取接入监控状态信息失败, dataModelId: {}", dataModelId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取监控状态失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 获取实时监控数据
     *
     *
     * @param dataModelId 数据模型ID
     * @return 接入监控实时数据
     */
    @GetMapping("/data/{dataModelId}")
    public Map<String, Object> getAccessMonitorData(@PathVariable Integer dataModelId) {
        
        log.info("开始获取实时监控数据, dataModelId: {}", dataModelId);
        
        try {
            AccessMonitorDataDTO monitorData = accessMonitorService.getAccessMonitorData(dataModelId);
            
            // 记录获取到的关键数据
            if (monitorData != null && monitorData.getAccessMonitor() != null) {
                log.info("实时监控数据获取成功: dataModelId={}, todayAccessCount={}, totalCount={}, accessSpeed={}, isConnected={}", 
                    dataModelId,
                    monitorData.getAccessMonitor().getTodayAccessCount(),
                    monitorData.getAccessMonitor().getAccessTotalCount(),
                    monitorData.getAccessMonitor().getAccessSpeed(),
                    monitorData.getConnectionMonitor() != null ? monitorData.getConnectionMonitor().getIsConnected() : "N/A");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", monitorData);
            
            log.info("实时监控数据响应构建完成: dataModelId={}, success=true", dataModelId);
            return result;
        } catch (Exception e) {
            log.error("获取实时监控数据失败, dataModelId: {}, 错误信息: {}", dataModelId, e.getMessage(), e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取监控数据失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 获取数据源信息
     *
     *
     * @param dataModelId 数据模型ID
     * @return 数据源列表信息
     */
    @GetMapping("/sources/{dataModelId}")
    public Map<String, Object> getAccessSourceInfo(@PathVariable Integer dataModelId) {
        
        log.info("获取数据源信息, dataModelId: {}", dataModelId);
        
        try {
            List<AccessMonitorDataDTO.SourceInfo> sourceInfoList = accessMonitorService.getAccessSourceInfo(dataModelId);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", sourceInfoList);
            return result;
        } catch (Exception e) {
            log.error("获取数据源信息失败, dataModelId: {}", dataModelId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取数据源信息失败: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * 获取接入趋势数据
     *
     * @param dataModelId 数据模型ID
     * @param timeParams 时间范围参数
     * @return 趋势数据
     */
    @PostMapping("/trend/{dataModelId}")
    public Map<String, Object> getAccessTrendData(@PathVariable Integer dataModelId, 
                                                  @RequestBody TimeRangeParamsDTO timeParams) {
        
        log.info("获取接入趋势数据, dataModelId: {}, timeParams: {}", dataModelId, timeParams);
        
        try {
            AccessTrendDataDTO trendData = accessMonitorService.getAccessTrendData(dataModelId, timeParams);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", trendData);
            return result;
        } catch (Exception e) {
            log.error("获取接入趋势数据失败, dataModelId: {}", dataModelId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取趋势数据失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 获取接入明细列表
     *
     * @param dataModelId 数据模型ID
     * @param params 接入明细查询参数
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    @PostMapping("/list/{dataModelId}")
    public Map<String, Object> getAccessMonitorList(@PathVariable Integer dataModelId,
                                                   @RequestBody IAccessParams params) {
        log.info("获取接入明细列表, dataModelId: {}, params: {}", dataModelId, params);
        
        try {
            params.setDataModelId(dataModelId);
            IPaginatedResult<AccessMonitorRecord> result = accessMonitorListService.getAccessMonitorList(params);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result);
            return response;
        } catch (Exception e) {
            log.error("获取接入明细列表失败, dataModelId: {}", dataModelId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取接入明细列表失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 获取接入明细详情
     *
     * @param recordId 接入记录ID
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    @GetMapping("/detail/{recordId}")
    public Map<String, Object> getAccessDetail(@PathVariable String recordId) {
        log.info("获取接入明细详情, recordId: {}", recordId);
        
        try {
            AccessMonitorRecord detail = accessDetailService.getAccessDetail(recordId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", detail);
            return response;
        } catch (Exception e) {
            log.error("获取接入明细详情失败, recordId: {}", recordId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取接入明细详情失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 获取未读错误数量
     *
     * @param dataModelId 数据模型ID
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    @GetMapping("/error-count/{dataModelId}")
    public Map<String, Object> getErrorCount(@PathVariable Integer dataModelId) {
        log.info("获取未读错误数量, dataModelId: {}", dataModelId);
        
        try {
            int errorCount = errorManagementService.getUnreadErrorCount(dataModelId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", errorCount);
            return response;
        } catch (Exception e) {
            log.error("获取未读错误数量失败, dataModelId: {}", dataModelId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取未读错误数量失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 标记错误为已读
     *
     * @param dataModelId 数据模型ID
     * @param request 请求体，包含要标记的记录ID列表
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    @PostMapping("/mark-read/{dataModelId}")
    public Map<String, Object> markErrorAsRead(@PathVariable Integer dataModelId,
                                              @RequestBody Map<String, Object> request) {
        log.info("标记错误为已读, dataModelId: {}, request: {}", dataModelId, request);
        
        try {
            @SuppressWarnings("unchecked")
            List<String> recordIds = (List<String>) request.get("recordIds");
            boolean success = errorManagementService.markErrorAsRead(dataModelId, recordIds);
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "标记成功" : "标记失败");
            return response;
        } catch (Exception e) {
            log.error("标记错误为已读失败, dataModelId: {}", dataModelId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "标记错误为已读失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 获取监控配置
     *
     * @param dataModelId 数据模型ID
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    @GetMapping("/config/{dataModelId}")
    public Map<String, Object> getMonitorConfig(@PathVariable Integer dataModelId) {
        log.info("获取监控配置, dataModelId: {}", dataModelId);
        
        try {
            AccessMonitorConfig config = monitorConfigService.getConfig(dataModelId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", config);
            return response;
        } catch (Exception e) {
            log.error("获取监控配置失败, dataModelId: {}", dataModelId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取监控配置失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 切换监控状态
     *
     * @param dataModelId 数据模型ID
     * @param request 请求体，包含监控状态
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    @PostMapping("/toggle/{dataModelId}")
    public Map<String, Object> toggleMonitor(@PathVariable Integer dataModelId,
                                            @RequestBody Map<String, Object> request) {
        log.info("切换监控状态, dataModelId: {}, request: {}", dataModelId, request);
        
        try {
            Boolean enabled = (Boolean) request.get("enabled");
            boolean success = monitorConfigService.toggleMonitor(dataModelId, enabled);
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "切换成功" : "切换失败");
            return response;
        } catch (Exception e) {
            log.error("切换监控状态失败, dataModelId: {}", dataModelId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "切换监控状态失败: " + e.getMessage());
            return response;
        }
    }
    
    /**
     * 获取不同存储点的今日存储统计信息
     *
     * @param dataModelId 数据模型ID
     * @return 不同存储点的今日存储统计信息
     */
    @GetMapping("/storage-stats/{dataModelId}")
    public Map<String, Object> getStorageStats(@PathVariable Integer dataModelId) {
        
        log.info("=== 开始处理获取不同存储点的今日存储统计信息请求 ===");
        log.info("请求参数: dataModelId={}", dataModelId);
        
        try {
            log.info("调用AccessMonitorService.getStorageStats方法, dataModelId: {}", dataModelId);
            Map<String, Object> storageStats = accessMonitorService.getStorageStats(dataModelId);
            
            log.info("AccessMonitorService.getStorageStats方法调用成功, dataModelId: {}, storageStats: {}", 
                dataModelId, storageStats);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", storageStats);
            
            log.info("=== 获取不同存储点的今日存储统计信息请求处理完成 ===");
            log.info("返回结果: success=true, dataSize={}", storageStats.size());
            
            return result;
        } catch (Exception e) {
            log.error("=== 获取不同存储点的今日存储统计信息请求处理失败 ===");
            log.error("请求参数: dataModelId={}", dataModelId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取存储统计信息失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 获取指定存储点的今日存储统计信息
     *
     * @param dataModelId 数据模型ID
     * @param storageId 存储点ID
     * @return 指定存储点的今日存储统计信息
     */
    @GetMapping("/storage-stats/{dataModelId}/{storageId}")
    public Map<String, Object> getStorageStatsByStorageId(@PathVariable Integer dataModelId, 
                                                          @PathVariable Integer storageId) {
        
        log.info("=== 开始处理获取指定存储点的今日存储统计信息请求 ===");
        log.info("请求参数: dataModelId={}, storageId={}", dataModelId, storageId);
        
        try {
            log.info("调用AccessMonitorService.getStorageStatsByStorageId方法, dataModelId: {}, storageId: {}", 
                dataModelId, storageId);
            Map<String, Object> storageStats = accessMonitorService.getStorageStatsByStorageId(dataModelId, storageId);
            
            log.info("AccessMonitorService.getStorageStatsByStorageId方法调用成功, dataModelId: {}, storageId: {}, storageStats: {}", 
                dataModelId, storageId, storageStats);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", storageStats);
            
            log.info("=== 获取指定存储点的今日存储统计信息请求处理完成 ===");
            log.info("返回结果: success=true, dataSize={}", storageStats.size());
            
            return result;
        } catch (Exception e) {
            log.error("=== 获取指定存储点的今日存储统计信息请求处理失败 ===");
            log.error("请求参数: dataModelId={}, storageId={}", dataModelId, storageId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取存储统计信息失败: " + e.getMessage());
            return result;
        }
    }
    
}
