package com.trs.ai.moye.homepage.service.impl.collect;

import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.homepage.dao.DashBoardMapper;
import com.trs.ai.moye.homepage.entity.AggregatedStat;
import com.trs.ai.moye.homepage.entity.DashBoardData;
import com.trs.ai.moye.homepage.enums.HomePageShowData;
import com.trs.ai.moye.homepage.enums.StatType;
import com.trs.ai.moye.homepage.service.DataCollectService;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 收集算子工厂的信息 算子的数量和已执行的算子的数量.
 *
 * <AUTHOR>
 * @since 2025/1/22
 **/
@Service
public class OperatorFactoryCollector implements DataCollectService {

    @Resource
    private AbilityMapper abilityMapper;

    @Resource
    private DashBoardMapper dashBoardMapper;

    /**
     * 获取首页的流水线 算子工厂的信息 .
     *
     * @return {@link DashBoardData}
     */
    @Override
    public List<DashBoardData> collectData() {
        Long operatorCount = abilityMapper.selectCount(null);
        Long executedOperatorTotalCount = dashBoardMapper.getExecutedOperatorTotalCount();
        return DashBoardData.buildData(HomePageShowData.OPERATOR_FACTORY, operatorCount, executedOperatorTotalCount);
    }

    @Override
    public AggregatedStat getIncrementStat(LocalDateTime startTime, LocalDateTime endTime) {
        Long executedOperatorIncrementCount = dashBoardMapper.getExecutedOperatorIncrementCount(startTime, endTime);
        AggregatedStat aggregatedStat = new AggregatedStat();
        aggregatedStat.setStatType(StatType.OPERATOR_INVOKE)
            .setTotal(executedOperatorIncrementCount)
            .setLastStatTime(endTime);
        return aggregatedStat;
    }

}
