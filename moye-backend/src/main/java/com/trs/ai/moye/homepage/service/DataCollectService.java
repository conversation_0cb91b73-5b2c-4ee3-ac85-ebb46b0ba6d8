package com.trs.ai.moye.homepage.service;

import com.trs.ai.moye.homepage.entity.AggregatedStat;
import com.trs.ai.moye.homepage.entity.DashBoardData;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 主要收集清洗各个模块的数据
 *
 * <AUTHOR>
 * @since 2025/1/17
 **/

public interface DataCollectService {

    /**
     * 收集数据
     *
     * @return {@link DashBoardData}
     */
    List<DashBoardData> collectData();


    /**
     * 获取指定时间范围内的增量数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 增量数据
     */
    AggregatedStat getIncrementStat(LocalDateTime startTime, LocalDateTime endTime);



}
