package com.trs.ai.moye.monitor.controller;

import com.trs.ai.moye.data.model.response.CategoryTreeResponse;
import com.trs.ai.moye.monitor.service.AccessService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/10/25
 **/

@RestController
@RequestMapping("/monitor/access")
public class AccessController {

    @Resource
    private AccessService accessService;

    /**
     * 获取任务数据模型树
     * <a href="http://**************:3001/project/4220/interface/api/165235">http://localhost:8080/monitor/access/task/data-model/tree</a>
     *
     * @return {@link CategoryTreeResponse}
     */
    @GetMapping("/task/data-model/tree")
    public List<CategoryTreeResponse> taskDataModelTree() {
        return accessService.taskDataModelTree();
    }

}
