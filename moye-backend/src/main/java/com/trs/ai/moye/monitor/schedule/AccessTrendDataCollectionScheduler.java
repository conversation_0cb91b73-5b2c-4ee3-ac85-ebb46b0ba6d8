package com.trs.ai.moye.monitor.schedule;

import com.trs.ai.moye.monitor.service.AccessTrendDataCollectionService;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import com.trs.moye.base.common.enums.ModelLayer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 接入监控趋势数据采集定时任务
 * 每分钟采集一次所有启用状态数据模型的监控指标
 *
 * <AUTHOR>
 * @since 2025-08-22
 */
@Slf4j
@Component
public class AccessTrendDataCollectionScheduler {

    @Resource
    private AccessTrendDataCollectionService trendDataCollectionService;

    @Resource
    private DataModelMapper dataModelMapper;

    /**
     * 每分钟执行一次趋势数据采集
     * cron表达式：每分钟的第0秒执行
     */
    @Scheduled(cron = "0 * * * * ?")
    public void collectAccessTrendData() {
        log.debug("开始执行接入监控趋势数据采集定时任务");
        
        try {
            // 获取所有启用状态的数据模型
            List<DataModel> enabledDataModels = getEnabledDataModels();
            
            log.info("找到 {} 个启用状态的数据模型需要采集趋势数据", enabledDataModels.size());
            
            // 为每个数据模型采集趋势数据
            for (DataModel dataModel : enabledDataModels) {
                try {
                    trendDataCollectionService.collectAccessTrendPoint(dataModel.getId());
                    log.debug("数据模型 {} 趋势数据采集完成", dataModel.getId());
                } catch (Exception e) {
                    log.error("数据模型 {} 趋势数据采集失败", dataModel.getId(), e);
                }
            }
            
            log.info("接入监控趋势数据采集定时任务执行完成，共处理 {} 个数据模型", enabledDataModels.size());
            
        } catch (Exception e) {
            log.error("接入监控趋势数据采集定时任务执行失败", e);
        }
    }

    /**
     * 获取所有启用状态的数据模型
     *
     * @return {@link List }<{@link DataModel }>
     */
    private List<DataModel> getEnabledDataModels() {
        try {
            // 查询所有启用状态的ODS数据模型
            return dataModelMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<DataModel>()
                    .eq(DataModel::getExecuteStatus, ModelExecuteStatus.START)
                    .eq(DataModel::getLayer, ModelLayer.ODS)
            );
        } catch (Exception e) {
            log.error("获取启用状态数据模型失败", e);
            return List.of();
        }
    }

    /**
     * 应用启动时的数据初始化
     * 为避免在系统重启后趋势图为空，可以在启动时补充最近的数据点
     */
    @org.springframework.context.event.EventListener(org.springframework.boot.context.event.ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("应用启动完成，开始初始化接入监控趋势数据");
        
        try {
            // 延迟30秒后执行，确保所有服务都已启动
            Thread.sleep(30000);
            
            // 执行一次数据采集
            collectAccessTrendData();
            
            log.info("接入监控趋势数据初始化完成");
            
        } catch (Exception e) {
            log.error("接入监控趋势数据初始化失败", e);
        }
    }
}
