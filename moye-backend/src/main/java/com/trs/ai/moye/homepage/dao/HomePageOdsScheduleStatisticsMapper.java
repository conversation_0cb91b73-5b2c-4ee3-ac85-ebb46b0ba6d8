package com.trs.ai.moye.homepage.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.homepage.entity.HomePageOdsScheduleStatistics;
import com.trs.ai.moye.monitor.request.StatisticRequest;
import com.trs.ai.moye.monitor.response.statistics.TaskStatisticsListResponse;
import com.trs.moye.base.common.request.SortParams;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 贴源库首页调度统计mapper
 *
 * <AUTHOR>
 * @since 2025/5/25
 */
@DS("clickhouse")
@Mapper
public interface HomePageOdsScheduleStatisticsMapper extends BaseMapper<HomePageOdsScheduleStatistics> {

    /**
     * 查询统计表中所有数据
     *
     * @param type         统计表类型
     * @param dataModelIds 数据模型ID
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 统计表数据
     */
    List<HomePageOdsScheduleStatistics> selectAll(@Param("type") String type,
        @Param("dataModelIds") List<Integer> dataModelIds,
        @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 最后一次同步时间
     *
     * @return 最后一次同步时间
     */
    LocalDateTime selectMaxSyncTime();


    /**
     * 更新数据
     *
     * @param data 数据
     */
    void updateData(@Param("originalData") HomePageOdsScheduleStatistics data);


    /**
     * 任务统计下转列表页面
     *
     * @param request   请求参数
     * @param toPage    分页参数
     * @param sortable  排序
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param errorFlag 是否异常
     * @param keyword   关键词
     * @param mode      接入方式
     * @return 列表数据
     */
    Page<TaskStatisticsListResponse> taskList(@Param("request") StatisticRequest request,
        Page<TaskStatisticsListResponse> toPage, @Param("sortRequest") SortParams sortable,
        @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("errorFlag") Boolean errorFlag,
        @Param("keyword") String keyword, @Param("mode") String mode);

    /**
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param errorFlag 是否异常
     * @param keyword   关键词
     * @param mode      接入方式
     * @return 总数
     */
    Long selectAmount(@Param("startTime") String startTime, @Param("endTime") String endTime,
        @Param("errorFlag") Boolean errorFlag,
        @Param("keyword") String keyword, @Param("mode") String mode);
}
