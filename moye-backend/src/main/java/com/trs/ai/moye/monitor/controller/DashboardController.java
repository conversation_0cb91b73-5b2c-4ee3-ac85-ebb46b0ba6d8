package com.trs.ai.moye.monitor.controller;

import com.trs.ai.moye.common.dao.GroupCount;
import com.trs.ai.moye.data.model.dao.DataModelDisplayMapper;
import com.trs.ai.moye.data.model.dao.DataSourceDisplayMapper;
import com.trs.ai.moye.data.model.dao.DataStorageDisplayMapper;
import com.trs.ai.moye.homepage.dao.HomePageDwdStatisticsMapper;
import com.trs.ai.moye.homepage.dao.HomePageOdsStorageStatisticsMapper;
import com.trs.ai.moye.homepage.dao.HomePageSubjectStatisticsMapper;
import com.trs.ai.moye.homepage.dao.HomePageThemeStatisticsMapper;
import com.trs.ai.moye.monitor.response.DashboardResponse;
import com.trs.ai.moye.monitor.response.DashboardResponse.Item;
import com.trs.ai.moye.monitor.response.statistics.DataStatisticsResponse;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.enums.ArrangedStatus;
import com.trs.moye.base.data.model.enums.CreateTableStatus;
import com.trs.moye.base.data.model.enums.ModelExecuteStatus;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * DashboardController
 *
 * <AUTHOR>
 * @since 2024/10/25 19:39
 */
@RestController
@RequestMapping("/monitor")
public class DashboardController {

    @Resource
    private DataModelDisplayMapper dataModelDisplayMapper;
    @Resource
    private DataStorageDisplayMapper dataStorageDisplayMapper;
    @Resource
    private DataSourceDisplayMapper dataSourceDisplayMapper;
    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private HomePageDwdStatisticsMapper homePageDwdStatisticsMapper;

    @Resource
    private HomePageThemeStatisticsMapper homePageThemeStatisticsMapper;

    @Resource
    private HomePageSubjectStatisticsMapper homePageSubjectStatisticsMapper;

    @Resource
    private HomePageOdsStorageStatisticsMapper odsStorageStatisticsMapper;

    /**
     * 按 执行状态 分类统计贴源/要素表的数量<br>
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/165205">YApi</a>
     *
     * @param modelLayer 数据建模分层
     * @return 分类统计结果
     */
    @GetMapping("/{modelLayer}/dashboard/execute-status")
    public DashboardResponse getExecuteStatusDashboard(@PathVariable ModelLayer modelLayer) {
        List<GroupCount<ModelExecuteStatus>> groupCounts = dataModelDisplayMapper.selectExecuteStatusCounts(modelLayer);
        DashboardResponse dashboardResponse = new DashboardResponse(groupCounts, ModelExecuteStatus::getLabel);
        dashboardResponse.setTotal(dataModelMapper.countByModelLayer(modelLayer));

        Map<ModelLayer, Supplier<Long>> storageCountStrategy = Map.of(
            ModelLayer.ODS, () -> {
                DataStatisticsResponse dataStatisticsResponse = odsStorageStatisticsMapper.selectTotalCount();
                return Objects.nonNull(dataStatisticsResponse) ? dataStatisticsResponse.getTotal() : 0L;
            },
            ModelLayer.DWD, homePageDwdStatisticsMapper::selectTotalCount,
            ModelLayer.THEME, homePageThemeStatisticsMapper::selectTotalCount,
            ModelLayer.SUBJECT, homePageSubjectStatisticsMapper::selectTotalCount
        );

        dashboardResponse.setStorageCount(storageCountStrategy.getOrDefault(modelLayer, () -> 0L).get());
        return dashboardResponse;
    }

    /**
     * 按 建表状态 分类统计贴源/要素表的数量<br>
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/165210">YApi</a>
     *
     * @param modelLayer 数据建模分层
     * @return 分类统计结果
     */
    @GetMapping("/{modelLayer}/dashboard/table-status")
    public DashboardResponse getTableStatusDashboard(@PathVariable ModelLayer modelLayer) {
        List<GroupCount<CreateTableStatus>> groupCounts = dataStorageDisplayMapper.selectCreateStatusCounts(modelLayer);
        return new DashboardResponse(groupCounts, CreateTableStatus::getLabel);
    }

    /**
     * 按 业务分类 分类统计贴源/要素表的数量<br>
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/165215">YApi</a>
     *
     * @param modelLayer 数据建模分层
     * @return 分类统计结果
     */
    @GetMapping("/{modelLayer}/dashboard/business-category")
    public DashboardResponse getBusinessCategoryDashboard(@PathVariable ModelLayer modelLayer) {
        List<GroupCount<String>> groupCounts = dataModelDisplayMapper.selectBusinessCategoryCounts(modelLayer);
        return new DashboardResponse(groupCounts);
    }

    /**
     * 按 数据源类型 分类统计贴源/要素表的数量<br>
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/165220">YApi</a>
     *
     * @param modelLayer 数据建模分层
     * @return 分类统计结果
     */
    @GetMapping("/{modelLayer}/dashboard/data-source")
    public DashboardResponse getDataSourceTypeDashboard(@PathVariable ModelLayer modelLayer) {
        List<GroupCount<ConnectionType>> groupCounts = dataSourceDisplayMapper.selectDataSourceTypeCounts(modelLayer);

        // 根据 DataSourceCategory 进行聚合
        Map<DataSourceCategory, Long> dataSourceCategory2Count = groupCounts.stream()
            .collect(Collectors.groupingBy(
                groupCount -> groupCount.getValue().getCategory(), // 按 DataSourceCategory 分组
                Collectors.summingLong(GroupCount::getCount) // 计算每个分组的总和
            ));

        List<Item> items = new ArrayList<>();
        Long total = dataSourceCategory2Count.entrySet().stream().mapToLong(dataSourceCategory -> {
            Item item = new Item();
            item.setName(dataSourceCategory.getKey().getLabel());
            item.setCount(dataSourceCategory.getValue());
            item.setValue(dataSourceCategory.getKey());
            items.add(item);
            return dataSourceCategory.getValue();
        }).sum();

        return new DashboardResponse(total, items, 0L);
    }

    /**
     * 获取要素库流处理/批处理各自的数量
     *
     * @return DashboardResponse
     */
    @GetMapping("/DWD/dashboard/deal-type")
    public DashboardResponse getDwdDealType() {
        List<GroupCount<ConnectionType>> groupCounts = dataSourceDisplayMapper.selectDataSourceTypeCounts(
            ModelLayer.DWD);
        // 根据 DataSourceCategory 进行聚合
        Map<DataSourceCategory, Long> dataSourceCategory2Count = groupCounts.stream()
            .collect(Collectors.groupingBy(
                groupCount -> groupCount.getValue().getCategory(), // 按 DataSourceCategory 分组
                Collectors.summingLong(GroupCount::getCount) // 计算每个分组的总和
            ));
        List<Item> items = new ArrayList<>();
        Long mqTotal = dataSourceCategory2Count.getOrDefault(DataSourceCategory.MQ, 0L);
        Long otherTotal = dataSourceCategory2Count.entrySet().stream()
            .filter(entry -> entry.getKey() != DataSourceCategory.MQ)
            .mapToLong(Map.Entry::getValue)
            .sum();
        items.add(Item.createItem("REALTIME", mqTotal, "REALTIME"));
        items.add(Item.createItem("BATCH", otherTotal, "BATCH"));
        return new DashboardResponse(mqTotal + otherTotal, items, 0L);
    }

    /**
     * 获取数据服务的数量
     *
     * @param modelLayer 数据建模分层
     * @return DashboardResponse
     */
    @GetMapping("/{modelLayer}/dashboard/data-service")
    public DashboardResponse dataServiceDashboard(@PathVariable ModelLayer modelLayer) {
        List<GroupCount<String>> groupCounts = dataModelDisplayMapper.selectDataServiceCounts(modelLayer);
        return new DashboardResponse(groupCounts);
    }

    /**
     * 获取贴源/要素表的治理数量
     *
     * @param modelLayer 数据建模分层
     * @return DashboardResponse
     */
    @GetMapping("/{modelLayer}/dashboard/arranged-status")
    public DashboardResponse getArrangedDashboard(@PathVariable ModelLayer modelLayer) {
        List<GroupCount<ArrangedStatus>> groupCounts = dataModelDisplayMapper.selectArrangedCounts(modelLayer);
        List<DashboardResponse.Item> items = new ArrayList<>();
        Long total = groupCounts.stream().mapToLong(groupCount -> {
            DashboardResponse.Item item = new DashboardResponse.Item();
            ArrangedStatus status = groupCount.getValue();
            item.setName(status.getLabel());
            item.setCount(groupCount.getCount());
            item.setValue(status.isBooleanValue());
            items.add(item);
            return groupCount.getCount();
        }).sum();

        return new DashboardResponse(total, items, 0L);
    }

}
