package com.trs.ai.moye.monitor.controller;

import com.trs.ai.moye.monitor.request.DataModelMonitorRequest;
import com.trs.ai.moye.monitor.request.MonitorEventPeriodRequest;
import com.trs.ai.moye.monitor.request.OdsMonitorTrendRequest;
import com.trs.ai.moye.monitor.response.HomePageResponse;
import com.trs.ai.moye.monitor.response.MonitorDetailTableResponse;
import com.trs.ai.moye.monitor.response.MonitorEventPeriodResponse;
import com.trs.ai.moye.monitor.response.MonitorTrendDetailResponse;
import com.trs.ai.moye.monitor.response.MonitorTrendResponse;
import com.trs.ai.moye.monitor.response.OdsMonitorConfigVersionResponse;
import com.trs.ai.moye.monitor.response.OdsMonitorStatisticsResponse;
import com.trs.ai.moye.monitor.response.SourceTypeResponse;
import com.trs.ai.moye.monitor.service.OdsMonitorService;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.data.connection.enums.ConnectionType;
import com.trs.moye.base.data.connection.enums.DataSourceCategory;
import com.trs.moye.base.monitor.enums.MonitorConfigType;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/10/23
 **/
@RestController
@RequestMapping("/monitor/data-model")
public class OdsMonitorController {


    @Resource
    private OdsMonitorService odsMonitorService;

    /**
     * Ods监控首页 <br>
     * <a href=http://192.168.210.40:3001/project/4220/interface/api/163390>YApi</a>
     *
     * @param request 请求参数
     * @return 首页数据
     */
    @PostMapping("/home-page")
    public HomePageResponse odsMonitorHomePage(
        @RequestBody DataModelMonitorRequest request) {
        MonitorConfigType type = request.getMonitorType();
        return odsMonitorService.odsMonitorHomePage(type, request, ModelLayer.ODS);
    }

    /**
     * 贴源库监控统计列表
     * <p>
     * <a href=http://192.168.210.40:3001/project/4220/interface/api/163360>YApi</a>
     *
     * @param request 请求参数
     * @return 首页数据
     */
    @PostMapping("/list")
    public PageResponse<OdsMonitorStatisticsResponse> odsMonitorStatisticsList(
        @RequestBody DataModelMonitorRequest request) {
        return odsMonitorService.odsMonitorStatisticsList(request.getMonitorType(), request, ModelLayer.ODS);
    }


    /**
     * 贴源库监控详情列表
     * <p>
     * <a href=http://192.168.210.40:3001/project/4220/interface/api/163400>YApi</a>
     *
     * @param dataModelId 数据建模id
     * @param request     请求参数
     * @return 首页数据
     */
    @PostMapping("/detail/table/{dataModelId}")
    public PageResponse<MonitorDetailTableResponse> odsMonitorDetailTable(
        @PathVariable Integer dataModelId,
        @RequestBody DataModelMonitorRequest request) {
        return odsMonitorService.odsMonitorDetailTable(request.getMonitorType(), dataModelId, request);
    }

    /**
     * 首页——积压/断流监控——周期列表 <br/>
     * <a href=https://yapi-192.trscd.com.cn/project/5419/interface/api/167833>YApi</a>
     *
     * @param monitorType 监控类型
     * @param dataModelId 数据建模id
     * @param request     请求参数
     * @return 监控详情列表
     */
    @PostMapping("/{monitorType}/detail/periods/{dataModelId}")
    public PageResponse<MonitorEventPeriodResponse> odsMonitorDetailPeriods(
        @PathVariable("monitorType") MonitorConfigType monitorType,
        @PathVariable("dataModelId") Integer dataModelId,
        @RequestBody MonitorEventPeriodRequest request) {
        if (MonitorConfigType.LAG.equals(monitorType) || MonitorConfigType.CUTOFF.equals(monitorType)) {
            return odsMonitorService.odsMonitorDetailPeriods(monitorType, dataModelId, request);
        }
        throw new BizException("监控类型【%s】不提供周期列表",
            Optional.ofNullable(monitorType).map(MonitorConfigType::getLabel).orElse("null"));
    }


    /**
     * 获取贴源库监控配置版本信息
     * <p>
     * <a href=http://192.168.210.40:3001/project/4220/interface/api/163415>YApi</a>
     *
     * @param monitorType 监控类型
     * @param dataModelId 元数据id
     * @return 版本信息
     */
    @GetMapping("/{monitorType}/detail/versions/{dataModelId}")
    public List<OdsMonitorConfigVersionResponse> odsMonitorDetailVersions(@PathVariable String monitorType,
        @PathVariable Integer dataModelId) throws BizException {
        return odsMonitorService.odsMonitorDetailVersions(getMonitorType(monitorType), dataModelId);
    }


    private MonitorConfigType getMonitorType(String monitorType) throws BizException {
        return MonitorConfigType.valueOf(monitorType.toUpperCase());
    }

    /**
     * 贴源库 详情趋势
     * <br>
     * <a href=http://192.168.210.40:3001/project/4220/interface/api/163410>YApi</a>
     *
     * @param monitorType 监控类型
     * @param dataModelId 元数据id
     * @param request     其他筛选条件
     * @return 趋势
     */
    @PostMapping("/{monitorType}/detail/trend/{dataModelId}")
    public List<MonitorTrendResponse> odsMonitorTrend(@PathVariable("monitorType") String monitorType,
        @PathVariable("dataModelId") Integer dataModelId, @RequestBody @Valid OdsMonitorTrendRequest request) {
        MonitorConfigType typeEnum = getMonitorType(monitorType);
        if (Objects.isNull(typeEnum)) {
            throw new BizException("监控类型【%s】是非法值", monitorType);
        }
        if (MonitorConfigType.LAG.equals(typeEnum) || MonitorConfigType.FLUCTUATION.equals(typeEnum)
            || MonitorConfigType.TASK_EXECUTION_TIME.equals(typeEnum)) {
            return odsMonitorService.odsMonitorDetailTrend(typeEnum, dataModelId,
                request.getConfigId(), request.getCount());
        }
        throw new BizException("监控类型【%s】不提供趋势图", monitorType);
    }

    /**
     * 获取数据源类型
     *
     * @return 数据源类型
     */
    @GetMapping("/source-type")
    public List<SourceTypeResponse> getSourceType() {
        return Arrays.stream(DataSourceCategory.values()).map(SourceTypeResponse::new).toList();
    }

    /**
     * 获取数据源子类型
     *
     * @param sourceType 数据源类型
     * @return 数据源子类型
     */
    @GetMapping("/source-sub-type/{sourceType}")
    public List<SourceTypeResponse> getSourceSubType(@PathVariable String sourceType) {
        List<ConnectionType> enums = Arrays.stream(ConnectionType.values())
            .filter(connectionType -> connectionType.getCategory().name().equals(sourceType.toUpperCase())).toList();
        return enums.stream().map(SourceTypeResponse::new).toList();
    }

    /**
     * 获取积压周期
     * <a href=http://192.168.210.40:3001/project/5419/interface/api/167947>YApi</a>
     *
     * @param dataModelId 数据建模id
     * @param request     请求参数
     * @return 积压周期
     */
    @PostMapping("/{dataModelId}/periods")
    public MonitorTrendDetailResponse getLagPeriods(@PathVariable Integer dataModelId,
        @RequestBody DataModelMonitorRequest request) {
        if (MonitorConfigType.CUTOFF.equals(request.getMonitorType())) {
            throw new BizException("监控类型【%s】不提供趋势列表", request.getMonitorType().getLabel());
        }
        return odsMonitorService.dataModelMonitorPeriod(dataModelId, request);
    }
}
