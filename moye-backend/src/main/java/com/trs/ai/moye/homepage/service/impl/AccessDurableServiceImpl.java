package com.trs.ai.moye.homepage.service.impl;

import com.trs.ai.moye.homepage.dao.StorageStatsMapper;
import com.trs.ai.moye.homepage.entity.AggregatedStat;
import com.trs.ai.moye.homepage.entity.BaseStat;
import com.trs.ai.moye.homepage.enums.StatType;
import com.trs.ai.moye.homepage.service.DataCollectService;
import com.trs.ai.moye.homepage.service.impl.collect.AccessDurableService;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/4/14
 **/

@Service
public class AccessDurableServiceImpl implements AccessDurableService {

    @Resource
    private StorageStatsMapper storageStatsMapper;

    @Resource
    List<DataCollectService> dataCollectServiceList;

    @Override
    public void durableAccessStat() {
        // 统计数据
        // 1. 查询上一次的统计时间
        List<AggregatedStat> historyStatList = storageStatsMapper.selectStatList();
        LocalDateTime startTime = storageStatsMapper.selectLastStatTime();
        if (Objects.isNull(historyStatList) || historyStatList.isEmpty()) {
            startTime = null;
        }
        // 2. 查询当前的统计数据
        LocalDateTime endTime = getRoundedUpHour();
        List<AggregatedStat> incrementStats = collectIncrementStats(startTime, endTime);
        // 3. 查询上一次的统计数据
        List<AggregatedStat> newStats = calculate(historyStatList, incrementStats, endTime);
        // 3. 插入统计数据
        if (Objects.isNull(historyStatList) || historyStatList.isEmpty()) {
            storageStatsMapper.insertStats(newStats);
        } else {
            newStats.forEach(this::updateStat);
        }
    }

    private void updateStat(AggregatedStat aggregatedStat) {
        if (Objects.isNull(aggregatedStat)) {
            return;
        }
        if (Objects.nonNull(aggregatedStat.getStatType())
            && aggregatedStat.getStatType().isNeedUpdate()) {
            storageStatsMapper.updateStats(aggregatedStat);
        }

    }

    private List<AggregatedStat> collectIncrementStats(LocalDateTime startTime, LocalDateTime endTime) {

        List<AggregatedStat> stats = new ArrayList<>(dataCollectServiceList.stream().map(
            collector -> collector.getIncrementStat(startTime, endTime)
        ).filter(stat -> Objects.nonNull(stat.getStatType()) && stat.getStatType().isNeedUpdate()).toList());

        BaseStat taskBaseStat = storageStatsMapper.selectTaskStats(startTime, endTime);
        AggregatedStat taskStat = convert(taskBaseStat, StatType.TASK, endTime);
        BaseStat lagBaseStat = storageStatsMapper.selectLagStats(startTime, endTime);
        AggregatedStat lagStat = convert(lagBaseStat, StatType.LAG, endTime);
        BaseStat fluctuationBaseStat = storageStatsMapper.selectFluctuationStats(startTime, endTime);
        AggregatedStat fluctuationStat = convert(fluctuationBaseStat, StatType.FLUCTUATION, endTime);
        BaseStat cutoffBaseStat = storageStatsMapper.selectCutoffStats(startTime, endTime);
        AggregatedStat cutoffStat = convert(cutoffBaseStat, StatType.CUTOFF, endTime);
        stats.addAll(List.of(taskStat, lagStat, fluctuationStat, cutoffStat));
        return stats;
    }

    private AggregatedStat convert(BaseStat baseStat, StatType statType, LocalDateTime statTime) {
        return new AggregatedStat()
            .setStatType(statType)
            .setLastStatTime(statTime)
            .setTotal(baseStat.getTotal())
            .setNormal(baseStat.getNormal())
            .setAbnormal(baseStat.getAbnormal());
    }

    private List<AggregatedStat> calculate(List<AggregatedStat> historyStats,
        List<AggregatedStat> incrementStats,
        LocalDateTime newStatTime) {
        // 阶段1：数据准备（空安全处理）
        final Map<StatType, AggregatedStat> historyMap = createStatMap(historyStats);
        final Map<StatType, AggregatedStat> incrementMap = createStatMap(incrementStats);

        // 阶段2：合并类型集合
        final Set<StatType> mergedTypes = mergeStatTypes(historyMap, incrementMap);

        // 阶段3：类型合并计算
        return mergedTypes.stream()
            .map(type -> mergeSingleStat(type, historyMap.get(type), incrementMap.get(type), newStatTime))
            .toList();
    }

    private Map<StatType, AggregatedStat> createStatMap(List<AggregatedStat> stats) {
        return Optional.ofNullable(stats).orElseGet(ArrayList::new).stream()
            .collect(Collectors.toMap(
                AggregatedStat::getStatType,
                Function.identity(),
                (existing, replacement) -> existing // 保留已有值，处理重复键
            ));
    }

    private Set<StatType> mergeStatTypes(Map<StatType, ?> map1, Map<StatType, ?> map2) {
        return Stream.concat(map1.keySet().stream(), map2.keySet().stream())
            .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    private AggregatedStat mergeSingleStat(StatType type,
        AggregatedStat history,
        AggregatedStat increment,
        LocalDateTime newStatTime) {
        return new AggregatedStat().setStatType(type
        ).setLastStatTime(newStatTime
        ).setTotal(sumValues(history, increment, AggregatedStat::getTotal)
        ).setNormal(sumValues(history, increment, AggregatedStat::getNormal)
        ).setAbnormal(sumValues(history, increment, AggregatedStat::getAbnormal)
        );
    }

    private Long sumValues(AggregatedStat a,
        AggregatedStat b,
        Function<AggregatedStat, Long> valueExtractor) {
        final long valA = getSafeValue(a, valueExtractor);
        final long valB = getSafeValue(b, valueExtractor);
        return valA + valB;
    }

    private long getSafeValue(AggregatedStat stat, Function<AggregatedStat, Long> extractor) {
        return Optional.ofNullable(stat)
            .map(extractor)
            .orElse(0L);
    }


    private static LocalDateTime getRoundedUpHour() {
        LocalDateTime now = LocalDateTime.now();
        return now.truncatedTo(ChronoUnit.HOURS);
    }
}
