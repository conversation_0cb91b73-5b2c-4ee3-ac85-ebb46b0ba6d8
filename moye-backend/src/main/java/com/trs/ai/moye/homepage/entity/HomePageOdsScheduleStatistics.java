package com.trs.ai.moye.homepage.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 贴源库首页统计调度数据
 *
 * <AUTHOR>
 * @since 2025/5/23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HomePageOdsScheduleStatistics {

    /**
     * 数据建模id
     */
    private Integer dataModelId;

    /**
     * 数据建模名字
     */
    private String dataModelName;

    /**
     * 总数量
     */
    private Long totalCount;

    /**
     * 成功数量
     */
    private Long successCount;

    /**
     * 失败数量
     */
    private Long failCount;

    /**
     * 数据更新时间
     */
    private String time;

    /**
     * 同步时间
     */
    private LocalDateTime syncTime;

    /**
     * 接入方式(调度专用)
     */
    private String accessType;

    /**
     * 通过新数据更新当前数据
     *
     * @param newData 新数据
     */
    public void updateByNew(HomePageOdsScheduleStatistics newData) {
        this.failCount = newData.getFailCount();
        this.successCount = newData.getSuccessCount();
        this.totalCount = newData.getTotalCount();
        this.syncTime = newData.getSyncTime();
    }
}
