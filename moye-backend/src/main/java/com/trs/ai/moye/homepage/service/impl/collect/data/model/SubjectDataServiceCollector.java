package com.trs.ai.moye.homepage.service.impl.collect.data.model;

import com.trs.ai.moye.homepage.entity.DashBoardData;
import com.trs.ai.moye.homepage.enums.HomePageShowData;
import com.trs.ai.moye.homepage.enums.StatType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 专题库数据收集service类
 *
 * <AUTHOR>
 * @since 2025/2/24
 **/
@Service
public class SubjectDataServiceCollector extends DataModelDataCollectService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Override
    public List<DashBoardData> collectData() {
        Long count = dataModelMapper.selectCountCreateTableStatus(getLayer());
        return DashBoardData.buildData(HomePageShowData.SUBJECT_LAYER, count,
            getTotal());
    }

    @Override
    public ModelLayer getLayer() {
        return ModelLayer.SUBJECT;
    }

    @Override
    protected StatType getStatType() {
        return StatType.SUBJECT_STORAGE;
    }
}
