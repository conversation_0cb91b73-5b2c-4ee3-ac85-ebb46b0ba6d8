package com.trs.ai.moye.monitor.collector;

import com.trs.ai.moye.data.model.dao.BatchTaskRecordMapper;
import com.trs.ai.moye.data.model.dao.StorageTaskMapper;
import com.trs.ai.moye.monitor.dao.DataProcessRecordMapper;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import java.util.List;
import lombok.Getter;

/**
 * 抽象类
 *
 * <AUTHOR>
 * @param <T> 统计数据类型
 * @since 2025/6/24
 */
@Getter
public abstract class AbstractStatisticsCollector<T> implements StatisticsCollector<T> {

    protected final DataModelMapper dataModelMapper;
    protected final BatchTaskRecordMapper batchTaskRecordMapper;
    protected final DataProcessRecordMapper dataProcessRecordMapper;
    protected final StorageTaskMapper storageTaskMapper;
    protected final ModelLayer modelLayer;
    protected final int storageLayerType;

    public AbstractStatisticsCollector(DataModelMapper dataModelMapper,
        BatchTaskRecordMapper batchTaskRecordMapper,
        DataProcessRecordMapper dataProcessRecordMapper,
        StorageTaskMapper storageTaskMapper,
        ModelLayer modelLayer,
        Integer storageLayerType) {
        this.dataModelMapper = dataModelMapper;
        this.batchTaskRecordMapper = batchTaskRecordMapper;
        this.dataProcessRecordMapper = dataProcessRecordMapper;
        this.storageTaskMapper = storageTaskMapper;
        this.modelLayer = modelLayer;
        this.storageLayerType = storageLayerType;
    }

    @Override
    public List<Integer> selectModelIdsByLayer() {
        return dataModelMapper.selectIdsByLayer(modelLayer);
    }

    @Override
    public void insertAllStatistics(List<Integer> modelIds) {
        // 批处理统计
        List<T> batchStatistics = selectBatchStatistics(null, null);
        List<T> batchStorageStatistics = selectBatchStorageStatistics(null, null);
        insertNewStatistics(batchStatistics);
        insertNewStatistics(batchStorageStatistics);

        // 流处理统计
        List<T> streamStatistics = selectStreamStatistics(null, null, modelIds);
        insertNewStatistics(streamStatistics);

        // 存储统计
        List<T> storageStatistics = selectStorageStatistics(null, null, storageLayerType);
        insertNewStatistics(storageStatistics);
    }
}