package com.trs.ai.moye.homepage.enums;

import lombok.Getter;

/**
 * 数据中台V4.0 首页展示数据(流水线页面), 模块名称枚举，用于记录每个模块的名称
 *
 * <AUTHOR>
 * @since 2025/1/17
 **/
@Getter
public enum HomePageShowData {

    DATA_SOURCE("dataSource"),

    ODS_LAYER("odsLayer"),

    DWD_LAYER("dwdLayer"),

    THEME_LAYER("themeLayer"),

    SUBJECT_LAYER("subjectLayer"),

    DATA_SERVICE("dataService"),

    OPERATOR_FACTORY("operatorFactory");

    HomePageShowData(String moduleName) {
        this.moduleName = moduleName;
    }

    /**
     * 模块名称
     */
    private final String moduleName;



    /**
     * 根据模块名称获取对应的枚举
     *
     * @param moduleName 模块名称
     * @return {@link HomePageShowData}
     */
    public static HomePageShowData getByModuleName(String moduleName) {
        for (HomePageShowData value : HomePageShowData.values()) {
            if (value.getModuleName().equals(moduleName)) {
                return value;
            }
        }
        return null;
    }
}
