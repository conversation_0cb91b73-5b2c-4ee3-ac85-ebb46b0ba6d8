package com.trs.ai.moye.batchengine.feign;

import com.trs.ai.moye.batchengine.entity.BatchEngineTaskParam;
import com.trs.ai.moye.batchengine.entity.ExecuteResultMap;
import com.trs.ai.moye.batchengine.entity.ExecuteResultRequest;
import com.trs.ai.moye.batchengine.response.CodeFormatterResponse;
import com.trs.ai.moye.common.config.OpenFeignConfig;
import com.trs.ai.moye.data.model.entity.BatchTaskTracer;
import com.trs.ai.moye.data.model.request.CodeFormatterRequest;
import com.trs.moye.base.common.response.ResponseMessage;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * batch engine feign service
 *
 * <AUTHOR>
 * @since 2024/11/25 15:55
 */
@FeignClient(name = "moye-batch-engine", path = "/batch-engine", configuration = OpenFeignConfig.class)
public interface BatchEngineFeignService {


    /**
     * 执行代码任务
     *
     * @param tasks 批处理任务传输类
     * <AUTHOR>
     */
    @PostMapping("/task/execute")
    void execute(@RequestBody List<BatchEngineTaskParam> tasks);

    /**
     * 执行代码任务(同步)
     *
     * @param tasks 批处理任务传输类
     * @return ResponseMessage
     * <AUTHOR>
     */
    @PostMapping("/task/execute/sync")
    ResponseMessage executeSync(@RequestBody List<BatchEngineTaskParam> tasks);

    /**
     * 立即执行dag任务
     *
     * @param task 批处理任务传输类
     */
    @PostMapping("/task/dag/execute")
    void dagExecute(@RequestBody BatchEngineTaskParam task);

    /**
     * 测试dag任务
     *
     * @param task 批处理任务传输类
     * @return 任务执行记录
     */
    @PostMapping("/task/dag/test")
    List<BatchTaskTracer> dagTest(@RequestBody BatchEngineTaskParam task);

    /**
     * 立即执行dag任务（同步执行）
     *
     * @param task 批处理任务传输类
     * @return ResponseMessage
     */
    @PostMapping("/task/dag/execute/sync")
    ResponseMessage dagExecuteSync(@RequestBody BatchEngineTaskParam task);

    /**
     * 代码格式化
     *
     * @param vo 代码
     * @return {@link CodeFormatterRequest} 代码传输类
     */
    @PostMapping("/api/code-formatter")
    CodeFormatterResponse formatCode(@RequestBody CodeFormatterRequest vo);

    /**
     * 获取执行结果
     *
     * @param executeResultRequest  参数
     * @return 执行结果
     */
    @PostMapping("/task/execute/result")
    List<ExecuteResultMap> executeResult(@RequestBody ExecuteResultRequest executeResultRequest);

    /**
     * 停止spark任务
     *
     * @param executeId 任务id
     */
    @PostMapping("/task/kill/{executeId}")
    void kill(@PathVariable("executeId") String executeId);

    /**
     * 刷新日志
     *
     * @param executeId 任务id
     */
    @PostMapping("/task/log/flush/{executeId}")
    void flush(@PathVariable("executeId") String executeId);
}
