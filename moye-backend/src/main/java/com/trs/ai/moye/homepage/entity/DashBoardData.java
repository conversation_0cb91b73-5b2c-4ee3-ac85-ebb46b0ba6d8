package com.trs.ai.moye.homepage.entity;

import com.trs.ai.moye.homepage.enums.HomePageShowData;
import com.trs.moye.base.common.utils.SnowflakeIdUtil;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表dashboard_data的实体类
 *
 * <AUTHOR>
 * @since 2025/1/17
 **/

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DashBoardData {

    /**
     * 主键，唯一标识一条记录
     */
    private Long id;
    /**
     * 批次号，每次插入新数据时，批次号递增
     */
    private Long batchId;
    /**
     * 数据类别（如 dataSource, sourceLayer 等）
     */
    private String category;
    /**
     * 指标名称（如 industryCount, totalVolume 等）
     */
    private String metric;
    /**
     * 指标值
     */
    private Long value;
    /**
     * 数据写入时间
     */
    private LocalDateTime createdAt;

    /**
     * 构建数据
     *
     * @param homePageShowData 首页展示数据
     * @param count            业务数量
     * @param total            总数
     * @return {@link List}<{@link DashBoardData}>
     */
    public static List<DashBoardData> buildData(HomePageShowData homePageShowData, Long count, Long total) {
        return List.of(
            new DashBoardData(SnowflakeIdUtil.newId(), null, homePageShowData.getModuleName(),
                DashBoardConstants.COUNT, count, LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS)),
            new DashBoardData(SnowflakeIdUtil.newId(), null, homePageShowData.getModuleName(),
                DashBoardConstants.TOTAL, total, LocalDateTime.now().truncatedTo(ChronoUnit.SECONDS))
        );
    }
}
