package com.trs.ai.moye.homepage.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.ai.moye.homepage.entity.AggregatedStat;
import com.trs.ai.moye.homepage.entity.BaseStat;
import com.trs.ai.moye.homepage.enums.StatType;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 统计数据持久化接口
 */
@Mapper
@DS("clickhouse")
public interface StorageStatsMapper {

    /**
     * 查询单个统计数据
     *
     * @param statType 统计类型
     * @return 统计数据
     */
    BaseStat selectStatsByType(@Param("statType") StatType statType);

    /**
     * 查询所有的统计数据
     *
     * @return 统计数据列表
     */
    List<AggregatedStat> selectStatList();

    /**
     * 查询接入统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 接入数据
     */
    BaseStat selectAccessStats(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 插入统计数据
     *
     * @param stats 统计数据列表
     */
    void insertStats(@Param("stats") List<AggregatedStat> stats);

    /**
     * 查询上一次的统计时间
     *
     * @return 上一次的统计时间
     */
    LocalDateTime selectLastStatTime();

    /**
     * 更新统计数据, 根据统计类型进行更新
     *
     * @param newStat 待更新的数据
     */
    void updateStats(@Param("newStat") AggregatedStat newStat);

    /**
     * 查询统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    BaseStat selectTaskStats(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 查询当日统计数据
     *
     * @param startTime 开始时间
     * @return 统计数据
     */
    BaseStat selectTaskStatsCurrent(@Param("startTime") LocalDateTime startTime);

    /**
     * 查询统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    BaseStat selectLagStats(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    BaseStat selectFluctuationStats(@Param("startTime") LocalDateTime startTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    BaseStat selectCutoffStats(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}