package com.trs.ai.moye.homepage.entity;

import com.trs.ai.moye.homepage.enums.StatisticsDwdType;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 要素库首页统计数据
 *
 * <AUTHOR>
 * @since 2025/5/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HomePageDwdStatistics {

    /**
     * 数据建模id
     */
    private Integer dataModelId;

    /**
     * 数据建模名字
     */
    private String dataModelName;

    /**
     * 总数量
     */
    private Long totalCount;

    /**
     * 成功数量
     */
    private Long successCount;

    /**
     * 失败数量
     */
    private Long failCount;

    /**
     * 数据更新时间
     */
    private String time;

    /**
     * 统计类型
     */
    private StatisticsDwdType type;

    /**
     * 执行的算子数量
     */
    private Integer operatorCount = 0;

    /**
     * 同步时间
     */
    private LocalDateTime syncTime;

    /**
     * 存储点
     */
    private Integer storageId;


    /**
     * 通过新数据更新当前数据
     *
     * @param newData 新数据
     */
    public void updateByNew(HomePageDwdStatistics newData) {
        this.failCount = newData.getFailCount();
        this.operatorCount = newData.getOperatorCount();
        this.successCount = newData.getSuccessCount();
        this.totalCount = newData.getTotalCount();
        this.syncTime = newData.getSyncTime();
        this.storageId = newData.getStorageId();
        this.type = newData.getType();
    }
}
