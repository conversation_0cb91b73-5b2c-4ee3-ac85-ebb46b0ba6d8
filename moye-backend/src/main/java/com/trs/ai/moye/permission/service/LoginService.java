package com.trs.ai.moye.permission.service;

import com.trs.ai.moye.backstage.dao.UserLogMapper;
import com.trs.ai.moye.backstage.dao.UserMapper;
import com.trs.ai.moye.backstage.entity.User;
import com.trs.ai.moye.backstage.entity.UserLog;
import com.trs.ai.moye.backstage.enums.UserType;
import com.trs.ai.moye.backstage.service.UserService;
import com.trs.ai.moye.common.exception.AuthenticationException;
import com.trs.ai.moye.common.utils.IPUtils;
import com.trs.ai.moye.permission.properties.SecurityProperties;
import com.trs.ai.moye.permission.request.UserLoginRequest;
import com.trs.ai.moye.permission.response.LoginResponse;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.utils.DateTimeUtils;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 登录服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class LoginService {

    @Resource
    private UserLogMapper loginLogMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private JwtService jwtService;

    @Resource
    private AuthenticationService authenticationService;

    @Resource
    private SecurityProperties securityProperties;

    @Resource
    private TokenStore tokenStore;

    @Resource
    private BcryptService bcryptService;

    @Resource
    private UserService userService;

    private static final String OMNIPOTENT_CODE = "99CA7BA032913FE436ECB24247B40C2F";


    /**
     * 记录登出信息
     *
     * @param account   用户账户
     * @param loginTime 登录时间
     */
    private void recordLogout(String account, LocalDateTime loginTime) {
        UserLog userLogOptional = loginLogMapper.selectByAccountAndLoginTime(account, loginTime);
        if (Objects.nonNull(userLogOptional)) {
            userLogOptional.setLogoutTime(LocalDateTime.now());
            loginLogMapper.updateById(userLogOptional);
        }
    }

    /**
     * 登录
     *
     * @param userLoginRequest   登录用户名密码
     * @param httpServletRequest {@link HttpServletRequest}
     * @return token
     */
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse login(UserLoginRequest userLoginRequest, HttpServletRequest httpServletRequest) {
        //是否需要验证码
        if (Boolean.TRUE.equals(securityProperties.getEnableCode())) {
            String authCode = userLoginRequest.getCode();
            HttpSession session = httpServletRequest.getSession();
            // 验证验证码,验证码不区分大小写
            if (StringUtils.isNotEmpty(authCode)) {
                verifyCode(authCode, session);
            } else {
                throw new AuthenticationException("请输入验证码！");
            }
        }
        String account = userLoginRequest.getAccount();
        User correctUser = userService.getUserByAccountUpdateCache(account);
        if (Objects.isNull(correctUser)) {
            throw new AuthenticationException("登录失败：用户名或密码错误！");
        }
        //校验用户是否启用
        if (!correctUser.isEnable()) {
            throw new AuthenticationException("用户未启用，请与管理员核实！");
        }
        // 检查是否超过限制登录失败次数
        checkLoginFailedNum(correctUser);
        try {
            //鉴权
            User authenticatedUser = authenticationService.authenticate(userLoginRequest);
            //生成token
            LocalDateTime loginTime = LocalDateTime.now();
            String jwtToken = jwtService.generateToken(authenticatedUser, loginTime);

            //如果不是企业用户，则移除上一次登录的用户token
            if (UserType.ENTERPRISE != correctUser.getType()) {
                tokenStore.removeAllTokenByUser(account);
            }
            tokenStore.saveToken(account, DateTimeUtils.toInstant(loginTime).toEpochMilli(), jwtToken);

            // 将登录信息存入登录日志中
            UserLog userLog = new UserLog();
            userLog.setUserId(correctUser.getId());
            userLog.setLoginTime(loginTime);
            userLog.setIp(IPUtils.getIpAddr(httpServletRequest));
            loginLogMapper.insert(userLog);

            // 重置登录失败次数
            loginFailNumClear(correctUser);

            log.info("用户 {} 登录成功", account);

            LoginResponse response = new LoginResponse();
            response.setId(correctUser.getId());
            response.setAccount(account);
            response.setToken(jwtToken);
            return response;
        } catch (Exception e) {
            loginFailInc(correctUser);
            throw new AuthenticationException("登录失败：" + e.getMessage(), e);
        } finally {
            httpServletRequest.getSession().removeAttribute("rand");
        }
    }

    /**
     * 检查登录是否超过登录失败次数限制，如果超过就不予登录，且锁定用户
     *
     * @param correctUser 当前用户
     * <AUTHOR>
     * @since 2022/11/8 10:31
     */
    private void checkLoginFailedNum(User correctUser) throws BizException {
        if (!Objects.isNull(correctUser)) {
            Integer loginFailNumLimit = securityProperties.getLoginFailNumLimit();
            if (loginFailNumLimit <= correctUser.getLoginFailedNum()) {
                userMapper.updateUserStatus(correctUser.getId());
                throw new AuthenticationException("登录失败超过" + loginFailNumLimit + "次，用户已锁定，请联系管理员！");
            }
        }
    }

    /**
     * 登录失败次数清零
     *
     * @param correctUser 当前用户
     * <AUTHOR>
     * @since 2022/11/8 10:23
     */
    private void loginFailNumClear(User correctUser) {
        if (!Objects.isNull(correctUser)) {
            userMapper.updateLoginFailedNum(correctUser.getId(), 0);
        }
    }

    /**
     * 登录失败次数+1
     *
     * @param correctUser 当前用户
     * <AUTHOR>
     * @since 2022/11/8 10:21
     */
    private void loginFailInc(User correctUser) {
        if (!Objects.isNull(correctUser)) {
            userMapper.updateLoginFailedNum(correctUser.getId(), correctUser.getLoginFailedNum() + 1);
        }
    }

    private void verifyCode(String authCode, HttpSession session) throws BizException {
        Object rand = session.getAttribute("rand");
        if (Objects.isNull(rand)) {
            throw new AuthenticationException("验证码无效！");
        }
        String correctAuthCode = rand.toString();
        if (!OMNIPOTENT_CODE.equalsIgnoreCase(authCode) && !authCode.equalsIgnoreCase(correctAuthCode)) {
            throw new AuthenticationException("验证码错误！");
        }
    }

    /**
     * 登出
     *
     * @param httpServletRequest {@link HttpServletRequest}
     */
    @Transactional(rollbackFor = Exception.class)
    public void doLogout(HttpServletRequest httpServletRequest) {
        String token = JwtService.getJwtToken(httpServletRequest);
        Long loginTime = jwtService.extractLoginTime(token);
        //删除用户对应的所有token
        User user = AuthHelper.getCurrentUser();
        tokenStore.removeTokenByUser(user.getAccount(), loginTime);
        recordLogout(user.getAccount(),
            LocalDateTime.ofInstant(Instant.ofEpochMilli(loginTime), ZoneId.systemDefault()));
        //清除用户缓存
        userService.deleteUserCache(user.getAccount());
        log.info("用户 {} 登出", user.getAccount());
    }

    /**
     * 更新密码，以bcrypt加密
     */
    public void updatePasswordToBcrypt() {
        List<User> users = userMapper.selectAll();
        for (User user : users) {
            //不是bcrypt加密的
            if (!user.getPassword().startsWith("$2a$10")) {
                String encrypted = bcryptService.getBcryptPassword(user.getPassword());
                user.setPassword(encrypted);
                userMapper.updateById(user);
            }
        }
    }

}
