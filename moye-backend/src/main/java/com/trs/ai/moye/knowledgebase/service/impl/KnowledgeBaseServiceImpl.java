package com.trs.ai.moye.knowledgebase.service.impl;

import static com.trs.ai.moye.common.utils.EasyExcelUtils.getHorizontalCellStyleStrategy;
import static com.trs.ai.moye.knowledgebase.constant.Constants.MSG_KNOWLEDGE_BASE_EXIST_EN_NAME;
import static com.trs.ai.moye.knowledgebase.constant.Constants.MSG_KNOWLEDGE_BASE_EXIST_ZH_NAME;
import static com.trs.ai.moye.knowledgebase.constant.Constants.MSG_KNOWLEDGE_BASE_FIELD_EXIST_EN_NAME;
import static com.trs.ai.moye.knowledgebase.constant.Constants.MSG_KNOWLEDGE_BASE_FIELD_EXIST_ZH_NAME;
import static com.trs.ai.moye.knowledgebase.constant.Constants.MSG_KNOWLEDGE_BASE_FIELD_NOT_EXIST;
import static com.trs.ai.moye.knowledgebase.constant.Constants.MSG_KNOWLEDGE_BASE_NOT_EXIST;
import static com.trs.ai.moye.knowledgebase.constant.Constants.NOT_SUPPORT_FUZZY_SEARCH_FIELD_SET;

import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.common.dao.DictMapper;
import com.trs.ai.moye.common.entity.UsageInfoResponse;
import com.trs.ai.moye.common.entity.UsageInfoResponse.UsingObjects;
import com.trs.ai.moye.common.response.DictResponse;
import com.trs.ai.moye.data.ability.dao.AbilityMapper;
import com.trs.ai.moye.data.model.response.KeyValueResponse;
import com.trs.ai.moye.data.standard.dao.DataStandardFieldMapper;
import com.trs.ai.moye.knowledgebase.constant.Constants;
import com.trs.ai.moye.knowledgebase.dao.dynamicrepository.KnowledgeBaseDataMapper;
import com.trs.ai.moye.knowledgebase.listener.ReadDataListener;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseRequest;
import com.trs.ai.moye.knowledgebase.request.KnowledgeBaseUpdateRequest;
import com.trs.ai.moye.knowledgebase.response.KnowledgeBaseListResponse;
import com.trs.ai.moye.knowledgebase.service.KnowledgeBaseService;
import com.trs.moye.ability.LocalAbilityScanner;
import com.trs.moye.ability.entity.Ability;
import com.trs.moye.ability.entity.Schema;
import com.trs.moye.ability.enums.AbilityCategory;
import com.trs.moye.ability.enums.AbilityTestStatus;
import com.trs.moye.ability.enums.AbilityUpdateStatus;
import com.trs.moye.ability.exception.AbilityScanException;
import com.trs.moye.ability.response.TagMarkResponse;
import com.trs.moye.base.common.entity.ModuleEnum;
import com.trs.moye.base.common.entity.TwoTuple;
import com.trs.moye.base.common.enums.FieldType;
import com.trs.moye.base.common.exception.BizException;
import com.trs.moye.base.common.request.BaseRequestParams;
import com.trs.moye.base.common.response.PageResponse;
import com.trs.moye.base.common.utils.AssertUtils;
import com.trs.moye.base.common.utils.DateTimeUtils;
import com.trs.moye.base.common.utils.JsonUtils;
import com.trs.moye.base.common.utils.StringUtils;
import com.trs.moye.base.data.connection.enums.FileExtension;
import com.trs.moye.base.data.model.dao.DataModelFieldMapper;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.dao.OperatorBusinessCategoryMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.model.entity.DataModelField;
import com.trs.moye.base.data.model.entity.OperatorBusinessCategory;
import com.trs.moye.base.data.standard.entity.DataStandardField;
import com.trs.moye.base.knowledgebase.dao.KnowledgeBaseFieldMapper;
import com.trs.moye.base.knowledgebase.dao.KnowledgeBaseMapper;
import com.trs.moye.base.knowledgebase.entity.ImportEntityDataResult;
import com.trs.moye.base.knowledgebase.entity.ImportEntityDataResult.FailReason;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBase;
import com.trs.moye.base.knowledgebase.entity.KnowledgeBaseField;
import com.trs.moye.base.knowledgebase.entity.SqlColumn;
import com.trs.moye.base.knowledgebase.enums.KnowledgeBaseType;
import com.trs.moye.base.knowledgebase.request.KnowledgeBaseFieldRequest;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-21 23:22
 */
@Slf4j
@Service
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    @Resource
    private KnowledgeBaseMapper mapper;

    @Resource
    private DictMapper dictMapper;

    @Resource
    private KnowledgeBaseFieldMapper knowledgeBaseFieldMapper;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private DataModelFieldMapper dataModelFieldMapper;

    @Resource
    private DataStandardFieldMapper dataStandardFieldMapper;

    @Resource
    private KnowledgeBaseDataMapper knowledgeBaseDataMapper;

    @Resource
    private AbilityMapper abilityMapper;

    @Resource
    private OperatorBusinessCategoryMapper categoryMapper;

    private static final String BACKUP_TABLE_SUFFIX = "_backup";

    private static final String TAG_LIB_MARK_ABILITY_EN_NAME = "tagLibMark";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int addKnowledgeBase(KnowledgeBaseRequest request) {
        checkKnowledgeBaseName(request);
        KnowledgeBaseType baseType = request.getType();

        int baseId;
        if (baseType.isNeedPhysicsTable()) {
            baseId = addKnowledgeBaseDbRecordAndPhysicsTable(request);
        } else {
            baseId = addKnowledgeBaseDbRecord(request).getFirst().getId();
        }
        KnowledgeBase base = request.toKnowledgeBase();
        base.setId(baseId);
        // 为标签库更新能力
        updateTagLibMarkAbility(base);
        return baseId;
    }

    private void checkKnowledgeBaseName(KnowledgeBaseRequest request) {
        if (existKnowledgeBaseName(request.getType(), request.getEnName(), false)) {
            throw new BizException(MSG_KNOWLEDGE_BASE_EXIST_EN_NAME, request.getEnName());
        }
        if (existKnowledgeBaseName(request.getType(), request.getZhName(), true)) {
            throw new BizException(MSG_KNOWLEDGE_BASE_EXIST_ZH_NAME, request.getZhName());
        }
    }

    private int addKnowledgeBaseDbRecordAndPhysicsTable(KnowledgeBaseRequest request) {
        KnowledgeBaseType baseType = request.getType();
        String tableName = baseType.buildTableName(request.getEnName());
        if (knowledgeBaseDataMapper.existTable(tableName)) {
            throw new BizException("与知识库同名库表【%s】表已存在", tableName);
        }
        TwoTuple<KnowledgeBase, List<KnowledgeBaseField>> twoTuple = addKnowledgeBaseDbRecord(request);
        addKnowledgeBasePhysicsTable(tableName, twoTuple.getSecond());
        return twoTuple.getFirst().getId();
    }

    private TwoTuple<KnowledgeBase, List<KnowledgeBaseField>> addKnowledgeBaseDbRecord(KnowledgeBaseRequest request) {
        KnowledgeBase base = request.toKnowledgeBase();
        mapper.insert(base);
        List<KnowledgeBaseField> fields = request.toKnowledgeBaseFields(base.getId());
        if (ObjectUtils.isNotEmpty(fields)) {
            knowledgeBaseFieldMapper.insert(fields);
        }
        return new TwoTuple<>(base, fields);
    }

    private void addKnowledgeBasePhysicsTable(String tableName, List<KnowledgeBaseField> fields) {
        List<SqlColumn> sqlColumns = fields.stream().map(SqlColumn::new).toList();
        waitFutureResult(Constants.EXECUTOR.submit(() -> knowledgeBaseDataMapper.createTable(tableName, sqlColumns)));
    }

    private static void waitFutureResult(Future<?> future) {
        try {
            future.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException(e, "等待异步任务执行结果发生中断异常");
        } catch (ExecutionException e) {
            Throwable cause = e.getCause();
            throw new BizException(cause, "异步任务执行发生异常，异常信息：", cause.getMessage());
        }
    }

    @Override
    public boolean existKnowledgeBaseName(KnowledgeBaseType type, String name, boolean isZhName) {
        return mapper.getByTypeName(type.name(), name, isZhName) != null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateKnowledgeBase(Integer baseId, KnowledgeBaseUpdateRequest request) {
        KnowledgeBase oldBase = mapper.selectById(baseId);
        AssertUtils.notEmpty(oldBase, MSG_KNOWLEDGE_BASE_NOT_EXIST, baseId);
        boolean isUpdateZhName = !request.getZhName().equals(oldBase.getZhName());
        if (isUpdateZhName && existKnowledgeBaseName(oldBase.getType(), request.getZhName(), true)) {
            throw new BizException(MSG_KNOWLEDGE_BASE_EXIST_ZH_NAME, request.getZhName());
        }
        String oldEnName = oldBase.getEnName();
        boolean isUpdateEnName = !request.getEnName().equals(oldEnName);
        if (isUpdateEnName && existKnowledgeBaseName(oldBase.getType(), request.getEnName(), false)) {
            throw new BizException(MSG_KNOWLEDGE_BASE_EXIST_EN_NAME, request.getEnName());
        }
        UsageInfoResponse usageInfo = getKnowledgeBaseUsageInfo(baseId);
        if (usageInfo.isUse()) {
            if (isUpdateZhName || isUpdateEnName) {
                throw new BizException(usageInfo.getMessage() + "；不允许修改");
            }
        }
        if (oldBase.getType().isNeedPhysicsTable() && isUpdateEnName) {
            String oldTableName = oldBase.getType().buildTableName(oldEnName);
            String newTableName = oldBase.getType().buildTableName(request.getEnName());
            updatePhysicsTableName(oldTableName, newTableName);
        }
        oldBase.setEnName(request.getEnName());
        oldBase.setZhName(request.getZhName());
        oldBase.setDesc(request.getDesc());
        oldBase.setTags(request.getTags());
        mapper.updateById(oldBase);
        // 为标签库更新能力
        updateTagLibMarkAbility(oldBase);
    }

    private boolean isUpdateDesc(String oldDesc, String newDesc) {
        return !org.apache.commons.lang3.StringUtils.equals(oldDesc, newDesc);
    }

    private boolean isUpdateTags(List<String> oldTags, List<String> newTags) {
        if (oldTags == null && newTags == null) {
            return false;
        }
        if (oldTags == null || newTags == null) {
            return true;
        }
        if (oldTags.size() != newTags.size()) {
            return true;
        }
        for (String tag : oldTags) {
            if (!newTags.contains(tag)) {
                return true;
            }
        }
        return false;
    }

    private void updatePhysicsTableName(String oldTableName, String newTableName) {
        waitFutureResult(
            Constants.EXECUTOR.submit(() -> knowledgeBaseDataMapper.changeTableName(oldTableName, newTableName)));
    }

    @Override
    public UsageInfoResponse getKnowledgeBaseUsageInfo(Integer baseId) {
        KnowledgeBase base = mapper.selectById(baseId);
        AssertUtils.notEmpty(base, MSG_KNOWLEDGE_BASE_NOT_EXIST, baseId);
        List<UsingObjects> usageDetailList = new ArrayList<>();
        addStandardFieldUsageInfo(base, usageDetailList);
        addModelFieldUsageInfo(base, usageDetailList);
        return new UsageInfoResponse(usageDetailList, base.getZhName(), base.getType().getUsageInfoModuleEnum());
    }

    private void addStandardFieldUsageInfo(KnowledgeBase base, List<UsingObjects> usageDetailList) {
        List<DataStandardField> standardFields = dataStandardFieldMapper.selectByTypeTypeName(base.getType().name(),
            base.getZhName());
        if (ObjectUtils.isEmpty(standardFields)) {
            return;
        }
        UsingObjects usingObjects = new UsingObjects();
        usingObjects.setType(ModuleEnum.DATA_STANDARD);
        usingObjects.setObjects(
            standardFields.stream().map(field -> new KeyValueResponse(field.getId(), field.getZhName())).toList());
        usageDetailList.add(usingObjects);
    }

    private void addModelFieldUsageInfo(KnowledgeBase base, List<UsingObjects> usageDetailList) {
        List<DataModelField> modelFields = dataModelFieldMapper.selectByTypeTypeName(base.getType().name(),
            base.getZhName());
        if (ObjectUtils.isEmpty(modelFields)) {
            return;
        }
        List<DataModel> modelList = dataModelMapper.selectByIds(
            modelFields.stream().map(DataModelField::getDataModelId).distinct().toList());
        UsingObjects usingObjects = new UsingObjects();
        usingObjects.setType(ModuleEnum.DATA_MODELING);
        usingObjects.setObjects(
            modelList.stream().map(model -> new KeyValueResponse(model.getId(), model.getZhName())).toList());
        usageDetailList.add(usingObjects);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteKnowledgeBase(Integer baseId) {
        UsageInfoResponse usageInfo = getKnowledgeBaseUsageInfo(baseId);
        if (usageInfo.isUse()) {
            throw new BizException(usageInfo.getMessage() + "；不允许删除");
        }
        KnowledgeBase base = mapper.selectById(baseId);
        if (base.getType().isNeedPhysicsTable()) {
            String tableName = base.buildTableName();
            dropPhysicsTable(tableName);
        }
        knowledgeBaseFieldMapper.deleteByBaseId(baseId);
        mapper.deleteById(baseId);
    }

    private void dropPhysicsTable(String tableName) {
        waitFutureResult(Constants.EXECUTOR.submit(() -> knowledgeBaseDataMapper.dropTable(tableName)));
    }

    @Override
    public List<KnowledgeBaseListResponse> getKnowledgeBaseList(KnowledgeBaseType type) {
        return mapper.listByType(type.name()).stream().map(KnowledgeBaseListResponse::new).toList();
    }

    @Override
    public List<KnowledgeBaseListResponse> getKnowledgeBaseList() {
        return mapper.selectAll().stream().map(KnowledgeBaseListResponse::new).toList();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int addField(Integer baseId, KnowledgeBaseFieldRequest request) {
        KnowledgeBase base = mapper.selectById(baseId);
        AssertUtils.notEmpty(base, MSG_KNOWLEDGE_BASE_NOT_EXIST, baseId);
        if (existFieldName(baseId, request.getEnName(), false)) {
            throw new BizException(MSG_KNOWLEDGE_BASE_FIELD_EXIST_EN_NAME, base.getZhName(), request.getEnName());
        }
        if (existFieldName(baseId, request.getZhName(), true)) {
            throw new BizException(MSG_KNOWLEDGE_BASE_FIELD_EXIST_ZH_NAME, base.getZhName(), request.getZhName());
        }
        KnowledgeBaseField field = new KnowledgeBaseField(baseId, request);
        Integer maxBaseShowOrder = knowledgeBaseFieldMapper.maxBaseShowOrder(baseId);
        field.setShowOrder(maxBaseShowOrder == null ? 0 : maxBaseShowOrder + 1);
        if (base.getType().isNeedPhysicsTable()) {
            String tableName = base.buildTableName();
            addPhysicsTableColumn(tableName, field);
        }
        knowledgeBaseFieldMapper.insert(field);

        // 为标签库更新能力
        updateTagLibMarkAbility(base);

        return field.getId();
    }


    @Override
    public void updateTagLibMarkAbility(KnowledgeBase base) {
        if (!KnowledgeBaseType.TAG.equals(base.getType())) {
            return;
        }
        String abilityEnName = base.getEnName() + "_" + TAG_LIB_MARK_ABILITY_EN_NAME;
        String abilityZhName = "标签库打标-" + base.getZhName();
        // 查找现有能力
        Ability ability = abilityMapper.selectByEnName(abilityEnName);
        // 构建标签库输出Schema
        List<KnowledgeBaseField> fields = knowledgeBaseFieldMapper.listByBaseId(base.getId());
        Schema.ObjectTypeSchema outputSchema = TagMarkResponse.buildTagLibOutputSchema(base, fields);

        // 构建 details 字段
        String details = buildTagLibMarkAbilityDetails(base, fields);

        if (Objects.isNull(ability)) {
            // 如果能力不存在，通过扫描器创建新能力
            // 尝试从 EntityQueryAbility 类中获取能力定义
            ability = getLocalTagLibMarkAbility();
            ability.setOutputSchema(outputSchema);

            // 设置能力分类
            handleAbilityCategory(ability);
            ability.setEnName(abilityEnName);
            ability.setZhName(abilityZhName);
            ability.setIconName(AbilityCategory.GENERAL.getIconName());
            abilityMapper.insert(ability);
            log.info("创建标签库打标能力成功，标签库ID: {}, 标签库名称: {}", base.getId(), base.getZhName());
        } else {
            Ability localAbility = getLocalTagLibMarkAbility();
            // 能力已存在，更新能力信息
            if (Objects.nonNull(localAbility)) {
                ability.setInputSchema(localAbility.getInputSchema());
            }
            ability.setOutputSchema(outputSchema);
            ability.setUpdateStatus(AbilityUpdateStatus.UPDATED);
            ability.setTestStatus(AbilityTestStatus.UNTESTED);
            ability.setDetails(details);
            // 检查并设置分类
            if (Objects.isNull(ability.getOperatorCategoryId())) {
                handleAbilityCategory(ability);
            }
            abilityMapper.updateById(ability);
            log.info("更新标签库打标能力成功，标签库ID: {}, 标签库名称: {}", base.getId(), base.getZhName());
        }
    }

    private Ability getLocalTagLibMarkAbility() {
        Ability ability;
        try {
            Class<?> abilityClass = Class.forName("com.trs.moye.ability.base.EntityQueryAbility");
            ability = LocalAbilityScanner.getAbility(abilityClass, TAG_LIB_MARK_ABILITY_EN_NAME);
        } catch (ClassNotFoundException | AbilityScanException e) {
            // 如果获取失败，回退到手动创建能力
            log.warn("无法通过扫描器获取标签库打标能力", e);
            throw new BizException("无法通过扫描器获取标签库打标能力,创建标签库打标能力失败", e);
        }
        return ability;
    }

    /**
     * 处理能力分类
     *
     * @param ability 能力对象
     */
    private void handleAbilityCategory(Ability ability) {
        // 查询现有分类
        OperatorBusinessCategory existingCategory = categoryMapper.selectByEnAbbr(ability.getCategoryEnAbbr());
        if (existingCategory != null) {
            // 使用现有分类ID
            ability.setOperatorCategoryId(existingCategory.getId());
            log.info("使用现有分类: {}", existingCategory.getZhName());
        } else {
            // 使用默认分类
            ability.setOperatorCategoryId(AbilityCategory.GENERAL.getId());
            log.info("使用默认分类: {}", AbilityCategory.GENERAL.getName());
        }
        // 默认使用通用算子图标
        ability.setIconName(AbilityCategory.GENERAL.getIconName());
    }

    private void addPhysicsTableColumn(String tableName, KnowledgeBaseField field) {
        waitFutureResult(Constants.EXECUTOR.submit(
            () -> knowledgeBaseDataMapper.addTableColumn(tableName, List.of(new SqlColumn(field)))));
    }

    @Override
    public boolean existFieldName(Integer baseId, String name, boolean isZhName) {
        return knowledgeBaseFieldMapper.getBaseFieldByName(baseId, name, isZhName) != null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateField(Integer fieldId, KnowledgeBaseFieldRequest request) {
        KnowledgeBaseField oldField = knowledgeBaseFieldMapper.selectById(fieldId);
        AssertUtils.notEmpty(oldField, MSG_KNOWLEDGE_BASE_FIELD_NOT_EXIST, fieldId);
        KnowledgeBase base = mapper.selectById(oldField.getBaseId());
        if (!request.getZhName().equals(oldField.getZhName()) && existFieldName(base.getId(), request.getZhName(),
            true)) {
            throw new BizException(MSG_KNOWLEDGE_BASE_FIELD_EXIST_ZH_NAME, base.getZhName(), request.getZhName());
        }
        String oldEnName = oldField.getEnName();
        boolean isUpdateEnName = !request.getEnName().equals(oldEnName);
        if (isUpdateEnName && existFieldName(base.getId(), request.getEnName(), false)) {
            throw new BizException(MSG_KNOWLEDGE_BASE_FIELD_EXIST_EN_NAME, base.getZhName(), request.getEnName());
        }
        KnowledgeBaseField field = new KnowledgeBaseField(base.getId(), request);
        if (base.getType().isNeedPhysicsTable() && isUpdateEnName) {
            String tableName = base.buildTableName();
            changePhysicsTableColumn(tableName, oldEnName, field);
        }
        field.setId(oldField.getId());
        knowledgeBaseFieldMapper.updateById(field);
        // 为标签库更新能力
        updateTagLibMarkAbility(base);
    }

    private void changePhysicsTableColumn(String tableName, String oldFieldEnName, KnowledgeBaseField field) {
        waitFutureResult(Constants.EXECUTOR.submit(
            () -> knowledgeBaseDataMapper.changeTableColumn(tableName, oldFieldEnName, new SqlColumn(field))));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteField(Integer fieldId) {
        KnowledgeBaseField field = knowledgeBaseFieldMapper.selectById(fieldId);
        AssertUtils.notEmpty(field, MSG_KNOWLEDGE_BASE_FIELD_NOT_EXIST, fieldId);
        KnowledgeBase base = mapper.selectById(field.getBaseId());
        if (base.getType().isNeedPhysicsTable()) {
            String tableName = base.buildTableName();
            deletePhysicsTableColumn(tableName, field);
        }
        knowledgeBaseFieldMapper.deleteById(fieldId);
        // 为标签库更新能力
        updateTagLibMarkAbility(base);
        log.warn("删除知识库字段：{}", JsonUtils.toJsonString(field));
    }

    private void deletePhysicsTableColumn(String tableName, KnowledgeBaseField field) {
        waitFutureResult(Constants.EXECUTOR.submit(
            () -> knowledgeBaseDataMapper.deleteTableColumns(tableName, List.of(field.getEnName()))));
    }

    @Override
    public void updateFieldShowOrder(Integer baseId, List<Integer> fieldIds) {
        KnowledgeBase base = mapper.selectById(baseId);
        AssertUtils.notEmpty(base, MSG_KNOWLEDGE_BASE_NOT_EXIST, baseId);
        knowledgeBaseFieldMapper.updateShowOrder(baseId, fieldIds);
    }

    @Override
    public PageResponse<KnowledgeBaseField> getFieldPageList(Integer baseId, BaseRequestParams request) {
        Page<KnowledgeBaseField> page = knowledgeBaseFieldMapper.getFieldPageList(baseId, request.getSearchParams(),
            request.getSortParams(), request.getPageParams().toPage());
        return PageResponse.of(page);
    }

    @Override
    public List<KnowledgeBaseField> getSupportFuzzySearchFieldList(Integer baseId) {
        return knowledgeBaseFieldMapper.listByBaseId(baseId).stream()
            .filter(field -> !NOT_SUPPORT_FUZZY_SEARCH_FIELD_SET.contains(field.getType())).toList();
    }

    @Override
    public List<KnowledgeBaseField> getFieldList(Integer baseId) {
        return knowledgeBaseFieldMapper.listByBaseId(baseId);
    }

    @Override
    public List<DictResponse> getKnowledgeBaseDictList(Collection<KnowledgeBaseType> baseTypes) {
        return dictMapper.getKnowledgeBaseDictList(baseTypes);
    }

    @Override
    public ImportEntityDataResult importKnowledgeBaseDataList(Integer baseId, MultipartFile multipartFile)
        throws IOException {
        final String fileType = FileExtension.XLSX.getExtension();
        String name = multipartFile.getOriginalFilename();
        assert name != null;
        ImportEntityDataResult dataResult = new ImportEntityDataResult();
        dataResult.setSuccessData(new ArrayList<>());
        dataResult.setFailData(new ArrayList<>());
        if (name.endsWith(fileType)) {
            log.info("开始解析Excel：");
            readExcelAndStore(multipartFile.getInputStream(), baseId, dataResult);
            log.info("实体数据导入成功！");
        } else {
            throw new BizException("导入失败，文件名格式不正确！");
        }
        return dataResult;
    }

    private void readExcelAndStore(InputStream inputStream, Integer baseId, ImportEntityDataResult dataResult) {
        ReadDataListener readDataListener = new ReadDataListener();
        //查询出基础信息
        KnowledgeBase knowledgeBase = mapper.selectByPrimaryKey(baseId);

        //找到需要插入的表名
        String tableName = knowledgeBase.buildTableName();

        //查询出这个表有哪些字段，需要和Excel的表头对应
        //注意这里 后端没有维护每个表的ID字段，这个需要手动拼，
        //其次模版导出的时候熟悉使用的是中文名，插入的时候找到对应的英文名
        List<KnowledgeBaseField> knowledgeBaseFields = new LinkedList<>();
        knowledgeBaseFields.add(new KnowledgeBaseField("id", "id", FieldType.INT));
        knowledgeBaseFields.addAll(knowledgeBaseFieldMapper.listByBaseId(baseId));

        // 将knowledgeBaseFields列表转换为有序Map，键为中文名，值为英文名
        Map<String, String> titleMap = knowledgeBaseFields.stream()
            .collect(Collectors.toMap(KnowledgeBaseField::getZhName,  // 中文名作为键
                KnowledgeBaseField::getEnName,  // 英文名作为值
                (oldValue, newValue) -> oldValue,    // 如果存在重复键，保留旧值
                LinkedHashMap::new                   // 使用LinkedHashMap保留插入顺序
            ));

        List<List<String>> readResult = new ArrayList<>();
        List<Map<Integer, String>> readSync = EasyExcelFactory.read(inputStream, readDataListener).sheet().doReadSync();
        readSync.forEach(m -> {
            List<String> values = new ArrayList<>(m.values());
            readResult.add(values);
        });
        List<String> tableZhTitles = ReadDataListener.checkHead();

        //校验表头
        checkTitle(tableZhTitles, knowledgeBaseFields);

        LinkedList<String> tableEnTitles = new LinkedList<>();

        //遍历中文的表头获取英文表头并校验是否对应
        for (String tableZhTitle : tableZhTitles) {
            //但凡有一个不存在就代表表头字段不对应
            String enName = titleMap.get(tableZhTitle);
            if (Objects.isNull(enName) || StringUtils.isEmpty(enName.trim())) {
                throw new BizException("导入失败，Excel表头" + tableZhTitle + "与知识库字段不匹配！");
            }
            tableEnTitles.add(enName);
        }

        List<List<Object>> results = new ArrayList<>();
        //校验数据合法性
        for (List<String> data : readResult) {
            List<Object> uploadData = castList(data, Object.class);
            try {
                //数据个数和表头要一样多
                checkUploadDataSize(tableEnTitles.size(), uploadData);
                //数据类型校验
                checkUploadDataType(knowledgeBaseFields, uploadData);
                dataResult.getSuccessData().add(data);
                results.add(uploadData);
            } catch (Exception e) {
                FailReason failReason = new FailReason(data, e.getMessage());
                dataResult.getFailData().add(failReason);
            }

        }

        if (!results.isEmpty()) {
            //先备份
            backupData(baseId, tableName);
            //插入
            knowledgeBaseDataMapper.insertEntityCommon(results, tableEnTitles, tableName);
        }

    }

    private void backupData(Integer baseId, String tableName) {
        log.info("创建备份：");
        String backupsTableName = tableName + BACKUP_TABLE_SUFFIX;
        knowledgeBaseDataMapper.dropTable(backupsTableName);
        knowledgeBaseDataMapper.copyTable(tableName, backupsTableName);
        mapper.updateBackupInfo(baseId, 1, LocalDateTime.now());
        log.info("创建备份成功！");
    }

    private <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return Collections.emptyList();
    }


    private void checkUploadDataType(List<KnowledgeBaseField> knowledgeBaseFields, List<Object> uploadData)
        throws BizException {
        List<String> collect = uploadData.stream().map(data -> Objects.isNull(data) ? null : String.valueOf(data))
            .toList();
        List<FieldType> types = knowledgeBaseFields.stream().map(KnowledgeBaseField::getType).toList();
        for (int index = 0; index < types.size(); index++) {
            String fieldName = knowledgeBaseFields.get(index).getZhName();
            String value = collect.get(index);
            FieldType fieldType = types.get(index);
            if (Objects.nonNull(value) && StringUtils.isNotEmpty(value)) {
                switch (Objects.requireNonNull(fieldType)) {
                    case INT, LONG -> checkInt(value, fieldName);
                    case FLOAT, DOUBLE -> checkFloat(value, fieldName);
                    case DATETIME, DATE -> convertToDate(uploadData, index, value, fieldName);
                    case BOOLEAN -> checkBoolean(uploadData, index, value);
                    case CHAR -> checkChar(value, fieldName);
                    default -> {
                        //ignore
                    }
                }
            }
        }
    }

    private void checkChar(String value, String fieldName) {
        if (value.length() != 1) {
            throw new BizException(String.format(
                "导入失败，文件中数据不符合数据库属性要求数据格式!（请检查字段为 %s 的属性数据类型是否匹配）", fieldName));
        }
    }

    private void checkBoolean(List<Object> uploadData, int index, String value) {
        boolean date = Objects.nonNull(value) && "是".equals(value);
        uploadData.set(index, date);
    }

    private void checkInt(String value, String fieldName) throws BizException {
        if (!StringUtils.isNumeric(value)) {
            throw new BizException(
                String.format("导入失败，文件中数据不符合数据库属性要求数据格式!（请检查字段为 %s 的属性数据类型）",
                    fieldName));
        }
    }

    private void checkFloat(String value, String fieldName) throws BizException {
        if (!StringUtils.isFloatOrDouble(value)) {
            throw new BizException(
                String.format("导入失败，文件中数据不符合数据库属性要求数据格式!（请检查字段为 %s 的属性数据类型）",
                    fieldName));
        }
    }

    private void convertToDate(List<Object> uploadData, int index, String value, String fieldName) throws BizException {
        try {
            if (value.length() == 10) {
                LocalDate date = DateTimeUtils.parseLocalDate(value);
                uploadData.set(index, date);
            } else {
                LocalDateTime date = DateTimeUtils.parse(value);
                uploadData.set(index, date);
            }
        } catch (Exception e) {
            throw new BizException(String.format(
                "导入失败，文件中数据不符合数据库属性要求数据格式!（请检查字段 %s 的属性数据，建议设置单元格格式为文本，例：yyyy-MM-dd HH:mm:ss 或者yyyy-MM-dd）",
                fieldName));
        }

    }


    private void checkUploadDataSize(int dbFieldsSize, List<Object> uploadData) throws BizException {
        if (dbFieldsSize < uploadData.size()) {
            throw new BizException("导入失败，表头和数据不对应（某行数据内容多于表头）！");
        } else {
            int additionalSize = dbFieldsSize - uploadData.size();
            for (int index = 0; index < additionalSize; index++) {
                uploadData.add(null);
            }
        }
    }


    @Override
    public void exportKnowledgeBaseDataList(Integer baseId, HttpServletResponse response) throws IOException {
        if (Objects.isNull(baseId)) {
            throw new BizException("知识库id不能为空！");
        }
        KnowledgeBase knowledgeBase = mapper.selectByPrimaryKey(baseId);

        List<KnowledgeBaseField> knowledgeBaseFields = new LinkedList<>();
        knowledgeBaseFields.add(new KnowledgeBaseField("id", "id", FieldType.INT));
        knowledgeBaseFields.addAll(knowledgeBaseFieldMapper.listByBaseId(baseId));

        String tableName = knowledgeBase.buildTableName();
        //获取表头
        List<List<String>> headers = getHeaders(knowledgeBaseFields);
        //通过表名查询到所有数据
        List<Map<String, Object>> dataMap = knowledgeBaseDataMapper.selectTableData(tableName);
        // 设置文件名
        String fileName = tableName + "." + FileExtension.XLSX.getExtension();
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);

        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-disposition", "attachment; filename=" + encodedFileName);

        ServletOutputStream outputStream = response.getOutputStream();

        List<List<Object>> result = new ArrayList<>(dataMap.size());

        for (Map<String, Object> temp : dataMap) {
            List<Object> list = new ArrayList<>();
            for (KnowledgeBaseField knowledgeBaseField : knowledgeBaseFields) {
                Object o = temp.getOrDefault(knowledgeBaseField.getEnName(), "");
                //如果是布尔类型的要转成是和否
                if (knowledgeBaseField.getType().equals(FieldType.BOOLEAN)) {
                    list.add(Boolean.TRUE.equals(o) ? "是" : "否");
                } else {
                    list.add(String.valueOf(o));
                }
            }
            result.add(list);
        }
        try {
            EasyExcelFactory.write(outputStream).head(headers).registerWriteHandler(getHorizontalCellStyleStrategy())
                .autoCloseStream(Boolean.FALSE).sheet().doWrite(result);
        } finally {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        }

    }


    private void checkTitle(List<String> titles, List<KnowledgeBaseField> knowledgeBaseFields) throws BizException {
        if (Objects.isNull(titles) || titles.isEmpty()) {
            throw new BizException("导入失败，文件头不能为空!");

        }
        String oneTitle = titles.get(0);
        if (!oneTitle.equals("id")) {
            throw new BizException("导入失败，文件头第一列必须为id！");
        }
        if (titles.contains("")) {
            throw new BizException("导入失败，文件头不能有空格表头！");
        }

        List<String> entityAttributeMetaName = knowledgeBaseFields.stream().map(KnowledgeBaseField::getZhName).toList();
        if (!compare(titles, entityAttributeMetaName)) {
            throw new BizException(
                String.format("导入失败，文件中属性与当前资源库属性个数不符!文件表头属性个数%s,当前资源库属性个数%s",
                    titles.size(), entityAttributeMetaName.size()));
        }
    }


    private <T extends Comparable<T>> boolean compare(List<T> a, List<T> b) {
        return a.size() == b.size();
    }

    @Override
    public void restoreKnowledgeBaseDataBackup(Integer baseId) {
        KnowledgeBase knowledgeBase = mapper.selectById(baseId);
        String tableName = knowledgeBase.buildTableName();
        String backupsTableName = tableName + BACKUP_TABLE_SUFFIX;
        //先判断是否有备份
        if (knowledgeBase.isBackup()) {
            knowledgeBaseDataMapper.dropTable(tableName);
            knowledgeBaseDataMapper.copyTable(backupsTableName, tableName);
            mapper.updateBackupInfo(baseId, 0, knowledgeBase.getRecentBackupTime());
        } else {
            throw new BizException("备份文件不存在！");
        }
    }

    @Override
    public void excelTemplateDownload(Integer baseId, HttpServletResponse response) throws IOException {
        if (Objects.isNull(baseId)) {
            throw new BizException("知识库id不能为空！");
        }
        KnowledgeBase knowledgeBase = mapper.selectByPrimaryKey(baseId);
        List<KnowledgeBaseField> knowledgeBaseFields = new LinkedList<>();
        knowledgeBaseFields.add(new KnowledgeBaseField("id", "id", FieldType.INT));
        knowledgeBaseFields.addAll(knowledgeBaseFieldMapper.listByBaseId(baseId));

        if (Objects.isNull(knowledgeBase)) {
            throw new BizException("该知识库不存在!");
        }
        String fileName = knowledgeBase.getZhName() + "." + FileExtension.XLSX.getExtension();
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment; filename=" + encodedFileName);
        List<List<String>> headers = getHeaders(knowledgeBaseFields);
        ServletOutputStream outputStream = response.getOutputStream();
        try {
            EasyExcelFactory.write(response.getOutputStream()).head(headers)
                // 样式
                .registerWriteHandler(getHorizontalCellStyleStrategy()).autoCloseStream(Boolean.FALSE).sheet()
                .doWrite((Collection<?>) null);
        } finally {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        }


    }

    private static List<List<String>> getHeaders(List<KnowledgeBaseField> knowledgeBaseFields) {
        return knowledgeBaseFields.stream().map(field -> List.of(field.getZhName())).toList();
    }

    /**
     * 构建标签库打标能力详情
     *
     * @param base   标签库
     * @param fields 标签库字段
     * @return 详情
     */
    private String buildTagLibMarkAbilityDetails(KnowledgeBase base, List<KnowledgeBaseField> fields) {
        String fullTitle = "标签库打标-" + base.getZhName();
        StringBuilder sb = new StringBuilder();
        sb.append("## ").append(fullTitle).append("\n\n");
        sb.append("### 算子类型\n");
        sb.append("通用算子\n\n");
        sb.append("### 算子描述\n");
        String operatorDesc =
            StringUtils.isNotEmpty(base.getDesc()) ? base.getDesc()
                : "根据指定标签库，对输入文本进行关键词匹配打标，返回匹配到的标签及关键词出现次数。";
        sb.append(operatorDesc).append("\n\n");
        sb.append("### 输入参数说明\n\n");
        sb.append("* **标签库英文名**\n");
        sb.append("    * 英文名: `tagLibEnName`\n");
        sb.append("    * 类型: `STRING`\n");
        sb.append("    * 描述: 标签库的英文名称\n");
        sb.append("    * 是否必填：是\n\n");
        sb.append("* **输入文本**\n");
        sb.append("    * 英文名: `inputText`\n");
        sb.append("    * 类型: `STRING`\n");
        sb.append("    * 描述: 需要进行打标的文本内容\n");
        sb.append("    * 是否必填：是\n\n");
        sb.append("### 输出参数说明\n\n");
        sb.append("#### 匹配标签对象字段\n\n");
        sb.append("| 字段名 | 英文名 | 类型 | 描述 |\n");
        sb.append("| --- | --- | --- | --- |\n");
        for (KnowledgeBaseField field : fields) {
            sb.append("| ")
                .append(field.getZhName()).append(" | ")
                .append(field.getEnName()).append(" | ")
                .append(field.getType()).append(" | ")
                .append(field.getDescription() == null ? "" : field.getDescription())
                .append(" |\n");
        }
        sb.append("\n");
        sb.append("#### 关键词匹配详情\n\n");
        sb.append("| 字段名 | 类型 | 描述 |\n");
        sb.append("| --- | --- | --- |\n");
        sb.append("| keyword | STRING | 匹配到的关键词 |\n");
        sb.append("| count | INT | 关键词在文本中出现的次数 |\n\n");
        sb.append("### 示例场景\n\n");
        sb.append("#### 场景1：文本包含多个标签关键词\n\n");
        sb.append("##### 输入参数\n\n");
        sb.append("| 参数 | 值 | 描述 |\n");
        sb.append("| --- | --- | --- |\n");
        sb.append("| tagLibEnName | ").append(base.getEnName()).append(" | 标签库英文名 |\n");
        sb.append("| inputText | 华为发布新AI芯片，昇腾性能强大 | 输入文本 |\n\n");
        sb.append("##### 输出结果\n\n");
        sb.append("| 参数 | 值 | 描述 |\n");
        sb.append("| --- | --- | --- |\n");
        sb.append("| result | [标签对象1, 标签对象2] | 匹配到的标签对象列表 |\n");
        sb.append(
            "| matchDetails | [{\"keyword\": \"AI芯片\", \"count\": 1}, {\"keyword\": \"昇腾\", \"count\": 1}] | 匹配到的关键词及出现次数 |\n\n");
        sb.append("### 算子适用场景简要说明\n\n");
        sb.append("适用于对文本内容进行标签打标、文本分类、关键词提取等场景。\n\n");
        sb.append("### 算子支持依赖\n\n");
        sb.append("无特殊依赖\n");
        return sb.toString();
    }
}
