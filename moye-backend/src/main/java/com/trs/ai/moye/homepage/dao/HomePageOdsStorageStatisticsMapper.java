package com.trs.ai.moye.homepage.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.data.model.response.DataLineageCountVO;
import com.trs.ai.moye.homepage.entity.HomePageOdsStorageStatistics;
import com.trs.ai.moye.monitor.entity.AccessTaskUnit;
import com.trs.ai.moye.monitor.response.statistics.DataStatisticsResponse;
import com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics;
import com.trs.moye.base.common.request.SortParams;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 贴源库库首页存储统计mapper
 *
 * <AUTHOR>
 * @since 2025/5/8
 */
@DS("clickhouse")
@Mapper
public interface HomePageOdsStorageStatisticsMapper extends BaseMapper<HomePageOdsStorageStatistics> {

    /**
     * 查询统计表中所有数据
     *
     * @param type         统计表类型
     * @param dataModelIds 数据模型ID
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 统计表数据
     */
    List<HomePageOdsStorageStatistics> selectAll(@Param("type") String type,
        @Param("dataModelIds") List<Integer> dataModelIds,
        @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 最后一次同步时间
     *
     * @return 最后一次同步时间
     */
    LocalDateTime selectMaxSyncTime();


    /**
     * 更新数据
     *
     * @param data 数据
     */
    void updateData(@Param("originalData") HomePageOdsStorageStatistics data);

    /**
     * 查询总数
     *
     * @return 总数
     */
    DataStatisticsResponse selectTotalCount();

    /**
     * 查询今日总数
     *
     * @return 总数
     */
    DataStatisticsResponse selectTotalCountToday();

    /**
     * 统计日 折线图
     *
     * @param taskId    建模id
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 折线图
     */
    List<ProcessSegmentationStatistics> selectCountByDay(@Param("taskId") Integer taskId,
        @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 统计该时间段内接入量
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param accessStatus 接入状态
     * @return 统计数量
     */
    Long getTotalCountByTimeParams(@Param("startTime") String startTime, @Param("endTime") String endTime,
        @Param("accessStatus") Boolean accessStatus);

    /**
     * 获取具体的数据接入详情列表
     *
     * @param page       分页参数
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param errorFlag  是否异常
     * @param sortParams 排序参数
     * @param keyword    关键字
     * @return 列表
     * @since 2025/05/25
     */
    Page<AccessTaskUnit> getPage(@Param("page") Page<AccessTaskUnit> page, @Param("startTime") String startTime,
        @Param("endTime") String endTime, @Param("errorFlag") Boolean errorFlag,
        @Param("sortParams") SortParams sortParams, @Param("keyword") String keyword);

    /**
     * 根据dataModelId和时间
     *
     * @param dataModelId 建模id
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param storageId   存储id
     * @return 统计
     */
    DataLineageCountVO selectByDataModelId(@Param("dataModelId") Integer dataModelId, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("storageId") Integer storageId);

}
