package com.trs.ai.moye.backstage.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.ai.moye.backstage.entity.Role;
import com.trs.ai.moye.backstage.entity.RoleOperation;
import com.trs.ai.moye.backstage.response.RoleSimpleResponse;
import com.trs.moye.base.common.request.SortParams;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 角色对数据库的操作接口
 *
 * <AUTHOR>
 * @since 2020/10/12 19:00
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 根据用户ID判断是否管理员
     *
     * @param userId 用户ID
     * @return 是否管理员
     */
    List<Integer> isAdmin(@Param("userId") Integer userId);

    /**
     * 通过用户id查询角色
     *
     * @param userId 用户id
     * @return {@link Role} 角色
     * <AUTHOR>
     * @since 2024/1/25 11:46
     */
    List<Role> selectRoleByUserId(@Param("userId") Integer userId);

    /**
     * 获取某个角色的模块权限信息
     *
     * @param roleIds 角色id
     * @return 模块名
     * <AUTHOR>
     * @since 2020/10/12 17:03
     */
    List<String> getUserModule(@Param("roleIds") List<Integer> roleIds);


    /**
     * 获取某个角色的操作权限信息
     *
     * @param roleIds 角色id
     * @return {@link RoleOperation} 权限列表
     * <AUTHOR>
     * @since 2024/9/19 13:44
     **/
    List<RoleOperation> getUserOperation(@Param("roleIds") List<Integer> roleIds);

    /**
     * id集合查询
     *
     * @param idCollection 角色id集合
     * @return 用户列表
     */
    List<Role> selectByIdCollection(@Param("idCollection") Collection<Integer> idCollection);

    /**
     * 根据角色名查询角色,排除自己
     *
     * @param name 角色名
     * @param id   主键id
     * @return 角色
     */
    Integer countByNameAndExcludeId(@Param("name") String name, @Param("id") Integer id);

    /**
     * 根据角色名，模糊检索角色；返回分页数据
     *
     * @param page      分页参数
     * @param roleName  角色名
     * @param sortParam 排序参数
     * @return 分页数据
     */
    Page<Role> selectPageByRoleName(Page<Role> page, @Param("roleName") String roleName,
        @Param("sortParam") SortParams sortParam);


    /**
     * 查询角色id和角色名
     *
     * @return 角色列表
     */
    List<RoleSimpleResponse> selectSimpleInfo();

    /**
     * 根据id查询角色
     *
     * @param id 角色id
     * @return 角色
     */
    Role selectByIdRole(@Param("id") Integer id);

    /**
     * 根据角色id查询角色
     *
     * @param roleIds 角色id
     * @return 角色列表
     */
    List<Role> selectByIds(@Param("ids") List<Integer> roleIds);


    /**
     * 查询所有的角色信息
     *
     * @return {@link Role}
     * <AUTHOR>
     * @since 2024/10/30 14:56
     */
    List<Role> selectAll();


    /**
     * 查询所有并转化成map
     *
     * @return {@link Map}
     * <AUTHOR>
     * @since 2024/11/4 17:08
     */
    @MapKey("id")
    Map<Integer, Role> selectAllAsMap();


}
