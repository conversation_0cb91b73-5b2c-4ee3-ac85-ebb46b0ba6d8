package com.trs.ai.moye.monitor.controller;

import com.trs.ai.moye.data.connection.response.DataConnectionStatisticsResponse;
import com.trs.ai.moye.monitor.request.DataConnectionBatchDetectionRequest;
import com.trs.ai.moye.monitor.request.DataConnectionHealthDetailRequest;
import com.trs.ai.moye.monitor.response.BatchDetectionResponse;
import com.trs.ai.moye.monitor.response.DataConnectionHealthDetailResponse;
import com.trs.ai.moye.monitor.response.DataConnectionHealthStatisticsResponse;
import com.trs.ai.moye.monitor.service.DataConnectionMonitorService;
import com.trs.moye.base.common.response.PageResponse;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据源监控controller
 *
 * <AUTHOR>
 * @since 2025/7/10
 */
@RestController
@RequestMapping("/monitor/data-connection")
public class DataConnectionMonitorController {

    @Resource
    private DataConnectionMonitorService dataConnectionMonitorService;

    /**
     * 统计信息
     * <a href="https://yapi-192.trscd.com.cn/project/5419/interface/api/168832">YApi</a>
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public DataConnectionStatisticsResponse statistics() {
        return dataConnectionMonitorService.getStatistics();
    }

    /**
     * 获取统计页面
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/168825">YApi</a>
     *
     * @return 统计页面
     */
    @GetMapping("/statistics-list")
    public List<DataConnectionHealthStatisticsResponse> statisticsList() {
        return dataConnectionMonitorService.statisticsList();
    }


    /**
     * 获取详情列表
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/168826">YApi</a>
     *
     * @param request 请求参数
     * @return 详情列表
     */
    @PostMapping("/detail-list")
    public PageResponse<DataConnectionHealthDetailResponse> detailList(@RequestBody DataConnectionHealthDetailRequest request) {
        return dataConnectionMonitorService.detailList(request).toNewPageResult(DataConnectionHealthDetailResponse::from);
    }

    /**
     * 批量检测
     * <a href="http://192.168.210.40:3001/project/5419/interface/api/168828">YApi</a>
     *
     * @param request 请求参数
     * @return BatchDetectionResponse
     */
    @PostMapping("/batch-detection")
    public BatchDetectionResponse batchDetection(@RequestBody @Validated DataConnectionBatchDetectionRequest request) {
        return dataConnectionMonitorService.batchDetection(request.getConnectionIds());
    }

}
