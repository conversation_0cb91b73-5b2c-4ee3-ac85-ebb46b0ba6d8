package com.trs.ai.moye.homepage.service.impl.collect.data.model;

import com.trs.ai.moye.homepage.entity.DashBoardData;
import com.trs.ai.moye.homepage.enums.HomePageShowData;
import com.trs.ai.moye.homepage.enums.StatType;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 主题库首页流水线数据收集工具
 *
 * <AUTHOR>
 * @since 2025/2/24
 **/
@Service
public class ThemeDataServiceCollector extends DataModelDataCollectService {

    @Resource
    private DataModelMapper dataModelMapper;

    @Override
    public List<DashBoardData> collectData() {
        Long count = dataModelMapper.selectCountCreateTableStatus(getLayer());
        return DashBoardData.buildData(HomePageShowData.THEME_LAYER, count,
            getTotal());
    }

    @Override
    public ModelLayer getLayer() {
        return ModelLayer.THEME;
    }

    @Override
    protected StatType getStatType() {
        return StatType.THEME_STORAGE;
    }
}

