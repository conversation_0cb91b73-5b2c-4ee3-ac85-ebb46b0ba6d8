package com.trs.ai.moye.homepage.service.impl.collect;

import com.trs.ai.moye.homepage.entity.AggregatedStat;
import com.trs.ai.moye.homepage.entity.DashBoardData;
import com.trs.ai.moye.homepage.enums.HomePageShowData;
import com.trs.ai.moye.homepage.service.DataCollectService;
import com.trs.moye.base.data.model.dao.BusinessCategoryMapper;
import com.trs.moye.base.data.source.dao.DataSourceConfigMapper;
import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 查询有贴源表的分类数量,来源表的总量
 *
 * <AUTHOR>
 * @since 2025/1/22
 **/

@Service
public class DataSourceCollector implements DataCollectService {

    @Resource
    private BusinessCategoryMapper businessCategoryMapper;

    @Resource
    private DataSourceConfigMapper dataSourceConfigMapper;

    @Override
    public List<DashBoardData> collectData() {
        Long businessCount = businessCategoryMapper.selectCount(null);
        Long total = dataSourceConfigMapper.selectCount(null);
        return DashBoardData.buildData(HomePageShowData.DATA_SOURCE, businessCount, total);
    }

    @Override
    public AggregatedStat getIncrementStat(LocalDateTime startTime, LocalDateTime endTime) {
        return new AggregatedStat();
    }
}
