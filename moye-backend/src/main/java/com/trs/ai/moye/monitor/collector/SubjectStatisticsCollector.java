package com.trs.ai.moye.monitor.collector;

import com.trs.ai.moye.data.model.dao.BatchTaskRecordMapper;
import com.trs.ai.moye.data.model.dao.StorageTaskMapper;
import com.trs.ai.moye.homepage.dao.HomePageSubjectStatisticsMapper;
import com.trs.ai.moye.homepage.entity.HomePageSubjectStatistics;
import com.trs.ai.moye.monitor.dao.DataProcessRecordMapper;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 专题库收集器
 *
 * <AUTHOR>
 * @since 2025/6/24
 */
@Service
public class SubjectStatisticsCollector extends AbstractStatisticsCollector<HomePageSubjectStatistics> {

    private final HomePageSubjectStatisticsMapper homePageSubjectStatisticsMapper;

    @Autowired
    public SubjectStatisticsCollector(DataModelMapper dataModelMapper,
        BatchTaskRecordMapper batchTaskRecordMapper,
        DataProcessRecordMapper dataProcessRecordMapper,
        StorageTaskMapper storageTaskMapper,
        HomePageSubjectStatisticsMapper homePageSubjectStatisticsMapper) {
        super(dataModelMapper, batchTaskRecordMapper, dataProcessRecordMapper, storageTaskMapper,
            ModelLayer.SUBJECT, 4);
        this.homePageSubjectStatisticsMapper = homePageSubjectStatisticsMapper;
    }

    @Override
    public List<HomePageSubjectStatistics> selectAllStatistics() {
        return homePageSubjectStatisticsMapper.selectAll(null, null, null, null);
    }

    @Override
    public List<HomePageSubjectStatistics> selectBatchStatistics(LocalDateTime start, LocalDateTime end) {
        return batchTaskRecordMapper.selectLastDayCountByModelIdSubject(start, end);
    }

    @Override
    public List<HomePageSubjectStatistics> selectBatchStorageStatistics(LocalDateTime start, LocalDateTime end) {
        return batchTaskRecordMapper.selectBatchStorageSubject(start, end);
    }

    @Override
    public List<HomePageSubjectStatistics> selectStreamStatistics(LocalDateTime start, LocalDateTime end,
        List<Integer> modelIds) {
        return dataProcessRecordMapper.selectLastDayCountByModelIdSubject(start, end, modelIds);
    }

    @Override
    public List<HomePageSubjectStatistics> selectStorageStatistics(LocalDateTime start, LocalDateTime end,
        int layerType) {
        return storageTaskMapper.selectLastDayCountByModelIdSubject(start, end, layerType);
    }

    @Override
    public void updateStatistics(List<HomePageSubjectStatistics> statistics) {
        statistics.forEach(homePageSubjectStatisticsMapper::updateData);
    }

    @Override
    public void insertNewStatistics(List<HomePageSubjectStatistics> newStatistics) {
        homePageSubjectStatisticsMapper.insert(newStatistics);
    }

    @Override
    public String getStatisticsTypeName() {
        return "专题库";
    }
}
