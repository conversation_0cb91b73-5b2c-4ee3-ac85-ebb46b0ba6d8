package com.trs.ai.moye.homepage.entity;

import com.trs.ai.moye.homepage.enums.StatType;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 聚合信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AggregatedStat {

    private Long total = 0L;
    private Long normal = 0L;
    private Long abnormal = 0L;

    private StatType statType;
    private LocalDateTime lastStatTime;


    /**
     * 从BaseStat构建AggregatedStat
     *
     * @param baseStat 基础数据
     * @param statType 统计数据类型
     * @param lastStatTime 开始时间
     * @return AggregatedStat
     */
    public static AggregatedStat from(BaseStat baseStat, StatType statType, LocalDateTime lastStatTime) {
        return new AggregatedStat()
            .setStatType(statType)
            .setLastStatTime(lastStatTime)
            .setTotal(baseStat.getTotal())
            .setNormal(baseStat.getNormal())
            .setAbnormal(baseStat.getAbnormal());
    }

}