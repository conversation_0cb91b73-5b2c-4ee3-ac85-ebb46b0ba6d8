package com.trs.ai.moye.homepage.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.ai.moye.homepage.entity.HomePageThemeStatistics;
import com.trs.ai.moye.homepage.enums.StatisticsDwdType;
import com.trs.ai.moye.monitor.response.statistics.ProcessSegmentationStatistics;
import com.trs.ai.moye.monitor.response.statistics.StreamTaskBarChart;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 主题库首页统计mapper
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
@DS("clickhouse")
@Mapper
public interface HomePageThemeStatisticsMapper extends BaseMapper<HomePageThemeStatistics> {

    /**
     * 查询统计表中所有数据
     *
     * @param type         统计表类型
     * @param dataModelIds 数据模型ID
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 统计表数据
     */
    List<HomePageThemeStatistics> selectAll(@Param("type") String type, @Param("dataModelIds") List<Integer> dataModelIds,
        @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询统计表中折线图数据
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param type         类型
     * @param dataModelIds 数据模型ID
     * @return 折线图数据
     */
    List<ProcessSegmentationStatistics> selectAllLineByDay(@Param("startTime") String startTime,
        @Param("endTime") String endTime, @Param("type") StatisticsDwdType type,
        @Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 查询统计表中折线图数据 (成功)
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param type         类型
     * @param dataModelIds 数据模型ID
     * @return 折线图数据
     */
    List<ProcessSegmentationStatistics> selectSuccessLineByDay(@Param("startTime") String startTime,
        @Param("endTime") String endTime, @Param("type") StatisticsDwdType type,
        @Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 查询统计表中折线图数据 (失败)
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param type         类型
     * @param dataModelIds 数据模型ID
     * @return 折线图数据
     */
    List<ProcessSegmentationStatistics> selectFailLineByDay(@Param("startTime") String startTime,
        @Param("endTime") String endTime, @Param("type") StatisticsDwdType type,
        @Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 查询统计表中折线图数据 (成功)
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param type         类型
     * @param dataModelIds 数据模型ID
     * @return 折线图数据
     */
    List<ProcessSegmentationStatistics> selectSuccessLineByHour(@Param("startTime") String startTime,
        @Param("endTime") String endTime, @Param("type") StatisticsDwdType type,
        @Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 查询统计表中折线图数据 (失败)
     *
     * @param type         类型
     * @param dataModelIds 数据模型ID
     * @return 折线图数据
     */
    List<ProcessSegmentationStatistics> selectBatchFailLineByHour(@Param("type") StatisticsDwdType type,
        @Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 查询统计表中折线图数据
     *
     * @param type         类型
     * @param dataModelIds 数据模型ID
     * @return 折线图数据
     */
    List<ProcessSegmentationStatistics> selectStreamLineByHour(@Param("type") StatisticsDwdType type,
        @Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 查询统计表中柱状图数据
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param type         类型
     * @param dataModelIds 数据模型ID
     * @param top          前N条数据
     * @return 折线图数据
     */
    List<StreamTaskBarChart> selectStreamBarChartByDay(@Param("startTime") String startTime,
        @Param("endTime") String endTime, @Param("type") StatisticsDwdType type,
        @Param("dataModelIds") List<Integer> dataModelIds, @Param("top") Integer top);

    /**
     * 查询统计表中柱状图数据
     *
     * @param type         类型
     * @param dataModelIds 数据模型ID
     * @param top          前N条数据
     * @return 折线图数据
     */
    List<StreamTaskBarChart> selectStreamBarChartByHour(@Param("type") StatisticsDwdType type,
        @Param("dataModelIds") List<Integer> dataModelIds, @Param("top") Integer top);

    /**
     * 获取存储总数
     *
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param type         类型
     * @param dataModelIds 建模id
     * @return 存储总数
     */
    Long selectTotalStorageByDay(@Param("startTime") String startTime,
        @Param("endTime") String endTime, @Param("type") StatisticsDwdType type,
        @Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 获取存储总数
     *
     * @param dataModelIds 建模ids
     * @return 存储总数
     */
    Long selectTotalStorageByHour(@Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 获取存储总数
     *
     * @param dataModelIds 建模id
     * @return 存储总数
     */
    List<ProcessSegmentationStatistics> selectStorageLineByHour(@Param("dataModelIds") List<Integer> dataModelIds);

    /**
     * 查询算子执行总数
     *
     * @return 算子执行总数
     */
    Long selectOperatorExecuteCount();

    /**
     * 获取最近一次同步的时间
     *
     * @return 最近一次同步的时间
     */
    LocalDateTime selectMaxSyncTime();

    /**
     * 批量更新数据
     *
     * @param originalData 原始数据
     */
    void updateData(@Param("originalData") HomePageThemeStatistics originalData);

    /**
     * 获取所有数据
     *
     * @return total
     */
    Long selectTotalCount();

    /**
     * 根据建模id和时间统计数量
     *
     * @param dataModelId       建模id
     * @param startTime         开始时间
     * @param endTime           结束时间
     * @param statisticsDwdType 类型
     * @param storageId        存储点id
     * @return 数量
     */
    long selectByDataModelId(@Param("dataModelId") Integer dataModelId, @Param("startTime") String startTime,
        @Param("endTime") String endTime, @Param("type") StatisticsDwdType statisticsDwdType, @Param("storageId") Integer storageId);

}
