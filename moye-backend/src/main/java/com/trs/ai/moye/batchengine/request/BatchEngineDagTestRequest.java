package com.trs.ai.moye.batchengine.request;

import com.trs.ai.moye.batchengine.entity.BatchEngineTaskParam;
import com.trs.moye.ability.entity.operator.BatchOperator;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * BatchEngineDagTestRequest
 *
 * <AUTHOR>
 * @since 2025/8/8 18:09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchEngineDagTestRequest {

    private BatchEngineTaskParam task;
    private List<BatchOperator> batchOperators;
}
