package com.trs.ai.moye.homepage.service.impl;

import com.trs.ai.moye.data.model.dao.StorageTaskMapper;
import com.trs.ai.moye.homepage.dao.DashBoardMapper;
import com.trs.ai.moye.homepage.dao.HomePageDwdStatisticsMapper;
import com.trs.ai.moye.homepage.dao.HomePageOdsScheduleStatisticsMapper;
import com.trs.ai.moye.homepage.dao.HomePageOdsStorageStatisticsMapper;
import com.trs.ai.moye.homepage.dao.HomePageSubjectStatisticsMapper;
import com.trs.ai.moye.homepage.dao.HomePageThemeStatisticsMapper;
import com.trs.ai.moye.homepage.entity.DashBoardData;
import com.trs.ai.moye.homepage.entity.HomePageOdsScheduleStatistics;
import com.trs.ai.moye.homepage.entity.HomePageOdsStorageStatistics;
import com.trs.ai.moye.homepage.enums.HomePageShowData;
import com.trs.ai.moye.homepage.service.HomePageService;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import com.trs.moye.base.data.model.entity.DataModel;
import com.trs.moye.base.data.storage.DataStorage;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Supplier;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * HomePageService实现类
 *
 * <AUTHOR>
 * @since 2025/1/17
 **/

@Service
@Slf4j
public class HomePageServiceImpl implements HomePageService {

    @Resource
    private DashBoardMapper dashBoardMapper;

    @Resource
    private HomePageDwdStatisticsMapper homePageDwdStatisticsMapper;

    @Resource
    private StorageTaskMapper storageTaskMapper;

    @Resource
    private DataModelMapper dataModelMapper;

    @Resource
    private HomePageOdsScheduleStatisticsMapper odsScheduleStatisticsMapper;

    @Resource
    private HomePageOdsStorageStatisticsMapper odsStorageStatisticsMapper;

    @Resource
    private HomePageThemeStatisticsMapper homePageThemeStatisticsMapper;

    @Resource
    private HomePageSubjectStatisticsMapper homePageSubjectStatisticsMapper;

    @Getter
    @AllArgsConstructor
    private static class LayerConfig {

        private final HomePageShowData homePageLayer;
        private final Supplier<Long> totalCountSupplier;
        private final ModelLayer modelLayer;
    }

    @Override
    public Map<String, Map<String, Long>> getHomePagePipelineData() {
        List<DashBoardData> dashBoardDataLast = dashBoardMapper.getDashBoardDataLast();
        Map<String, Map<String, Long>> res = new HashMap<>();
        dashBoardDataLast.forEach(dashBoardData -> {
            String category = dashBoardData.getCategory();
            String metric = dashBoardData.getMetric();
            Long value = dashBoardData.getValue();
            if (Arrays.asList(HomePageShowData.values()).contains(HomePageShowData.getByModuleName(category))) {
                res.computeIfAbsent(category, k -> new HashMap<>()).put(metric, value);
            }
        });

        // 定义层处理配置
        List<LayerConfig> layerConfigs = Arrays.asList(
            new LayerConfig(
                HomePageShowData.ODS_LAYER,
                () -> odsStorageStatisticsMapper.selectTotalCountToday().getTotal(),
                ModelLayer.ODS
            ),
            new LayerConfig(
                HomePageShowData.DWD_LAYER,
                () -> homePageDwdStatisticsMapper.selectTotalCount(),
                ModelLayer.DWD
            ),
            new LayerConfig(
                HomePageShowData.THEME_LAYER,
                () -> homePageThemeStatisticsMapper.selectTotalCount(),
                ModelLayer.THEME
            ),
            new LayerConfig(
                HomePageShowData.SUBJECT_LAYER,
                () -> homePageSubjectStatisticsMapper.selectTotalCount(),
                ModelLayer.SUBJECT
            )
        );

        for (LayerConfig config : layerConfigs) {
            Map<String, Long> layerMap = res.get(config.getHomePageLayer().getModuleName());
            layerMap.put("total", config.getTotalCountSupplier().get());
            long createTableCount = dataModelMapper.selectCreateTableSuccessCount(config.getModelLayer());
            layerMap.put("count", createTableCount);
            res.put(config.getHomePageLayer().getModuleName(), layerMap);
        }
        return res;
    }

    @Override
    public void collectOdsHomePageStorageStatistic() {
        //查询统计统计表中所有原始
        log.info("开始更新贴源库存储数据");
        List<HomePageOdsStorageStatistics> originalStorageData = odsStorageStatisticsMapper.selectAll(null, null, null,
            null);
        // 判断是否有原始数据
        if (Objects.isNull(originalStorageData) || originalStorageData.isEmpty()) {
            // 如果没有原始数据，则直接查询所有数据, 作为第一次同步
            insertAllOdsStorageStatistics();
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime localDateTime = now.withNano(0);

        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        // 如果有原始数据，则查询上次同步时间到当前时间的数据
        List<HomePageOdsStorageStatistics> storageStatistics = storageTaskMapper.selectOdsStatistics(
            startOfDay, localDateTime);
        if (Objects.nonNull(storageStatistics) && !storageStatistics.isEmpty()) {
            buildStorage(storageStatistics);
        }
        updateOdsStorageStatistics(originalStorageData, storageStatistics);
        originalStorageData.forEach(e ->
            {
                if (Objects.isNull(e.getStorageName())) {
                    e.setStorageName("-");
                }
                odsStorageStatisticsMapper.updateData(e);
            }
        );
        log.info("贴源库存储更新数据: {}", originalStorageData);
    }

    @Override
    public void collectOdsHomePageScheduleStatistic() {
        //查询调度统计表中所有原始
        log.info("开始更新贴源库调度数据");
        List<HomePageOdsScheduleStatistics> originalScheduleData = odsScheduleStatisticsMapper.selectAll(null, null,
            null, null);
        // 判断是否有原始数据
        if (Objects.isNull(originalScheduleData) || originalScheduleData.isEmpty()) {
            // 如果没有原始数据，则直接查询所有数据, 作为第一次同步
            insertAllOdsScheduleStatistics();
            return;
        }
        // 如果有原始数据，则查询上次同步时间到当前时间的数据
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime localDateTime = now.withNano(0);
        List<HomePageOdsScheduleStatistics> dispatchStatistics = storageTaskMapper.selectOdsDispatchStatistics(
            startOfDay, localDateTime);
        if (Objects.nonNull(dispatchStatistics) && !dispatchStatistics.isEmpty()) {
            buildDisPatch(dispatchStatistics);
        }
        updateOdsScheduleStatistics(originalScheduleData, dispatchStatistics);
        log.info("贴源库调度更新数据: {}", originalScheduleData);
        originalScheduleData.forEach(e -> odsScheduleStatisticsMapper.updateData(e));
    }


    private void updateOdsStorageStatistics(List<HomePageOdsStorageStatistics> originalStorageData,
        List<HomePageOdsStorageStatistics> newDataList) {
        if (newDataList.isEmpty()) {
            return;
        }

        Set<HomePageOdsStorageStatistics> newDataSet = new HashSet<>(newDataList);

        for (HomePageOdsStorageStatistics original : originalStorageData) {
            Iterator<HomePageOdsStorageStatistics> iterator = newDataSet.iterator();
            while (iterator.hasNext()) {
                HomePageOdsStorageStatistics newData = iterator.next();
                if (original.getDataModelId().equals(newData.getDataModelId())
                    && original.getTime().equals(newData.getTime()) && (Objects.nonNull(original.getStorageId())
                    && Objects.nonNull(newData.getStorageId()) && original.getStorageId()
                    .equals(newData.getStorageId()))) {
                    // 更新数据并移除已匹配的
                    original.updateByNew(newData);
                    iterator.remove();
                }
            }
        }
        log.info("贴源库存储新增数据: {}", newDataSet);
        for (HomePageOdsStorageStatistics homePageDwdStatistics : newDataSet) {
            odsStorageStatisticsMapper.insert(homePageDwdStatistics);
        }
    }

    private void insertAllOdsStorageStatistics() {
        List<HomePageOdsStorageStatistics> dispatchStatistics = storageTaskMapper.selectOdsStatistics(null, null);
        //给dataModelName和accessType赋值
        if (Objects.nonNull(dispatchStatistics) && !dispatchStatistics.isEmpty()) {
            buildStorage(dispatchStatistics);
            odsStorageStatisticsMapper.insert(dispatchStatistics);
        }
    }

    private void insertAllOdsScheduleStatistics() {
        //调度统计
        List<HomePageOdsScheduleStatistics> dispatchStatistics = storageTaskMapper.selectOdsDispatchStatistics(null,
            null);
        //给dataModelName和accessType赋值
        if (Objects.nonNull(dispatchStatistics) && !dispatchStatistics.isEmpty()) {
            buildDisPatch(dispatchStatistics);
            odsScheduleStatisticsMapper.insert(dispatchStatistics);
        }
    }

    private void buildDisPatch(List<HomePageOdsScheduleStatistics> dispatchStatistics) {
        List<Integer> odsIds = dispatchStatistics.stream().map(HomePageOdsScheduleStatistics::getDataModelId).toList();
        List<DataModel> dataModels = dataModelMapper.selectByIds(odsIds);
        for (DataModel dataModel : dataModels) {
            List<HomePageOdsScheduleStatistics> list = dispatchStatistics.stream()
                .filter(e -> Objects.equals(e.getDataModelId(), dataModel.getId())).toList();
            list.forEach(e -> {
                e.setDataModelName(dataModel.getZhName());
                if (Objects.nonNull(dataModel.getDataSource()) && !dataModel.getDataSource().isEmpty()) {
                    e.setAccessType(
                        dataModel.getDataSource().get(0).getConnection().getConnectionType().getCategory().getLabel());
                }
            });
        }
    }

    private void buildStorage(List<HomePageOdsStorageStatistics> storageStatistics) {
        List<Integer> odsIds = storageStatistics.stream().map(HomePageOdsStorageStatistics::getDataModelId).toList();
        List<DataModel> dataModels = dataModelMapper.selectByIdsWithStorage(odsIds);
        for (DataModel dataModel : dataModels) {
            List<HomePageOdsStorageStatistics> list = storageStatistics.stream()
                .filter(e -> Objects.equals(e.getDataModelId(), dataModel.getId())).toList();
            list.forEach(e -> {
                e.setDataModelName(dataModel.getZhName());
                List<DataStorage> storageList = dataModel.getDataStorages().stream()
                    .filter(storage -> storage.getId().equals(e.getStorageId())).toList();
                if (!storageList.isEmpty()) {
                    e.setStorageName(storageList.get(0).getConnection().getName());
                } else {
                    e.setStorageName("-");
                }
            });
        }
    }

    private void updateOdsScheduleStatistics(List<HomePageOdsScheduleStatistics> originalData,
        List<HomePageOdsScheduleStatistics> newDataList) {
        if (newDataList.isEmpty()) {
            return;
        }

        Set<HomePageOdsScheduleStatistics> newDataSet = new HashSet<>(newDataList);

        for (HomePageOdsScheduleStatistics original : originalData) {
            Iterator<HomePageOdsScheduleStatistics> iterator = newDataSet.iterator();
            while (iterator.hasNext()) {
                HomePageOdsScheduleStatistics newData = iterator.next();
                if (original.getDataModelId().equals(newData.getDataModelId())
                    && original.getTime().equals(newData.getTime())) {
                    // 更新数据并移除已匹配的
                    original.updateByNew(newData);
                    iterator.remove();
                }
            }
        }
        log.info("贴源库调度新增数据: {}", newDataSet);
        for (HomePageOdsScheduleStatistics homePageOdsStatistics : newDataSet) {
            odsScheduleStatisticsMapper.insert(homePageOdsStatistics);
        }
    }
}
