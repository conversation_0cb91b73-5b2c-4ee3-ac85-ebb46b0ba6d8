package com.trs.ai.moye.monitor.controller;

import com.trs.ai.moye.monitor.feign.MonitorCenterService;
import com.trs.moye.base.monitor.entity.flowchart.FlowChartMetrics;
import com.trs.moye.base.monitor.entity.realtime.RealtimeMonitorMetric;
import com.trs.moye.base.monitor.request.TrendChartRequest;
import com.trs.moye.base.monitor.response.TrendChartResponse;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 消息中心
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/monitor/stream-process")
@RestController
public class StreamProcessMonitorController {

    @Resource
    private MonitorCenterService monitorCenterService;

    /**
     * 查询趋势图数据
     *
     * @param request 请求参数
     * @return 趋势图数据
     */
    @PostMapping("/trend-chart")
    public TrendChartResponse selectTrendChart(@RequestBody TrendChartRequest request) {
        return monitorCenterService.selectTrendChart(request);
    }

    /**
     * 查询实时监控数据
     *
     * @param dataModelId 数据建模id
     * @param storageId   存储id
     * @return 实时监控数据
     */
    @GetMapping("/realtime-monitor")
    public RealtimeMonitorMetric collectRealtimeMonitor(@RequestParam Integer dataModelId,
        @RequestParam Integer storageId) {
        return monitorCenterService.collectRealtimeMonitor(dataModelId, storageId);
    }

    /**
     * 采集流程图指标
     *
     * @param dataModelId 数据建模id
     * @return 实时监控数据
     */
    @GetMapping("/flow-chart-metric")
    public FlowChartMetrics collectFlowChartMetric(@RequestParam Integer dataModelId){
        return monitorCenterService.collectFlowChartMetric(dataModelId);
    }
}
