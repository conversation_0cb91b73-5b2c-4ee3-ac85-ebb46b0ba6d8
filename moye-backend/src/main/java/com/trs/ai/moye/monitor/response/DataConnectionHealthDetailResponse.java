package com.trs.ai.moye.monitor.response;

import com.trs.ai.moye.monitor.entity.DataConnectionHealthRecord;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * DataConnectionHealthRecord
 *
 * <AUTHOR>
 * @since 2025/7/9 18:14
 */
@Data
public class DataConnectionHealthDetailResponse {

    /**
     * 连接id
     */
    private Integer connectionId;

    /**
     * 是否异常
     */
    private Boolean isError;

    /**
     * 异常信息
     */
    private String errorMessage;

    /**
     * 检测时间
     */
    private LocalDateTime detectionTime;

    /**
     * 检测方式
     */
    private String detectionType;

    /**
     * 检测人员
     */
    private String detectionUserName;

    /**
     * 从DataConnectionHealthRecord转换为DataConnectionHealthDetailResponse
     *
     * @param dataConnectionHealthRecord DataConnectionHealthRecord
     * @return DataConnectionHealthDetailResponse
     */
    public static DataConnectionHealthDetailResponse from(DataConnectionHealthRecord dataConnectionHealthRecord) {
        DataConnectionHealthDetailResponse detailResponse = new DataConnectionHealthDetailResponse();
        detailResponse.setConnectionId(dataConnectionHealthRecord.getConnectionId());
        detailResponse.setDetectionType(dataConnectionHealthRecord.getDetectionType());
        detailResponse.setIsError(dataConnectionHealthRecord.isError());
        detailResponse.setErrorMessage(dataConnectionHealthRecord.getErrorMessage());
        detailResponse.setDetectionTime(dataConnectionHealthRecord.getDetectionTime());
        detailResponse.setDetectionUserName(dataConnectionHealthRecord.getDetectionUserName());
        return detailResponse;
    }
}
