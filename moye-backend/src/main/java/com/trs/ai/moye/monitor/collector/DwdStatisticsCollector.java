package com.trs.ai.moye.monitor.collector;

import com.trs.ai.moye.data.model.dao.BatchTaskRecordMapper;
import com.trs.ai.moye.data.model.dao.StorageTaskMapper;
import com.trs.ai.moye.homepage.dao.HomePageDwdStatisticsMapper;
import com.trs.ai.moye.homepage.entity.HomePageDwdStatistics;
import com.trs.ai.moye.monitor.dao.DataProcessRecordMapper;
import com.trs.moye.base.common.enums.ModelLayer;
import com.trs.moye.base.data.model.dao.DataModelMapper;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 要素库收集器
 *
 * <AUTHOR>
 * @since 2025/6/24
 */
@Service
public class DwdStatisticsCollector extends AbstractStatisticsCollector<HomePageDwdStatistics> {

    private final HomePageDwdStatisticsMapper homePageDwdStatisticsMapper;

    @Autowired
    public DwdStatisticsCollector(DataModelMapper dataModelMapper,
        BatchTaskRecordMapper batchTaskRecordMapper,
        DataProcessRecordMapper dataProcessRecordMapper,
        StorageTaskMapper storageTaskMapper,
        HomePageDwdStatisticsMapper homePageDwdStatisticsMapper) {
        super(dataModelMapper, batchTaskRecordMapper, dataProcessRecordMapper,
            storageTaskMapper, ModelLayer.DWD, 2);
        this.homePageDwdStatisticsMapper = homePageDwdStatisticsMapper;
    }

    @Override
    public List<HomePageDwdStatistics> selectAllStatistics() {
        return homePageDwdStatisticsMapper.selectAll(null, null, null, null);
    }

    @Override
    public List<HomePageDwdStatistics> selectBatchStatistics(LocalDateTime start, LocalDateTime end) {
        return batchTaskRecordMapper.selectLastDayCountByModelId(start, end);
    }

    @Override
    public List<HomePageDwdStatistics> selectBatchStorageStatistics(LocalDateTime start, LocalDateTime end) {
        return batchTaskRecordMapper.selectBatchStorage(start, end);
    }

    @Override
    public List<HomePageDwdStatistics> selectStreamStatistics(LocalDateTime start, LocalDateTime end,
        List<Integer> modelIds) {
        return dataProcessRecordMapper.selectLastDayCountByModelId(start, end, modelIds);
    }

    @Override
    public List<HomePageDwdStatistics> selectStorageStatistics(LocalDateTime start, LocalDateTime end, int layerType) {
        return storageTaskMapper.selectLastDayCountByModelId(start, end, layerType);
    }

    @Override
    public void updateStatistics(List<HomePageDwdStatistics> statistics) {
        statistics.forEach(homePageDwdStatisticsMapper::updateData);
    }

    @Override
    public void insertNewStatistics(List<HomePageDwdStatistics> newStatistics) {
        homePageDwdStatisticsMapper.insert(newStatistics);
    }

    @Override
    public String getStatisticsTypeName() {
        return "要素库";
    }
}
