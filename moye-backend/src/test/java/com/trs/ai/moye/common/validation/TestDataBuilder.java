package com.trs.ai.moye.common.validation;

import com.trs.ai.moye.data.service.entity.DataServiceField;
import com.trs.ai.moye.data.service.entity.query.Condition;
import com.trs.ai.moye.data.service.entity.query.ValueObject;
import com.trs.ai.moye.data.service.enums.DataServiceConditionType;
import com.trs.ai.moye.data.service.enums.LogicLinkOperator;
import com.trs.moye.base.common.enums.FieldType;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 测试数据构建器
 */
public class TestDataBuilder {

    public static Condition createExpression(String key, String operator, String... values) {
        Condition condition = new Condition();
        condition.setType(DataServiceConditionType.EXPRESSION);
        condition.setKey(createField(key));
        condition.setOperator(operator);
        if (values != null && values.length > 0) {
            condition.setValues(Arrays.stream(values)
                    .map(v -> {
                        ValueObject vo = new ValueObject();
                        vo.setValue(v);
                        return vo;
                    })
                    .toList());
        } else {
            condition.setValues(Collections.emptyList());
        }
        return condition;
    }

    public static Condition createLogic(String operator) {
        Condition condition = new Condition();
        condition.setType(DataServiceConditionType.LOGIC);
        condition.setOperator(operator);
        return condition;
    }

    public static Condition createVectorField() {
        Condition condition = new Condition();
        condition.setType(DataServiceConditionType.EXPRESSION);
        condition.setKey(createVectorField("vector_field"));
        condition.setOperator("=");
        condition.setValues(Collections.emptyList());
        return condition;
    }

    private static DataServiceField createField(String name) {
        DataServiceField field = new DataServiceField();
        field.setEnName(name);
        field.setZhName(name);
        field.setType(FieldType.STRING);
        field.setTypeName("字符串");
        return field;
    }

    private static DataServiceField createVectorField(String name) {
        DataServiceField field = new DataServiceField();
        field.setEnName(name);
        field.setZhName(name);
        field.setType(FieldType.FLOAT_VECTOR);
        field.setTypeName("浮点向量");
        return field;
    }

    public static Condition createInvalidCondition() {
        return new Condition();
    }

    public static Condition createNullOperatorCondition() {
        Condition condition = new Condition();
        condition.setType(DataServiceConditionType.EXPRESSION);
        condition.setOperator(null);
        return condition;
    }
}
