package com.trs.ai.moye.common.validation;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 主校验器集成测试
 */
@DisplayName("条件列表校验器集成测试")
class ConditionListValidatorTest {

    private ConditionListValidator validator;

    @Mock
    private ConstraintValidatorContext context;

    @Mock
    private ConstraintValidatorContext.ConstraintViolationBuilder violationBuilder;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        validator = new ConditionListValidator();

        when(context.buildConstraintViolationWithTemplate(anyString()))
                .thenReturn(violationBuilder);
        when(violationBuilder.addConstraintViolation())
                .thenReturn(context);
    }

    @Test
    @DisplayName("空列表应该通过校验")
    void testEmptyList() {
        assertTrue(validator.isValid(null, context));
        assertTrue(validator.isValid(Collections.emptyList(), context));
        verifyNoInteractions(context);
    }

    @Test
    @DisplayName("只有向量字段应该通过校验")
    void testOnlyVectorField() {
        var conditions = List.of(TestDataBuilder.createVectorField());
        assertTrue(validator.isValid(conditions, context));
        verifyNoInteractions(context);
    }

    @Test
    @DisplayName("简单有效表达式应该通过校验")
    void testSimpleValidExpression() {
        var conditions = List.of(
                TestDataBuilder.createExpression("name", "=", "test")
        );
        assertTrue(validator.isValid(conditions, context));
        verifyNoInteractions(context);
    }

    @Test
    @DisplayName("复杂有效表达式应该通过校验")
    void testComplexValidExpression() {
        var conditions = Arrays.asList(
                TestDataBuilder.createExpression("name", "=", "test"),
                TestDataBuilder.createLogic("and"),
                TestDataBuilder.createExpression("age", ">", "18")
        );
        assertTrue(validator.isValid(conditions, context));
        verifyNoInteractions(context);
    }

    @Test
    @DisplayName("带括号的有效表达式应该通过校验")
    void testValidExpressionWithBrackets() {
        var conditions = Arrays.asList(
                TestDataBuilder.createLogic("("),
                TestDataBuilder.createExpression("name", "=", "test"),
                TestDataBuilder.createLogic("or"),
                TestDataBuilder.createExpression("name", "=", "demo"),
                TestDataBuilder.createLogic(")"),
                TestDataBuilder.createLogic("and"),
                TestDataBuilder.createExpression("age", ">", "18")
        );
        assertTrue(validator.isValid(conditions, context));
        verifyNoInteractions(context);
    }

    @Test
    @DisplayName("括号不配对应该失败")
    void testUnmatchedBrackets() {
        var conditions = Arrays.asList(
                TestDataBuilder.createLogic("("),
                TestDataBuilder.createExpression("name", "=", "test")
        );
        assertFalse(validator.isValid(conditions, context));
        verify(context).buildConstraintViolationWithTemplate(anyString());
    }

    @Test
    @DisplayName("连续逻辑符号应该失败")
    void testConsecutiveLogicOperators() {
        var conditions = Arrays.asList(
                TestDataBuilder.createExpression("name", "=", "test"),
                TestDataBuilder.createLogic("and"),
                TestDataBuilder.createLogic("or"),
                TestDataBuilder.createExpression("age", ">", "18")
        );
        assertFalse(validator.isValid(conditions, context));
        verify(context).buildConstraintViolationWithTemplate(anyString());
    }

    @Test
    @DisplayName("以逻辑符号开头应该失败")
    void testStartWithLogicOperator() {
        var conditions = Arrays.asList(
                TestDataBuilder.createLogic("and"),
                TestDataBuilder.createExpression("name", "=", "test")
        );
        assertFalse(validator.isValid(conditions, context));
        verify(context).buildConstraintViolationWithTemplate(anyString());
    }

    @Test
    @DisplayName("以逻辑符号结尾应该失败")
    void testEndWithLogicOperator() {
        var conditions = Arrays.asList(
                TestDataBuilder.createExpression("name", "=", "test"),
                TestDataBuilder.createLogic("and")
        );
        assertFalse(validator.isValid(conditions, context));
        verify(context).buildConstraintViolationWithTemplate(anyString());
    }

    @Test
    @DisplayName("表达式缺少key应该失败")
    void testExpressionMissingKey() {
        var condition = TestDataBuilder.createExpression("name", "=", "test");
        condition.setKey(null);
        var conditions = List.of(condition);

        assertFalse(validator.isValid(conditions, context));
        verify(context).buildConstraintViolationWithTemplate(anyString());
    }

    @Test
    @DisplayName("表达式缺少value应该失败")
    void testExpressionMissingValue() {
        var conditions = List.of(
                TestDataBuilder.createExpression("name", "=")
        );
        assertFalse(validator.isValid(conditions, context));
        verify(context).buildConstraintViolationWithTemplate(anyString());
    }

    @Test
    @DisplayName("连续表达式应该失败")
    void testConsecutiveExpressions() {
        var conditions = Arrays.asList(
                TestDataBuilder.createExpression("name", "=", "test"),
                TestDataBuilder.createExpression("age", ">", "18")
        );
        assertFalse(validator.isValid(conditions, context));
        verify(context).buildConstraintViolationWithTemplate(anyString());
    }

    @Test
    @DisplayName("not后面没有表达式应该失败")
    void testNotWithoutExpression() {
        var conditions = List.of(
                TestDataBuilder.createLogic("not")
        );
        assertFalse(validator.isValid(conditions, context));
        verify(context).buildConstraintViolationWithTemplate(anyString());
    }

    @Test
    @DisplayName("正确的not使用应该通过")
    void testValidNotUsage() {
        var conditions = Arrays.asList(
                TestDataBuilder.createLogic("not"),
                TestDataBuilder.createExpression("name", "=", "test")
        );
        assertTrue(validator.isValid(conditions, context));
        verifyNoInteractions(context);
    }

    @Test
    @DisplayName("isnull操作符不需要value应该通过")
    void testIsNullOperator() {
        var conditions = List.of(
                TestDataBuilder.createExpression("name", "isnull")
        );
        assertTrue(validator.isValid(conditions, context));
        verifyNoInteractions(context);
    }

    @Test
    @DisplayName("isnotnull操作符不需要value应该通过")
    void testIsNotNullOperator() {
        var conditions = List.of(
                TestDataBuilder.createExpression("name", "isnotnull")
        );
        assertTrue(validator.isValid(conditions, context));
        verifyNoInteractions(context);
    }

    @Test
    @DisplayName("空括号应该失败")
    void testEmptyBrackets() {
        var conditions = Arrays.asList(
                TestDataBuilder.createLogic("("),
                TestDataBuilder.createLogic(")")
        );
        assertFalse(validator.isValid(conditions, context));
        verify(context).buildConstraintViolationWithTemplate(anyString());
    }

    @Test
    @DisplayName("向量字段后跟普通表达式应该通过")
    void testVectorFieldWithNormalExpression() {
        var conditions = Arrays.asList(
                TestDataBuilder.createVectorField(),
                TestDataBuilder.createExpression("name", "=", "test")
        );
        assertTrue(validator.isValid(conditions, context));
        verifyNoInteractions(context);
    }
}
